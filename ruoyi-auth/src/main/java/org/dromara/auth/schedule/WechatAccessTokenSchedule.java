package org.dromara.auth.schedule;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.WxAccessToken;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;

@Slf4j
@Component
@EnableScheduling
public class WechatAccessTokenSchedule {

    @Value("${wechat.appId}")
    private String appId;

    @Value("${wechat.appSecret}")
    private String appSecret;


    public WxAccessToken getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
        String loginJson = null;
        try {
            loginJson = HttpUtil.createGet(url).execute().body();
        } catch (Exception e) {
            log.error("微信获取accessToken失败");
        }
        return JsonUtils.parseObject(loginJson, WxAccessToken.class);
    }

    @Scheduled(fixedRate = 6900000) // 提前5分钟刷新，2小时=7200秒，7200-300=6900秒=6900000毫秒
    public void refreshAndStoreAccessToken() {
        WxAccessToken accessToken = getAccessToken();
        if (accessToken != null && StringUtils.hasText(accessToken.getAccess_token())) {
            try {
                // 存储到Redis，设置2小时过期
                RedisUtils.setCacheObject("wechat_access_token", accessToken.getAccess_token(), Duration.ofSeconds(7200));
                log.info("微信accessToken已更新并存储到Redis，有效期2小时");
            } catch (Exception e) {
                log.error("存储微信accessToken到Redis失败", e);
            }
        } else {
            log.error("获取微信accessToken失败，无法更新");
        }
    }

}
