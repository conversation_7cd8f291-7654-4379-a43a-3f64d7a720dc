package org.dromara.common.excel.annotation;

import org.dromara.common.core.utils.StringUtils;

import java.lang.annotation.*;

/**
 * 服务方法格式化
 * 用于从服务方法中获取Excel下拉框选项
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface ExcelServiceFormat {

    /**
     * 服务类的全限定名 (如: org.dromara.system.service.SysUserService)
     */
    String serviceClass() default "";

    /**
     * 服务方法名称 (如: getDropDownOptions)
     */
    String methodName() default "";

    /**
     * 方法参数类型数组，按照方法参数顺序排列
     * 例如：{String.class, Integer.class}
     */
    Class<?>[] parameterTypes() default {};

    /**
     * 方法参数值数组，按照方法参数顺序排列
     * 例如：{"param1", "2"}
     * 注意：基本类型会自动转换
     */
    String[] parameterValues() default {};
    
    /**
     * 获取键值对的方法名称 (用于转换器)
     * 该方法应返回Map<String, String>类型，其中key为实际值，value为显示值
     */
    String mapMethodName() default "";
    
    /**
     * 获取键值对方法的参数类型数组
     */
    Class<?>[] mapParameterTypes() default {};
    
    /**
     * 获取键值对方法的参数值数组
     */
    String[] mapParameterValues() default {};
    
    /**
     * 分隔符，读取字符串组内容
     */
    String separator() default StringUtils.SEPARATOR;
}