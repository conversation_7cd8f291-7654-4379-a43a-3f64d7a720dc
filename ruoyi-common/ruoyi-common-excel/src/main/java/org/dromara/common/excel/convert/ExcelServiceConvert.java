package org.dromara.common.excel.convert;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.annotation.ExcelServiceFormat;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务方法格式化转换处理
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelServiceConvert implements Converter<Object> {

    @Override
    public Class<Object> supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        ExcelServiceFormat anno = getAnnotation(contentProperty.getField());
        String label = cellData.getStringValue();
        
        // 获取键值对映射
        Map<String, String> valueToLabelMap = getValueLabelMap(anno);
        
        // 反转映射，从标签找到值
        Map<String, String> labelToValueMap = new HashMap<>();
        for (Map.Entry<String, String> entry : valueToLabelMap.entrySet()) {
            labelToValueMap.put(entry.getValue(), entry.getKey());
        }
        
        // 根据标签获取值
        String value = labelToValueMap.get(label);
        if (StringUtils.isBlank(value)) {
            log.warn("无法找到标签 '{}' 对应的值", label);
            return null;
        }
        
        return Convert.convert(contentProperty.getField().getType(), value);
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (ObjectUtil.isNull(object)) {
            return new WriteCellData<>("");
        }
        
        ExcelServiceFormat anno = getAnnotation(contentProperty.getField());
        String value = Convert.toStr(object);
        
        // 获取键值对映射
        Map<String, String> valueToLabelMap = getValueLabelMap(anno);
        
        // 根据值获取标签
        String label = valueToLabelMap.get(value);
        if (StringUtils.isBlank(label)) {
            log.warn("无法找到值 '{}' 对应的标签", value);
            return new WriteCellData<>(value);
        }
        
        return new WriteCellData<>(label);
    }

    /**
     * 获取键值对映射
     * 
     * @param anno ExcelServiceFormat注解
     * @return 键值对映射，key为实际值，value为显示值
     */
    private Map<String, String> getValueLabelMap(ExcelServiceFormat anno) {
        String serviceClassName = anno.serviceClass();
        String mapMethodName = anno.mapMethodName();
        
        if (StringUtils.isBlank(serviceClassName) || StringUtils.isBlank(mapMethodName)) {
            log.warn("服务类名或映射方法名为空");
            return new HashMap<>();
        }
        
        try {
            // 获取服务类
            Class<?> serviceClass = Class.forName(serviceClassName);
            // 获取服务实例
            Object serviceInstance = SpringUtils.getBean(serviceClass);
            
            // 获取方法参数类型和参数值
            Class<?>[] parameterTypes = anno.mapParameterTypes();
            String[] parameterValues = anno.mapParameterValues();
            
            // 获取方法
            Method method;
            Object result;
            
            if (parameterTypes.length > 0) {
                // 有参数的方法
                method = serviceClass.getMethod(mapMethodName, parameterTypes);
                
                // 准备方法参数
                Object[] args = new Object[parameterTypes.length];
                for (int i = 0; i < parameterTypes.length; i++) {
                    if (i < parameterValues.length) {
                        // 转换参数值为对应类型
                        args[i] = convertValue(parameterValues[i], parameterTypes[i]);
                    } else {
                        args[i] = null;
                    }
                }
                
                // 调用方法
                result = method.invoke(serviceInstance, args);
            } else {
                // 无参数的方法
                method = serviceClass.getMethod(mapMethodName);
                result = method.invoke(serviceInstance);
            }
            
            // 处理返回结果
            if (result instanceof Map) {
                // 如果返回的是Map类型
                return (Map<String, String>) result;
            } else {
                log.warn("方法 {}.{} 返回类型不是Map<String, String>", serviceClassName, mapMethodName);
                return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("调用服务方法获取键值对映射失败", e);
            throw new ServiceException(String.format("调用服务方法 %s.%s 获取键值对映射失败", serviceClassName, mapMethodName));
        }
    }
    
    /**
     * 转换参数值为对应类型
     */
    private Object convertValue(String value, Class<?> targetType) {
        if (targetType == String.class) {
            return value;
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.parseInt(value);
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.parseLong(value);
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.parseBoolean(value);
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.parseDouble(value);
        } else if (targetType == Float.class || targetType == float.class) {
            return Float.parseFloat(value);
        } else {
            // 对于其他类型，尝试使用Convert工具类转换
            return Convert.convert(targetType, value);
        }
    }

    private ExcelServiceFormat getAnnotation(Field field) {
        return AnnotationUtil.getAnnotation(field, ExcelServiceFormat.class);
    }
}