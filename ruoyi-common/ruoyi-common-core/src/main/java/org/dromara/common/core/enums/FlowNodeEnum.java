package org.dromara.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程节点枚举
 */

@Getter
@AllArgsConstructor
public enum FlowNodeEnum {

    BASIC_INFORMATION_INPUT("basic-information-input", "用户信息录入"),

    WATER_METER_INSTALLATION("water-meter-installation", "水表安装"),

    WATER_PRICE_SETTING("water-price-setting", "水价设置");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 判断是否为有效的枚举code
     *
     * @param code 待检查的code
     * @return 是否为有效的枚举code
     */
    public static boolean isValidCode(String code) {
        if (code == null) {
            return false;
        }

        for (FlowNodeEnum node : FlowNodeEnum.values()) {
            if (node.getCode().equals(code)) {
                return true;
            }
        }

        return false;
    }
}
