# 定时任务性能指标输出示例

## 概述

本文档展示了优化后的抄表定时任务执行时的详细性能指标输出效果。系统会将所有性能报告写入文件中，而不是输出到控制台，包括表格形式的详细报告、性能分析和JSON格式的结构化数据。

## 文件输出结构

```
logs/performance-reports/
├── performance-report_2024-01-15_14-30-25.txt    # 详细性能报告
├── performance-report_2024-01-15_15-00-18.txt
├── daily/
│   ├── daily-summary_2024-01-15.txt              # 每日汇总报告
│   └── daily-summary_2024-01-16.txt
└── json/
    ├── metrics_2024-01-15_14-30-25.json          # JSON格式性能指标
    └── metrics_2024-01-15_15-00-18.json
```

## 1. 详细性能指标表格

```
====================================================================================================
                              定时任务执行性能指标报告
====================================================================================================
┌─────────────────────────────────┬─────────────────────────────────────────────┐
│ 指标项                          │ 数值                                        │
├─────────────────────────────────┼─────────────────────────────────────────────┤
│ 本次执行总耗时                  │ 125,340 ms (125.34 秒)                     │
│ 平均任务耗时                    │ 25,068.00 ms                               │
│ 处理任务数量                    │ 5 个                                        │
│ 任务成功数量                    │ 5 个                                        │
│ 任务失败数量                    │ 0 个                                        │
│ 任务成功率                      │ 100.00%                                     │
│ 当前活跃任务数                  │ 0 个                                        │
├─────────────────────────────────┼─────────────────────────────────────────────┤
│ 处理水表总数                    │ 12,500 个                                   │
│ 水表处理速度                    │ 99.73 个/秒                                 │
│ 平均每任务处理水表数            │ 2,500 个                                    │
│ 平均每表处理时间                │ 10.03 ms                                    │
├─────────────────────────────────┼─────────────────────────────────────────────┤
│ 累计生成账单数                  │ 12,450 个                                   │
│ 累计审核账单数                  │ 12,430 个                                   │
│ 账单审核率                      │ 99.84%                                      │
│ 处理支付数量                    │ 8,200 个                                    │
│ 支付成功数量                    │ 8,150 个                                    │
│ 支付失败数量                    │ 50 个                                       │
│ 支付成功率                      │ 99.39%                                      │
├─────────────────────────────────┼─────────────────────────────────────────────┤
│ 系统累计执行任务数              │ 156 个                                      │
│ 系统累计处理水表数              │ 385,600 个                                  │
│ 系统整体成功率                  │ 98.72%                                      │
│ 系统平均任务耗时                │ 22,450 ms                                   │
│ 系统平均每表处理时间            │ 8.95 ms                                     │
└─────────────────────────────────┴─────────────────────────────────────────────┘
```

## 2. 性能分析报告

```
📊 性能分析报告:
   等级: EXCELLENT (得分: 94.2/100)
   ✅ 优势:
      • 任务成功率极高(100.0%)
      • 支付成功率很高(99.4%)
      • 系统整体稳定性良好
   ⚠️  劣势:
      • 处理速度较慢(99.7个/秒)
   💡 建议:
      • 考虑增加并发数或优化批处理大小
   📈 趋势: IMPROVING (最近性能持续改善)
```

## 3. JSON格式性能指标

```json
{
  "timestamp": "2024-01-15T14:30:25.123",
  "healthStatus": "healthy",
  "performanceSummary": "执行耗时: 125.34秒, 任务成功率: 100.0%, 处理速度: 99.7个/秒, 支付成功率: 99.4%",
  "taskStats": {
    "totalTasks": 5,
    "successRate": 100.00
  },
  "meterStats": {
    "totalProcessed": 12500,
    "processingSpeed": 99.73
  },
  "billingStats": {
    "billsGenerated": 12450,
    "paymentSuccessRate": 99.39
  }
}
```

## 4. 不同性能等级的示例

### 4.1 优秀级别 (EXCELLENT - 90-100分)

```
📊 性能分析报告:
   等级: EXCELLENT (得分: 96.8/100)
   ✅ 优势:
      • 任务成功率极高(99.8%)
      • 处理速度很快(245.6个/秒)
      • 支付成功率很高(98.9%)
      • 系统整体稳定性良好
   💡 建议:
      • 系统运行良好，无需特别优化
   📈 趋势: STABLE (性能表现稳定)
```

### 4.2 良好级别 (GOOD - 75-89分)

```
📊 性能分析报告:
   等级: GOOD (得分: 82.5/100)
   ✅ 优势:
      • 任务成功率极高(98.2%)
      • 系统整体稳定性良好
   ⚠️  劣势:
      • 处理速度较慢(65.3个/秒)
   💡 建议:
      • 考虑增加并发数或优化批处理大小
   📈 趋势: STABLE (性能表现稳定)
```

### 4.3 正常级别 (NORMAL - 60-74分)

```
📊 性能分析报告:
   等级: NORMAL (得分: 68.3/100)
   ✅ 优势:
      • 系统整体稳定性良好
   ⚠️  劣势:
      • 任务成功率偏低(92.5%)
      • 处理速度较慢(45.8个/秒)
   💡 建议:
      • 检查错误日志，优化异常处理机制
      • 考虑增加并发数或优化批处理大小
   📈 趋势: DECLINING (最近性能有所下降，建议关注)
```

### 4.4 较差级别 (POOR - 0-59分)

```
📊 性能分析报告:
   等级: POOR (得分: 45.2/100)
   ⚠️  劣势:
      • 任务成功率偏低(85.3%)
      • 处理速度较慢(28.9个/秒)
      • 支付成功率偏低(78.5%)
      • 系统负载较高
   💡 建议:
      • 检查错误日志，优化异常处理机制
      • 考虑增加并发数或优化批处理大小
      • 检查客户余额状态和支付接口稳定性
      • 优化账单审核逻辑，提高审核成功率
   📈 趋势: DECLINING (最近性能有所下降，建议关注)
```

## 5. 监控告警示例

### 5.1 健康状态 (healthy)

```json
{
  "timestamp": "2024-01-15T14:30:25.123",
  "healthStatus": "healthy",
  "performanceScore": 94.2,
  "successRate": 100.0,
  "paymentSuccessRate": 99.4,
  "processingSpeed": 99.73,
  "activeTasks": 0,
  "alerts": []
}
```

### 5.2 警告状态 (warning)

```json
{
  "timestamp": "2024-01-15T14:30:25.123",
  "healthStatus": "warning",
  "performanceScore": 68.3,
  "successRate": 92.5,
  "paymentSuccessRate": 85.2,
  "processingSpeed": 45.8,
  "activeTasks": 8,
  "alerts": [
    "Low success rate: 92.50%",
    "Low payment success rate: 85.20%"
  ]
}
```

### 5.3 严重状态 (critical)

```json
{
  "timestamp": "2024-01-15T14:30:25.123",
  "healthStatus": "critical",
  "performanceScore": 45.2,
  "successRate": 85.3,
  "paymentSuccessRate": 78.5,
  "processingSpeed": 28.9,
  "activeTasks": 15,
  "alerts": [
    "Low success rate: 85.30%",
    "Low payment success rate: 78.50%",
    "High system load: 15 active tasks"
  ]
}
```

## 6. 文件管理功能

### 6.1 自动文件清理

系统每天凌晨2点自动执行文件清理任务：
- 删除超过30天的过期文件
- 压缩超过7天的旧文件（.gz格式）
- 记录清理统计信息

### 6.2 文件管理配置

```yaml
waterfee:
  meter-reading:
    performance:
      reports:
        enable-file-output: true          # 启用文件输出
        base-directory: "logs/performance-reports"  # 报告文件根目录
        retention-days: 30                # 文件保留天数
        enable-compression: true          # 启用文件压缩
        compression-after-days: 7         # 压缩文件的天数阈值
        max-file-size-mb: 10             # 单个报告文件最大大小
```

## 7. REST API 接口示例

### 7.1 获取性能报告

```bash
GET /scheduler/performance
```

响应：
```json
{
  "totalTasksExecuted": 156,
  "totalMetersProcessed": 385600,
  "overallSuccessRate": 98.72,
  "averageTaskDuration": 22450,
  "totalBillsGenerated": 385200,
  "totalPaymentsSucceeded": 298500,
  "paymentSuccessRate": 99.15
}
```

### 6.2 获取详细指标

```bash
GET /scheduler/metrics
```

响应：
```json
{
  "basic": {
    "totalTasksExecuted": 156,
    "totalMetersProcessed": 385600,
    "overallSuccessRate": 98.72
  },
  "billing": {
    "totalBillsGenerated": 385200,
    "totalPaymentsSucceeded": 298500,
    "paymentSuccessRate": 99.15
  },
  "realtime": {
    "activeTaskCount": 0,
    "currentTotalProcessed": 12500,
    "timestamp": 1705312225123
  },
  "config": {
    "batchSize": 500,
    "maxConcurrentTasks": 10,
    "threadPoolSize": 16
  }
}
```

### 6.3 获取监控摘要

```bash
GET /scheduler/metrics/summary
```

响应：
```json
{
  "timestamp": 1705312225123,
  "status": "running",
  "healthStatus": "healthy",
  "overallSuccessRate": 98.72,
  "paymentSuccessRate": 99.15,
  "averageTaskDuration": 22450,
  "totalMetersProcessed": 385600,
  "warnings": []
}
```

### 7.4 文件管理接口

#### 获取报告文件列表
```bash
GET /scheduler/reports/list
```

响应：
```json
{
  "detailReports": [
    "performance-report_2024-01-15_15-00-18.txt",
    "performance-report_2024-01-15_14-30-25.txt"
  ],
  "jsonReports": [
    "metrics_2024-01-15_15-00-18.json",
    "metrics_2024-01-15_14-30-25.json"
  ],
  "dailyReports": [
    "daily-summary_2024-01-16.txt",
    "daily-summary_2024-01-15.txt"
  ],
  "totalFiles": 6
}
```

#### 获取目录统计信息
```bash
GET /scheduler/reports/stats
```

响应：
```json
{
  "totalFiles": 45,
  "totalSize": 2048576,
  "totalSizeFormatted": "2.0 MB",
  "compressedFiles": 12,
  "expiredFiles": 3,
  "oldFiles": 8,
  "timestamp": "2024-01-16T10:30:25"
}
```

#### 下载报告文件
```bash
GET /scheduler/reports/download/{type}/{filename}
```

参数：
- `type`: 文件类型 (detail/json/daily)
- `filename`: 文件名

示例：
```bash
GET /scheduler/reports/download/detail/performance-report_2024-01-15_14-30-25.txt
```

#### 获取最新性能报告
```bash
GET /scheduler/reports/latest
```

响应：
```json
{
  "filename": "performance-report_2024-01-15_15-00-18.txt",
  "content": "====================================================================================================\n                              定时任务执行性能指标报告\n...",
  "fileSize": 4096,
  "lastModified": "2024-01-15T15:00:25Z"
}
```

#### 获取最新JSON指标
```bash
GET /scheduler/reports/latest-json
```

响应：
```json
{
  "filename": "metrics_2024-01-15_15-00-18.json",
  "content": "{\n  \"timestamp\": \"2024-01-15 15:00:18\",\n  \"healthStatus\": \"healthy\",\n...",
  "fileSize": 2048,
  "lastModified": "2024-01-15T15:00:25Z"
}
```

#### 手动触发文件清理
```bash
GET /scheduler/reports/cleanup
```

响应：
```json
{
  "success": true,
  "message": "文件清理任务已触发",
  "timestamp": "2024-01-16T10:30:25"
}
```

## 7. 性能指标说明

### 7.1 核心指标

- **执行总耗时**: 本次定时任务的总执行时间
- **任务成功率**: 成功执行的任务占总任务数的百分比
- **水表处理速度**: 每秒处理的水表数量
- **支付成功率**: 自动支付成功的比例

### 7.2 性能等级评分标准

- **优秀 (90-100分)**: 系统运行非常高效，各项指标优良
- **良好 (75-89分)**: 系统运行效率较高，有小幅优化空间
- **正常 (60-74分)**: 系统运行平稳，需要关注部分指标
- **较差 (0-59分)**: 需要优化系统性能，存在明显问题

### 7.3 健康状态判断

- **healthy**: 成功率≥98% 且 支付成功率≥95%
- **warning**: 成功率≥90% 且 支付成功率≥85%
- **critical**: 成功率<90% 或 支付成功率<85%

## 8. 使用建议

### 8.1 日常监控
- **实时监控**: 通过REST API获取最新性能指标
- **文件监控**: 定期查看性能报告文件
- **健康检查**: 关注健康状态和性能等级变化

### 8.2 文件管理
- **定期清理**: 系统自动清理过期文件，也可手动触发
- **存储监控**: 通过统计接口监控文件存储使用情况
- **备份策略**: 重要的性能报告文件建议定期备份

### 8.3 性能优化
- **趋势分析**: 通过每日汇总报告观察性能变化趋势
- **问题定位**: 结合详细报告和JSON数据快速定位问题
- **参数调优**: 根据性能分析建议调整系统参数

### 8.4 集成建议
- **监控系统**: 将JSON格式数据集成到监控系统
- **告警设置**: 基于健康状态和性能指标设置告警
- **报表生成**: 利用性能数据生成定期报表

### 8.5 文件路径说明
```
logs/performance-reports/
├── performance-report_*.txt     # 详细性能报告（每次执行生成）
├── daily/daily-summary_*.txt    # 每日汇总报告（按日期追加）
└── json/metrics_*.json          # JSON格式指标（每次执行生成）
```

### 8.6 注意事项
1. **磁盘空间**: 定期监控日志目录的磁盘使用情况
2. **文件权限**: 确保应用有足够的文件读写权限
3. **并发访问**: 多实例部署时注意文件访问冲突
4. **网络传输**: 下载大文件时注意网络超时设置

通过这套完整的文件化性能指标输出系统，可以：
- 持久化保存所有性能数据，便于历史分析
- 支持离线分析和报表生成
- 提供完整的文件管理和清理机制
- 支持多种格式的数据导出和集成
- 确保系统长期稳定运行的可观测性
