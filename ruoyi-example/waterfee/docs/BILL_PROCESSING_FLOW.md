# 抄表任务账单处理流程

## 概述

在抄表任务优化方案中，账单处理是一个重要的环节。系统需要在创建抄表记录后，自动生成账单、审核账单，并对智能表进行自动扣款。本文档详细说明了优化后的账单处理流程。

## 账单处理流程图

```
抄表记录创建
    ↓
批量插入抄表记录
    ↓
按水表类型分组
    ├── 智能表记录 → 账单审核 → 自动支付
    └── 机械表记录 → 账单审核 → 等待人工处理
    ↓
统计处理结果
    ↓
性能监控记录
```

## 详细处理流程

### 1. 抄表记录创建阶段

```java
// 创建抄表记录
WaterfeeMeterReadingRecord record = createOptimizedRecord(meter, latestRecord, taskId, now);

// 批量收集记录
List<WaterfeeMeterReadingRecord> recordsToInsert = new ArrayList<>();
recordsToInsert.add(record);
```

**关键点：**
- 批量创建抄表记录，减少数据库操作
- 智能表自动读取当前读数
- 机械表等待人工录入读数

### 2. 批量插入阶段

```java
// 批量插入抄表记录
batchInsertRecords(recordsToInsert);

// 立即处理账单审核和支付
batchProcessBillsAndPayment(recordsToInsert, result);
```

**关键点：**
- 使用批量插入提高数据库写入性能
- 插入成功后立即处理账单逻辑

### 3. 账单处理分组

```java
// 按水表类型分组处理
Map<Boolean, List<WaterfeeMeterReadingRecord>> groupedRecords = records.stream()
    .collect(Collectors.groupingBy(record -> "2".equals(record.getMeterType())));

List<WaterfeeMeterReadingRecord> intelligentRecords = groupedRecords.getOrDefault(true, new ArrayList<>());
List<WaterfeeMeterReadingRecord> mechanicalRecords = groupedRecords.getOrDefault(false, new ArrayList<>());
```

**分组逻辑：**
- **智能表（meterType = 2）**：需要自动审核和自动支付
- **机械表（meterType = 1）**：只需要自动审核，不自动支付

### 4. 智能表账单处理

```java
private void batchProcessIntelligentMeterBills(List<WaterfeeMeterReadingRecord> records, BatchResult result) {
    for (WaterfeeMeterReadingRecord record : records) {
        // 1. 审核记录并生成账单
        List<Long> billIds = meterReadingRecordService.auditRecordRtnBillIds(record);
        
        // 2. 统计账单生成
        result.incrementBillsGenerated();
        result.incrementBillsAudited();
        
        // 3. 处理自动支付
        processAutoPaymentWithStats(record, billIds, result);
    }
}
```

**智能表处理步骤：**
1. **账单审核**：调用 `auditRecordRtnBillIds()` 审核抄表记录并生成账单
2. **获取账单信息**：根据账单ID获取账单详情
3. **自动扣款**：调用 `autoPayBillByDeposit()` 进行自动扣款
4. **结果统计**：记录支付成功/失败统计

### 5. 机械表账单处理

```java
private void batchProcessMechanicalMeterBills(List<WaterfeeMeterReadingRecord> records, BatchResult result) {
    for (WaterfeeMeterReadingRecord record : records) {
        // 只审核记录，不自动支付
        List<Long> billIds = meterReadingRecordService.auditRecordRtnBillIds(record);
        
        if (billIds != null && !billIds.isEmpty()) {
            result.incrementBillsGenerated();
            result.incrementBillsAudited();
        }
    }
}
```

**机械表处理步骤：**
1. **账单审核**：审核抄表记录并生成账单
2. **等待人工处理**：不进行自动支付，等待用户手动缴费

### 6. 自动支付处理

```java
private void processAutoPayment(WaterfeeMeterReadingRecord record, List<Long> billIds, BatchResult result) {
    // 检查记录是否已审核
    if (!"1".equals(record.getIsAudited())) {
        return;
    }
    
    // 获取账单信息
    WaterfeeBillVo bill = waterfeeBillService.queryById(billIds.get(0));
    
    // 智能表自动扣款
    if ("2".equals(record.getMeterType())) {
        result.incrementPaymentsProcessed();
        
        boolean success = waterfeeCounterPaymentService.autoPayBillByDeposit(
            bill.getCustomerId(), billIds, bill.getTotalAmount(),
            "智能表自动扣款：" + bill.getBillNumber()
        );
        
        if (success) {
            result.incrementPaymentsSucceeded();
        } else {
            result.incrementPaymentsFailed();
        }
    }
}
```

**自动支付逻辑：**
1. **验证审核状态**：只有已审核的记录才能进行支付
2. **获取账单详情**：查询账单金额和客户信息
3. **执行自动扣款**：从客户预存款中扣除费用
4. **更新账单状态**：标记账单为已支付
5. **记录支付结果**：统计支付成功/失败数量

## 统计信息

### 处理结果统计

```java
public static class BatchProcessResult {
    private int totalCount;           // 总处理数量
    private int successCount;         // 成功数量
    private int failCount;           // 失败数量
    
    // 账单处理统计
    private int billsGenerated;      // 生成账单数量
    private int billsAudited;        // 审核账单数量
    private int paymentsProcessed;   // 处理支付数量
    private int paymentsSucceeded;   // 支付成功数量
    private int paymentsFailed;      // 支付失败数量
}
```

### 性能监控指标

```java
// 性能监控记录
performanceMonitor.recordTaskComplete(taskId, 
    result.getSuccessCount(), result.getFailCount(),
    result.getBillsGenerated(), result.getBillsAudited(), 
    result.getPaymentsProcessed(), result.getPaymentsSucceeded(), result.getPaymentsFailed());
```

## 错误处理

### 1. 账单生成失败
- **现象**：`auditRecordRtnBillIds()` 返回空或异常
- **处理**：记录错误日志，继续处理其他记录
- **影响**：该水表不会生成账单，需要人工处理

### 2. 自动支付失败
- **现象**：`autoPayBillByDeposit()` 返回false
- **原因**：客户余额不足、账户状态异常等
- **处理**：记录支付失败，账单保持未支付状态
- **后续**：客户可通过其他方式缴费

### 3. 批量处理异常
- **现象**：整个批次处理出现异常
- **处理**：使用独立事务，避免影响其他批次
- **恢复**：支持重试机制和部分失败恢复

## 性能优化要点

### 1. 批量操作
- 批量插入抄表记录
- 批量审核账单
- 并行处理支付

### 2. 分组处理
- 智能表和机械表分开处理
- 减少不必要的支付处理

### 3. 异步处理
- 账单处理不阻塞抄表记录创建
- 支付失败不影响账单生成

### 4. 统计监控
- 完整的处理统计信息
- 实时性能监控
- 支付成功率监控

## 配置参数

```yaml
waterfee:
  meter-reading:
    batch:
      enable-batch-insert: true     # 启用批量插入
      size: 500                     # 批处理大小
    performance:
      enable-performance-alert: true # 启用性能告警
```

## 总结

优化后的账单处理流程具有以下特点：

1. **完整性**：覆盖从抄表记录到账单支付的完整流程
2. **高效性**：使用批量处理和并行处理提升性能
3. **智能化**：智能表自动完成整个缴费流程
4. **可监控**：提供详细的处理统计和性能监控
5. **容错性**：支持部分失败和错误恢复
6. **可配置**：支持灵活的参数配置和优化调整

通过这套完整的账单处理流程，系统能够高效地处理大量设备的抄表和缴费业务，显著提升用户体验和系统性能。
