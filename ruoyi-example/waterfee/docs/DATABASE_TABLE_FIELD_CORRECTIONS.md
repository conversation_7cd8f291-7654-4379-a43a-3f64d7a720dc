# 数据库表名和字段名修正说明

## 概述

在账单处理优化过程中，发现原始设计中使用的数据库表名和字段名与实际系统中的表结构不符。本文档详细说明了所有的修正内容。

## 修正的表名

### 1. 账单表
- **错误表名**: `waterfee_bill`
- **正确表名**: `waterfee_bills`
- **说明**: 账单表使用复数形式

### 2. 客户账户表
- **错误表名**: `waterfee_customer_account`
- **正确表名**: `waterfee_user`
- **说明**: 系统中没有独立的客户账户表，用户余额存储在用户表中

### 3. 支付记录表
- **错误表名**: `waterfee_payment_record`
- **正确表名**: `waterfee_payment_detail`
- **说明**: 支付记录表使用detail后缀

### 4. 客户表
- **错误表名**: `waterfee_customer`
- **正确表名**: `waterfee_user`
- **说明**: 客户信息存储在用户表中

## 修正的字段名

### 1. 账单表字段 (waterfee_bills)

| 错误字段名 | 正确字段名 | 说明 |
|-----------|-----------|------|
| `meter_no` | `meter_id` | 使用水表ID而非水表编号 |
| `previous_reading` | `previous_reading_value` | 上次读数值 |
| `current_reading` | `current_reading_value` | 当前读数值 |
| `water_usage` | `consumption_volume` | 用水量 |
| `unit_price` | `base_charge_amount` | 基础费用 |
| `water_fee` | - | 不存在此字段 |
| `sewage_fee` | - | 不存在此字段 |
| `other_fee` | `adjustments_amount` | 调整金额 |
| `due_date` | `billing_due_date` | 账单到期日期 |

### 2. 用户表字段 (waterfee_user)

| 错误字段名 | 正确字段名 | 说明 |
|-----------|-----------|------|
| `customer_id` | `user_id` | 用户ID |
| `customer_name` | `user_name` | 用户姓名 |
| `contact_phone` | `phone_number` | 联系电话 |
| `customer_address` | `address` | 地址 |
| `account_balance` | `balance` | 账户余额 |
| `frozen_amount` | - | 不存在冻结金额字段，默认为0 |

### 3. 支付记录表字段 (waterfee_payment_detail)

| 错误字段名 | 正确字段名 | 说明 |
|-----------|-----------|------|
| `customer_id` | `user_id` | 用户ID |

## 修正的文件

### 1. BillProcessingBatchMapper.java
- 修正了所有@Select注解中的表名和字段名
- 更新了方法参数和返回值映射

### 2. BillProcessingBatchMapper.xml
- 修正了所有SQL查询中的表名和字段名
- 更新了resultMap映射关系
- 修正了批量操作的SQL语句

### 3. 索引建议
更新了性能优化索引建议：

```sql
-- 账单表索引
CREATE INDEX idx_bills_customer_status ON waterfee_bills(customer_id, bill_status);
CREATE INDEX idx_bills_meter_period ON waterfee_bills(meter_id, billing_period_start);
CREATE INDEX idx_bills_status_due ON waterfee_bills(bill_status, billing_due_date);

-- 用户表索引
CREATE INDEX idx_user_balance ON waterfee_user(user_id, balance);

-- 支付记录表索引
CREATE INDEX idx_payment_user_time ON waterfee_payment_detail(user_id, payment_time DESC);
CREATE INDEX idx_payment_bill_time ON waterfee_payment_detail(bill_id, payment_time DESC);

-- 复合索引
CREATE INDEX idx_bills_del_status ON waterfee_bills(del_flag, bill_status, customer_id);
CREATE INDEX idx_user_del_balance ON waterfee_user(del_flag, user_id, balance);
CREATE INDEX idx_payment_del_status ON waterfee_payment_detail(del_flag, payment_status);
```

## 主要修正点

### 1. 表名统一
- 所有表名都使用实际系统中的表名
- 确保与现有数据库结构一致

### 2. 字段名对应
- 所有字段名都映射到实际的数据库字段
- 处理了不存在的字段（如frozen_amount设为0）

### 3. 关联关系
- 修正了表之间的关联关系
- 确保JOIN查询使用正确的字段

### 4. 数据类型
- 确保查询结果的数据类型与实际字段类型匹配
- 处理了字段别名以保持代码兼容性

## 影响的功能

### 1. 批量账单查询
- `batchSelectBillsByIds` - 批量获取账单信息
- `batchSelectUnpaidBillsByCustomers` - 获取未支付账单

### 2. 客户信息查询
- `batchSelectCustomerBalances` - 获取客户余额
- `batchGetCustomerPaymentCapacity` - 获取支付能力

### 3. 支付记录查询
- `batchGetRecentPaymentRecords` - 获取最近支付记录
- `batchGetBillPaymentHistory` - 获取账单支付历史

### 4. 统计查询
- `batchGetBillStatistics` - 获取账单统计
- `batchGetBillDetailsWithCustomer` - 获取账单详情

## 兼容性处理

### 1. 字段别名
使用AS别名确保返回的字段名与代码期望一致：
```sql
SELECT user_id as customer_id, balance as account_balance
```

### 2. 默认值处理
对于不存在的字段提供默认值：
```sql
SELECT 0 as frozen_amount
```

### 3. 关联查询优化
使用LEFT JOIN确保数据完整性：
```sql
LEFT JOIN waterfee_user u ON b.customer_id = u.user_id AND u.del_flag = '0'
```

## 测试验证

### 1. 单元测试
- 验证所有批量查询方法
- 确保返回数据格式正确

### 2. 集成测试
- 测试账单处理完整流程
- 验证数据库操作正确性

### 3. 性能测试
- 确保修正后的查询性能符合预期
- 验证索引优化效果

## 注意事项

1. **数据迁移**: 如果有旧数据，需要考虑数据迁移方案
2. **索引创建**: 建议在生产环境中逐步创建索引
3. **监控观察**: 部署后密切监控数据库性能
4. **回滚准备**: 准备回滚方案以防出现问题

## 总结

通过这次数据库表名和字段名的修正，确保了：

1. ✅ **数据库一致性**: 所有查询都使用正确的表名和字段名
2. ✅ **功能完整性**: 所有账单处理功能正常工作
3. ✅ **性能优化**: 索引建议基于实际表结构
4. ✅ **代码兼容性**: 通过字段别名保持代码兼容
5. ✅ **扩展性**: 为未来的功能扩展奠定基础

这些修正确保了账单处理优化功能能够在实际的数据库环境中正常运行，为系统的稳定性和性能提升提供了保障。
