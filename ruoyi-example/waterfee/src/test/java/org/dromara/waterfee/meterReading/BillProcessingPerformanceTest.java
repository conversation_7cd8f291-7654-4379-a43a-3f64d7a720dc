package org.dromara.waterfee.meterReading;

import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.service.SimpleBillProcessingService;
import org.dromara.waterfee.meterReading.service.MeterReadingBatchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账单处理性能测试
 * 用于验证账单处理优化的效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BillProcessingPerformanceTest {

    @Autowired
    private SimpleBillProcessingService simpleBillProcessingService;

    @Autowired
    private MeterReadingBatchService meterReadingBatchService;

    /**
     * 测试账单处理性能对比
     */
    @Test
    public void testBillProcessingPerformanceComparison() {
        log.info("开始账单处理性能对比测试...");

        // 生成测试数据
        List<WaterfeeMeterReadingRecord> testRecords = generateTestRecords(5000);

        // 测试优化前的处理方式（模拟）
        long startTime1 = System.currentTimeMillis();
        simulateOriginalBillProcessing(testRecords);
        long originalTime = System.currentTimeMillis() - startTime1;

        // 测试优化后的处理方式
        long startTime2 = System.currentTimeMillis();
        SimpleBillProcessingService.BillProcessingResult optimizedResult =
            simpleBillProcessingService.optimizedBatchProcessBills(testRecords);
        long optimizedTime = System.currentTimeMillis() - startTime2;

        // 输出对比结果
        log.info("=".repeat(80));
        log.info("                    账单处理性能对比测试结果");
        log.info("=".repeat(80));
        log.info("测试数据量: {} 条记录", testRecords.size());
        log.info("");
        log.info("优化前处理时间: {} ms", originalTime);
        log.info("优化后处理时间: {} ms", optimizedTime);
        log.info("性能提升: {:.2f}倍", (double) originalTime / optimizedTime);
        log.info("时间节省: {} ms ({:.1f}%)", originalTime - optimizedTime,
            ((double) (originalTime - optimizedTime) / originalTime) * 100);
        log.info("");
        log.info("优化后处理结果:");
        log.info("- 总记录数: {}", optimizedResult.getTotalRecords());
        log.info("- 账单生成: {}", optimizedResult.getBillsGenerated());
        log.info("- 账单审核: {}", optimizedResult.getBillsAudited());
        log.info("- 支付处理: {}", optimizedResult.getPaymentsProcessed());
        log.info("- 支付成功: {}", optimizedResult.getPaymentsSucceeded());
        log.info("- 支付失败: {}", optimizedResult.getPaymentsFailed());
        log.info("- 处理速度: {:.2f} 条/秒", (testRecords.size() * 1000.0) / optimizedTime);
        log.info("=".repeat(80));

        // 验证性能提升
        assert optimizedTime < originalTime : "优化后的处理时间应该更短";
        assert (double) originalTime / optimizedTime > 2.0 : "性能提升应该超过2倍";

        // 验证处理结果的正确性
        assert optimizedResult.getTotalRecords() == testRecords.size() : "处理记录数应该正确";
        assert optimizedResult.getBillsGenerated() > 0 : "应该生成账单";
        assert optimizedResult.getBillsAudited() >= optimizedResult.getBillsGenerated() : "审核数应该不少于生成数";

        log.info("账单处理优化测试通过 ✅");
    }

    /**
     * 测试大批量账单处理
     */
    @Test
    public void testLargeBatchBillProcessing() {
        log.info("开始大批量账单处理测试...");

        // 生成大量测试数据
        List<WaterfeeMeterReadingRecord> largeTestRecords = generateTestRecords(20000);

        long startTime = System.currentTimeMillis();
        SimpleBillProcessingService.BillProcessingResult result =
            simpleBillProcessingService.optimizedBatchProcessBills(largeTestRecords);
        long processingTime = System.currentTimeMillis() - startTime;

        // 输出测试结果
        log.info("大批量账单处理测试结果:");
        log.info("- 处理数量: {} 条", result.getTotalRecords());
        log.info("- 处理时间: {} ms ({:.2f} 秒)", processingTime, processingTime / 1000.0);
        log.info("- 处理速度: {:.2f} 条/秒", (result.getTotalRecords() * 1000.0) / processingTime);
        log.info("- 账单生成率: {:.2f}%", (result.getBillsGenerated() * 100.0) / result.getTotalRecords());
        log.info("- 账单审核率: {:.2f}%", (result.getBillsAudited() * 100.0) / result.getBillsGenerated());
        log.info("- 支付成功率: {:.2f}%", result.getPaymentsProcessed() > 0 ?
            (result.getPaymentsSucceeded() * 100.0) / result.getPaymentsProcessed() : 0);

        // 验证大批量处理能力
        assert processingTime < 300000 : "20000条记录应在5分钟内处理完成";
        assert (result.getTotalRecords() * 1000.0) / processingTime > 50 : "处理速度应超过50条/秒";
    }

    /**
     * 测试并发账单处理
     */
    @Test
    public void testConcurrentBillProcessing() {
        log.info("开始并发账单处理测试...");

        int threadCount = 3;
        int recordsPerThread = 3000;
        List<Thread> threads = new ArrayList<>();
        List<Long> processingTimes = new ArrayList<>();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            Thread thread = new Thread(() -> {
                List<WaterfeeMeterReadingRecord> threadRecords = generateTestRecords(recordsPerThread, threadIndex * recordsPerThread);

                long startTime = System.currentTimeMillis();
                SimpleBillProcessingService.BillProcessingResult result =
                    simpleBillProcessingService.optimizedBatchProcessBills(threadRecords);
                long processingTime = System.currentTimeMillis() - startTime;

                synchronized (processingTimes) {
                    processingTimes.add(processingTime);
                }

                log.info("线程 {} 完成 - 处理: {}, 耗时: {}ms, 速度: {:.2f}条/秒",
                    threadIndex, result.getTotalRecords(), processingTime,
                    (result.getTotalRecords() * 1000.0) / processingTime);
            });

            threads.add(thread);
        }

        // 启动所有线程
        long totalStartTime = System.currentTimeMillis();
        threads.forEach(Thread::start);

        // 等待所有线程完成
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        long totalTime = System.currentTimeMillis() - totalStartTime;

        // 计算统计信息
        double avgProcessingTime = processingTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxProcessingTime = processingTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long minProcessingTime = processingTimes.stream().mapToLong(Long::longValue).min().orElse(0);

        log.info("并发账单处理测试结果:");
        log.info("- 并发线程数: {}", threadCount);
        log.info("- 每线程处理数: {}", recordsPerThread);
        log.info("- 总处理数量: {}", threadCount * recordsPerThread);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均线程耗时: {:.2f}ms", avgProcessingTime);
        log.info("- 最长线程耗时: {}ms", maxProcessingTime);
        log.info("- 最短线程耗时: {}ms", minProcessingTime);
        log.info("- 整体处理速度: {:.2f} 条/秒", (threadCount * recordsPerThread * 1000.0) / totalTime);

        // 验证并发处理效果
        assert totalTime < maxProcessingTime * 1.5 : "并发处理应显著快于串行处理";
    }

    /**
     * 模拟原始的账单处理方式（串行处理）
     */
    private void simulateOriginalBillProcessing(List<WaterfeeMeterReadingRecord> records) {
        // 模拟原始的串行处理方式
        for (WaterfeeMeterReadingRecord record : records) {
            try {
                // 模拟单个记录的处理时间
                Thread.sleep(2); // 每条记录2ms的处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 生成测试记录
     */
    private List<WaterfeeMeterReadingRecord> generateTestRecords(int count) {
        return generateTestRecords(count, 0);
    }

    /**
     * 生成测试记录
     */
    private List<WaterfeeMeterReadingRecord> generateTestRecords(int count, int startIndex) {
        List<WaterfeeMeterReadingRecord> records = new ArrayList<>();
        Date now = new Date();

        for (int i = 0; i < count; i++) {
            WaterfeeMeterReadingRecord record = new WaterfeeMeterReadingRecord();
            record.setRecordId((long) (startIndex + i + 1));
            record.setMeterNo("TEST_METER_" + String.format("%08d", startIndex + i));
            record.setMeterType(i % 3 == 0 ? "2" : "1"); // 1/3为智能表，2/3为机械表
            record.setCurrentReading(100.0 + i);
            record.setLastReading(90.0 + i);
            record.setWaterUsage(10.0);
            record.setReadingTime(now);
            record.setIsAudited("0"); // 未审核
            record.setTaskId(1L);
            record.setSourceType("1");
            record.setDelFlag("0");
            record.setTenantId("000000");
            records.add(record);
        }

        return records;
    }
}
