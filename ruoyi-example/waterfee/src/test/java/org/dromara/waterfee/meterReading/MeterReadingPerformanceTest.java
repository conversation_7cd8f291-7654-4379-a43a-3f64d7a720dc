package org.dromara.waterfee.meterReading;

import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meterReading.service.MeterReadingBatchService;
import org.dromara.waterfee.meterReading.service.MeterReadingPerformanceMonitor;
import org.dromara.waterfee.meterReading.task.MeterReadingTaskScheduler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 抄表任务性能测试
 * 用于验证优化方案的效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class MeterReadingPerformanceTest {

    @Autowired
    private MeterReadingTaskScheduler taskScheduler;

    @Autowired
    private MeterReadingBatchService batchService;

    @Autowired
    private MeterReadingPerformanceMonitor performanceMonitor;

    /**
     * 测试批量处理性能
     */
    @Test
    public void testBatchProcessingPerformance() {
        log.info("开始批量处理性能测试...");

        // 生成测试数据
        List<String> meterNos = generateTestMeterNos(10000);
        Long taskId = 1L;
        Date now = new Date();

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行批量处理
        MeterReadingBatchService.BatchProcessResult result =
            batchService.batchProcessMeterReadings(meterNos, taskId, now);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 输出测试结果
        log.info("批量处理性能测试结果:");
        log.info("- 处理数量: {}", result.getTotalCount());
        log.info("- 成功数量: {}", result.getSuccessCount());
        log.info("- 失败数量: {}", result.getFailCount());
        log.info("- 处理时间: {}ms", duration);
        log.info("- 平均处理速度: {:.2f} 条/秒", (result.getTotalCount() * 1000.0) / duration);
        log.info("- 成功率: {:.2f}%", (result.getSuccessCount() * 100.0) / result.getTotalCount());
        log.info("- 账单生成: {}", result.getBillsGenerated());
        log.info("- 账单审核: {}", result.getBillsAudited());
        log.info("- 支付处理: {}", result.getPaymentsProcessed());
        log.info("- 支付成功: {}", result.getPaymentsSucceeded());
        log.info("- 支付失败: {}", result.getPaymentsFailed());

        // 验证性能指标
        assert duration < 300000; // 应在5分钟内完成
        assert result.getSuccessCount() > result.getTotalCount() * 0.9; // 成功率应超过90%
    }

    /**
     * 测试并发处理性能
     */
    @Test
    public void testConcurrentProcessingPerformance() {
        log.info("开始并发处理性能测试...");

        int taskCount = 5;
        int metersPerTask = 2000;
        ExecutorService executor = Executors.newFixedThreadPool(taskCount);

        List<CompletableFuture<Long>> futures = new ArrayList<>();

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 创建并发任务
        for (int i = 0; i < taskCount; i++) {
            final int taskIndex = i;
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                List<String> meterNos = generateTestMeterNos(metersPerTask, taskIndex * metersPerTask);
                Long taskId = (long) (taskIndex + 1);
                Date now = new Date();

                long taskStartTime = System.currentTimeMillis();
                MeterReadingBatchService.BatchProcessResult result =
                    batchService.batchProcessMeterReadings(meterNos, taskId, now);
                long taskEndTime = System.currentTimeMillis();

                log.info("任务 {} 完成 - 处理: {}, 成功: {}, 耗时: {}ms",
                    taskIndex, result.getTotalCount(), result.getSuccessCount(),
                    taskEndTime - taskStartTime);

                return taskEndTime - taskStartTime;
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成
        List<Long> taskDurations = futures.stream()
            .map(CompletableFuture::join)
            .toList();

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 计算统计信息
        long maxTaskDuration = taskDurations.stream().mapToLong(Long::longValue).max().orElse(0);
        long minTaskDuration = taskDurations.stream().mapToLong(Long::longValue).min().orElse(0);
        double avgTaskDuration = taskDurations.stream().mapToLong(Long::longValue).average().orElse(0);

        // 输出测试结果
        log.info("并发处理性能测试结果:");
        log.info("- 并发任务数: {}", taskCount);
        log.info("- 每任务处理数量: {}", metersPerTask);
        log.info("- 总处理数量: {}", taskCount * metersPerTask);
        log.info("- 总耗时: {}ms", totalDuration);
        log.info("- 最长任务耗时: {}ms", maxTaskDuration);
        log.info("- 最短任务耗时: {}ms", minTaskDuration);
        log.info("- 平均任务耗时: {:.2f}ms", avgTaskDuration);
        log.info("- 整体处理速度: {:.2f} 条/秒", (taskCount * metersPerTask * 1000.0) / totalDuration);

        executor.shutdown();
        try {
            executor.awaitTermination(60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证并发性能
        assert totalDuration < maxTaskDuration * 1.5; // 并发执行应显著快于串行执行
    }

    /**
     * 测试性能监控功能
     */
    @Test
    public void testPerformanceMonitoring() {
        log.info("开始性能监控功能测试...");

        // 重置监控数据
        performanceMonitor.resetStatistics();

        // 模拟多个任务执行
        for (int i = 0; i < 3; i++) {
            String taskId = "test-task-" + i;
            int meterCount = 1000 + i * 500;

            // 记录任务开始
            performanceMonitor.recordTaskStart(taskId, meterCount);

            // 模拟任务执行
            try {
                Thread.sleep(1000 + i * 500); // 模拟不同的执行时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 记录任务完成
            int successCount = (int) (meterCount * 0.95); // 95%成功率
            int failCount = meterCount - successCount;
            performanceMonitor.recordTaskComplete(taskId, successCount, failCount);
        }

        // 获取性能报告
        MeterReadingPerformanceMonitor.PerformanceReport report =
            performanceMonitor.getPerformanceReport();

        // 输出监控结果
        log.info("性能监控测试结果:");
        log.info("- 总任务数: {}", report.getTotalTasksExecuted());
        log.info("- 总处理数量: {}", report.getTotalMetersProcessed());
        log.info("- 总成功数量: {}", report.getTotalSuccessCount());
        log.info("- 总失败数量: {}", report.getTotalFailCount());
        log.info("- 整体成功率: {:.2f}%", report.getOverallSuccessRate());
        log.info("- 平均任务耗时: {}ms", report.getAverageTaskDuration());
        log.info("- 平均每表处理时间: {}ms", report.getAverageProcessingTimePerMeter());

        // 验证监控数据
        assert report.getTotalTasksExecuted() == 3;
        assert report.getTotalMetersProcessed() > 0;
        assert report.getOverallSuccessRate() > 90.0;

        // 输出详细的性能指标
        log.info("性能监控详细报告:");
        log.info("- 总任务数: {}", report.getTotalTasksExecuted());
        log.info("- 总处理数量: {}", report.getTotalMetersProcessed());
        log.info("- 整体成功率: {:.2f}%", report.getOverallSuccessRate());
        log.info("- 平均任务耗时: {}ms", report.getAverageTaskDuration());
        log.info("- 账单生成数: {}", report.getTotalBillsGenerated());
        log.info("- 账单审核率: {:.2f}%", report.getBillAuditRate());
        log.info("- 支付成功数: {}", report.getTotalPaymentsSucceeded());
        log.info("- 支付成功率: {:.2f}%", report.getPaymentSuccessRate());
    }

    /**
     * 压力测试
     */
    @Test
    public void testStressTest() {
        log.info("开始压力测试...");

        int totalMeters = 50000; // 5万个水表
        int batchSize = 5000;    // 每批5000个
        int batchCount = totalMeters / batchSize;

        List<CompletableFuture<MeterReadingBatchService.BatchProcessResult>> futures = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        // 创建批量处理任务
        for (int i = 0; i < batchCount; i++) {
            final int batchIndex = i;
            CompletableFuture<MeterReadingBatchService.BatchProcessResult> future =
                CompletableFuture.supplyAsync(() -> {
                    List<String> meterNos = generateTestMeterNos(batchSize, batchIndex * batchSize);
                    Long taskId = (long) (batchIndex + 1);
                    Date now = new Date();

                    return batchService.batchProcessMeterReadings(meterNos, taskId, now);
                });

            futures.add(future);
        }

        // 等待所有批次完成
        List<MeterReadingBatchService.BatchProcessResult> results = futures.stream()
            .map(CompletableFuture::join)
            .toList();

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 汇总结果
        int totalProcessed = results.stream().mapToInt(MeterReadingBatchService.BatchProcessResult::getTotalCount).sum();
        int totalSuccess = results.stream().mapToInt(MeterReadingBatchService.BatchProcessResult::getSuccessCount).sum();
        int totalFail = results.stream().mapToInt(MeterReadingBatchService.BatchProcessResult::getFailCount).sum();

        // 输出压力测试结果
        log.info("压力测试结果:");
        log.info("- 总处理数量: {}", totalProcessed);
        log.info("- 总成功数量: {}", totalSuccess);
        log.info("- 总失败数量: {}", totalFail);
        log.info("- 总耗时: {}ms ({:.2f}分钟)", totalDuration, totalDuration / 60000.0);
        log.info("- 处理速度: {:.2f} 条/秒", (totalProcessed * 1000.0) / totalDuration);
        log.info("- 成功率: {:.2f}%", (totalSuccess * 100.0) / totalProcessed);

        // 验证压力测试结果
        assert totalProcessed == totalMeters;
        assert totalDuration < 600000; // 应在10分钟内完成
        assert (totalSuccess * 100.0) / totalProcessed > 85.0; // 成功率应超过85%
    }

    /**
     * 生成测试用的水表编号列表
     */
    private List<String> generateTestMeterNos(int count) {
        return generateTestMeterNos(count, 0);
    }

    /**
     * 测试性能指标输出功能
     */
    @Test
    public void testPerformanceMetricsOutput() {
        log.info("开始性能指标输出功能测试...");

        // 重置监控数据
        performanceMonitor.resetStatistics();

        // 模拟执行一个完整的抄表任务
        List<String> meterNos = generateTestMeterNos(1000);
        Long taskId = 1L;
        Date now = new Date();

        long startTime = System.currentTimeMillis();

        // 记录任务开始
        performanceMonitor.recordTaskStart("test-metrics-task", meterNos.size());

        // 执行批量处理
        MeterReadingBatchService.BatchProcessResult result =
            batchService.batchProcessMeterReadings(meterNos, taskId, now);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 记录任务完成（包含账单处理信息）
        performanceMonitor.recordTaskComplete("test-metrics-task",
            result.getSuccessCount(), result.getFailCount(),
            result.getBillsGenerated(), result.getBillsAudited(),
            result.getPaymentsProcessed(), result.getPaymentsSucceeded(), result.getPaymentsFailed());

        // 获取性能报告
        MeterReadingPerformanceMonitor.PerformanceReport report =
            performanceMonitor.getPerformanceReport();

        // 输出完整的性能指标报告
        log.info("=".repeat(100));
        log.info("                              性能指标输出功能测试报告");
        log.info("=".repeat(100));
        log.info("┌─────────────────────────────────┬─────────────────────────────────────────────┐");
        log.info("│ 指标项                          │ 数值                                        │");
        log.info("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        log.info("│ 测试执行总耗时                  │ {} ms ({:.2f} 秒)                          │",
            String.format("%,d", duration), duration / 1000.0);
        log.info("│ 处理水表总数                    │ {} 个                                       │", result.getTotalCount());
        log.info("│ 处理成功数量                    │ {} 个                                       │", result.getSuccessCount());
        log.info("│ 处理失败数量                    │ {} 个                                       │", result.getFailCount());
        log.info("│ 处理成功率                      │ {:.2f}%                                     │",
            result.getTotalCount() > 0 ? (result.getSuccessCount() * 100.0) / result.getTotalCount() : 0);
        log.info("│ 处理速度                        │ {:.2f} 个/秒                                │",
            duration > 0 ? (result.getTotalCount() * 1000.0) / duration : 0);
        log.info("├─────────────────────────────────┼─────────────────────────────────────────────┤");
        log.info("│ 生成账单数                      │ {} 个                                       │", result.getBillsGenerated());
        log.info("│ 审核账单数                      │ {} 个                                       │", result.getBillsAudited());
        log.info("│ 账单审核率                      │ {:.2f}%                                     │",
            result.getBillsGenerated() > 0 ? (result.getBillsAudited() * 100.0) / result.getBillsGenerated() : 0);
        log.info("│ 处理支付数量                    │ {} 个                                       │", result.getPaymentsProcessed());
        log.info("│ 支付成功数量                    │ {} 个                                       │", result.getPaymentsSucceeded());
        log.info("│ 支付失败数量                    │ {} 个                                       │", result.getPaymentsFailed());
        log.info("│ 支付成功率                      │ {:.2f}%                                     │",
            result.getPaymentsProcessed() > 0 ? (result.getPaymentsSucceeded() * 100.0) / result.getPaymentsProcessed() : 0);
        log.info("└─────────────────────────────────┴─────────────────────────────────────────────┘");

        // 输出JSON格式的性能指标
        log.info("");
        log.info("📋 JSON格式性能指标:");
        log.info("```json");
        log.info("{{");
        log.info("  \"timestamp\": \"{}\",", java.time.LocalDateTime.now());
        log.info("  \"testResult\": {{");
        log.info("    \"totalProcessed\": {},", result.getTotalCount());
        log.info("    \"successCount\": {},", result.getSuccessCount());
        log.info("    \"failCount\": {},", result.getFailCount());
        log.info("    \"successRate\": {:.2f},",
            result.getTotalCount() > 0 ? (result.getSuccessCount() * 100.0) / result.getTotalCount() : 0);
        log.info("    \"processingSpeed\": {:.2f},",
            duration > 0 ? (result.getTotalCount() * 1000.0) / duration : 0);
        log.info("    \"duration\": {}", duration);
        log.info("  }},");
        log.info("  \"billingStats\": {{");
        log.info("    \"billsGenerated\": {},", result.getBillsGenerated());
        log.info("    \"billsAudited\": {},", result.getBillsAudited());
        log.info("    \"paymentsProcessed\": {},", result.getPaymentsProcessed());
        log.info("    \"paymentsSucceeded\": {},", result.getPaymentsSucceeded());
        log.info("    \"paymentsFailed\": {}", result.getPaymentsFailed());
        log.info("  }}");
        log.info("}}");
        log.info("```");

        // 验证性能指标
        assert result.getTotalCount() == 1000;
        assert result.getSuccessCount() > 0;
        assert duration < 60000; // 应在1分钟内完成

        log.info("性能指标输出功能测试完成");
    }

    /**
     * 生成测试用的水表编号列表
     */
    private List<String> generateTestMeterNos(int count, int startIndex) {
        List<String> meterNos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            meterNos.add("TEST_METER_" + String.format("%08d", startIndex + i));
        }
        return meterNos;
    }
}
