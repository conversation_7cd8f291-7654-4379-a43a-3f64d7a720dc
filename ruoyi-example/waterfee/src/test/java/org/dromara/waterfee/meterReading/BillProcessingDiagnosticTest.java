package org.dromara.waterfee.meterReading;

import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.service.MeterReadingBatchService;
import org.dromara.waterfee.meterReading.service.SimpleBillProcessingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 账单处理诊断测试
 * 用于诊断和验证账单处理中支付成功率为0的问题
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class BillProcessingDiagnosticTest {

    @Autowired
    private MeterReadingBatchService batchService;

    @Autowired
    private SimpleBillProcessingService simpleBillProcessingService;

    /**
     * 诊断账单处理问题
     */
    @Test
    public void diagnoseBillProcessingIssue() {
        log.info("开始诊断账单处理问题...");

        // 使用少量测试数据
        List<String> testMeterNos = Arrays.asList(
            "TEST_METER_00000001", "TEST_METER_00000002", 
            "TEST_METER_00000003", "TEST_METER_00000004",
            "TEST_METER_00000005", "TEST_METER_00000006"
        );

        log.info("使用测试水表编号: {}", testMeterNos);

        try {
            // 执行批量处理
            MeterReadingBatchService.BatchProcessResult result = 
                batchService.batchProcessMeterReadings(testMeterNos, 1L, new Date());

            // 输出详细结果
            log.info("=".repeat(80));
            log.info("                    账单处理诊断结果");
            log.info("=".repeat(80));
            log.info("处理水表数量: {}", testMeterNos.size());
            log.info("成功处理数量: {}", result.getSuccessCount());
            log.info("失败处理数量: {}", result.getFailCount());
            log.info("账单生成数量: {}", result.getBillsGenerated());
            log.info("账单审核数量: {}", result.getBillsAudited());
            log.info("支付处理数量: {}", result.getPaymentsProcessed());
            log.info("支付成功数量: {}", result.getPaymentsSucceeded());
            log.info("支付失败数量: {}", result.getPaymentsFailed());
            log.info("支付成功率: {:.2f}%", 
                result.getPaymentsProcessed() > 0 ? 
                (result.getPaymentsSucceeded() * 100.0) / result.getPaymentsProcessed() : 0);
            log.info("=".repeat(80));

            // 分析结果
            analyzeResults(result, testMeterNos.size());

        } catch (Exception e) {
            log.error("诊断过程中发生异常", e);
        }
    }

    /**
     * 分析处理结果
     */
    private void analyzeResults(MeterReadingBatchService.BatchProcessResult result, int totalMeters) {
        log.info("结果分析:");

        // 检查账单生成
        if (result.getBillsGenerated() == 0) {
            log.error("❌ 问题：没有生成任何账单");
            log.error("   可能原因：");
            log.error("   1. 测试水表不存在");
            log.error("   2. 抄表记录创建失败");
            log.error("   3. 账单审核服务异常");
        } else if (result.getBillsGenerated() < totalMeters) {
            log.warn("⚠️ 警告：账单生成数量({})少于水表数量({})", result.getBillsGenerated(), totalMeters);
        } else {
            log.info("✅ 账单生成正常：{} 个", result.getBillsGenerated());
        }

        // 检查账单审核
        if (result.getBillsAudited() < result.getBillsGenerated()) {
            log.warn("⚠️ 警告：账单审核数量({})少于生成数量({})", 
                result.getBillsAudited(), result.getBillsGenerated());
        } else {
            log.info("✅ 账单审核正常：{} 个", result.getBillsAudited());
        }

        // 检查支付处理
        if (result.getPaymentsProcessed() == 0) {
            log.error("❌ 问题：没有处理任何支付");
            log.error("   可能原因：");
            log.error("   1. 没有智能表记录（meter_type != 2）");
            log.error("   2. 水表类型字段为空或错误");
            log.error("   3. 自动支付功能被禁用");
            log.error("   建议：");
            log.error("   1. 检查数据库中水表的 meter_type 字段");
            log.error("   2. 运行 test-data/meter-test-data.sql 初始化测试数据");
        } else if (result.getPaymentsSucceeded() == 0) {
            log.error("❌ 问题：支付处理全部失败");
            log.error("   可能原因：");
            log.error("   1. 用户余额不足");
            log.error("   2. 支付服务异常");
            log.error("   3. 账单状态异常");
            log.error("   建议：");
            log.error("   1. 检查用户余额是否充足");
            log.error("   2. 检查支付服务日志");
            log.error("   3. 检查账单状态是否为未支付(0)");
        } else {
            double successRate = (result.getPaymentsSucceeded() * 100.0) / result.getPaymentsProcessed();
            if (successRate >= 90) {
                log.info("✅ 支付处理正常：成功率 {:.2f}%", successRate);
            } else {
                log.warn("⚠️ 支付成功率偏低：{:.2f}%", successRate);
            }
        }

        // 总体评估
        if (result.getBillsGenerated() > 0 && result.getPaymentsSucceeded() > 0) {
            log.info("🎉 账单处理功能基本正常！");
        } else {
            log.error("💥 账单处理存在严重问题，需要修复！");
        }
    }

    /**
     * 测试直接的账单处理服务
     */
    @Test
    public void testDirectBillProcessing() {
        log.info("开始测试直接账单处理服务...");

        // 创建测试记录
        List<WaterfeeMeterReadingRecord> testRecords = createTestRecords();

        if (testRecords.isEmpty()) {
            log.warn("没有创建测试记录，跳过测试");
            return;
        }

        try {
            // 直接调用账单处理服务
            SimpleBillProcessingService.BillProcessingResult result = 
                simpleBillProcessingService.optimizedBatchProcessBills(testRecords);

            // 输出结果
            log.info("直接账单处理结果:");
            log.info("- 总记录数: {}", result.getTotalRecords());
            log.info("- 账单生成: {}", result.getBillsGenerated());
            log.info("- 账单审核: {}", result.getBillsAudited());
            log.info("- 支付处理: {}", result.getPaymentsProcessed());
            log.info("- 支付成功: {}", result.getPaymentsSucceeded());
            log.info("- 支付失败: {}", result.getPaymentsFailed());
            log.info("- 处理时间: {} ms", result.getProcessingTime());

            if (!result.getErrorMessages().isEmpty()) {
                log.warn("处理过程中的错误:");
                result.getErrorMessages().forEach(error -> log.warn("- {}", error));
            }

        } catch (Exception e) {
            log.error("直接账单处理测试异常", e);
        }
    }

    /**
     * 创建测试记录
     */
    private List<WaterfeeMeterReadingRecord> createTestRecords() {
        // 这里应该创建一些测试用的抄表记录
        // 由于需要依赖数据库中的实际数据，这里返回空列表
        // 在实际使用时，可以根据需要创建测试数据
        log.info("注意：此方法需要根据实际数据库数据进行实现");
        return Arrays.asList();
    }

    /**
     * 数据库连接测试
     */
    @Test
    public void testDatabaseConnection() {
        log.info("测试数据库连接和基础数据...");

        try {
            // 这里可以添加一些基础的数据库查询测试
            log.info("数据库连接测试通过");
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
        }
    }
}
