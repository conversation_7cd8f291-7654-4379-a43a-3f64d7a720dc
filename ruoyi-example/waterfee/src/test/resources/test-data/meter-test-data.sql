-- 测试用水表数据初始化脚本
-- 用于确保账单处理优化测试中有正确的智能表和机械表数据

-- 清理测试数据
DELETE FROM waterfee_meter WHERE meter_no LIKE 'TEST_METER_%';
DELETE FROM waterfee_user WHERE user_name LIKE 'TEST_USER_%';

-- 插入测试用户数据
INSERT INTO waterfee_user (user_id, user_name, phone_number, address, balance, create_time, update_time, del_flag, tenant_id) VALUES
(10001, 'TEST_USER_001', '13800000001', '测试地址001', 1000.00, NOW(), NOW(), '0', '000000'),
(10002, 'TEST_USER_002', '13800000002', '测试地址002', 1500.00, NOW(), NOW(), '0', '000000'),
(10003, 'TEST_USER_003', '13800000003', '测试地址003', 2000.00, NOW(), NOW(), '0', '000000'),
(10004, 'TEST_USER_004', '13800000004', '测试地址004', 500.00, NOW(), NOW(), '0', '000000'),
(10005, 'TEST_USER_005', '13800000005', '测试地址005', 3000.00, NOW(), NOW(), '0', '000000');

-- 插入测试水表数据（智能表和机械表混合）
-- 智能表（meter_type = 2）
INSERT INTO waterfee_meter (meter_id, meter_no, meter_type, user_id, installation_date, status, create_time, update_time, del_flag, tenant_id) VALUES
(20001, 'TEST_METER_00000001', 2, 10001, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20002, 'TEST_METER_00000002', 2, 10002, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20003, 'TEST_METER_00000003', 2, 10003, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20004, 'TEST_METER_00000004', 2, 10004, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20005, 'TEST_METER_00000005', 2, 10005, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),

-- 机械表（meter_type = 1）
(20006, 'TEST_METER_00000006', 1, 10001, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20007, 'TEST_METER_00000007', 1, 10002, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20008, 'TEST_METER_00000008', 1, 10003, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20009, 'TEST_METER_00000009', 1, 10004, '2024-01-01', 1, NOW(), NOW(), '0', '000000'),
(20010, 'TEST_METER_00000010', 1, 10005, '2024-01-01', 1, NOW(), NOW(), '0', '000000');

-- 验证数据插入
SELECT 
    '智能表数量' as type, 
    COUNT(*) as count 
FROM waterfee_meter 
WHERE meter_no LIKE 'TEST_METER_%' AND meter_type = 2

UNION ALL

SELECT 
    '机械表数量' as type, 
    COUNT(*) as count 
FROM waterfee_meter 
WHERE meter_no LIKE 'TEST_METER_%' AND meter_type = 1

UNION ALL

SELECT 
    '用户数量' as type, 
    COUNT(*) as count 
FROM waterfee_user 
WHERE user_name LIKE 'TEST_USER_%';

-- 显示测试数据详情
SELECT 
    m.meter_no,
    m.meter_type,
    CASE m.meter_type 
        WHEN 1 THEN '机械表'
        WHEN 2 THEN '智能表'
        ELSE '未知'
    END as meter_type_name,
    u.user_name,
    u.balance
FROM waterfee_meter m
LEFT JOIN waterfee_user u ON m.user_id = u.user_id
WHERE m.meter_no LIKE 'TEST_METER_%'
ORDER BY m.meter_no;
