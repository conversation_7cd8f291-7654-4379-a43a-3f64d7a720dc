package org.dromara.waterfee.meterReading.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
// import org.dromara.waterfee.meterReading.mapper.BillProcessingBatchMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 账单处理优化服务
 * 专门用于优化账单审核和支付处理的性能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillProcessingOptimizationService {

    private final IMeterReadingRecordService meterReadingRecordService;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeCounterPaymentService waterfeeCounterPaymentService;
    // 暂时注释掉可能有问题的依赖
    // private final BillProcessingBatchMapper billProcessingBatchMapper;

    // 批处理配置
    private static final int BILL_BATCH_SIZE = 200; // 账单批处理大小
    private static final int PAYMENT_BATCH_SIZE = 100; // 支付批处理大小
    private static final int MAX_CONCURRENT_BATCHES = 5; // 最大并发批次数

    /**
     * 优化的批量账单处理
     */
    public BillProcessingResult optimizedBatchProcessBills(List<WaterfeeMeterReadingRecord> records) {
        long startTime = System.currentTimeMillis();
        BillProcessingResult result = new BillProcessingResult();
        result.setTotalRecords(records.size());

        try {
            log.info("开始优化的批量账单处理，记录数量: {}", records.size());

            // 1. 分组处理：智能表和机械表
            Map<Boolean, List<WaterfeeMeterReadingRecord>> groupedRecords = records.stream()
                .collect(Collectors.groupingBy(record -> "2".equals(record.getMeterType())));

            List<WaterfeeMeterReadingRecord> intelligentRecords = groupedRecords.getOrDefault(true, new ArrayList<>());
            List<WaterfeeMeterReadingRecord> mechanicalRecords = groupedRecords.getOrDefault(false, new ArrayList<>());

            // 2. 并行处理智能表和机械表
            CompletableFuture<BillProcessingResult> intelligentFuture = CompletableFuture.supplyAsync(() ->
                processIntelligentMeterBillsOptimized(intelligentRecords));

            CompletableFuture<BillProcessingResult> mechanicalFuture = CompletableFuture.supplyAsync(() ->
                processMechanicalMeterBillsOptimized(mechanicalRecords));

            // 3. 等待并合并结果
            BillProcessingResult intelligentResult = intelligentFuture.join();
            BillProcessingResult mechanicalResult = mechanicalFuture.join();

            mergeResults(result, intelligentResult, mechanicalResult);

            result.setProcessingTime(System.currentTimeMillis() - startTime);

            log.info("优化的批量账单处理完成 - 总数: {}, 账单生成: {}, 审核: {}, 支付成功: {}, 耗时: {}ms",
                result.getTotalRecords(), result.getBillsGenerated(), result.getBillsAudited(),
                result.getPaymentsSucceeded(), result.getProcessingTime());

        } catch (Exception e) {
            log.error("优化的批量账单处理异常", e);
            result.getErrorMessages().add("批量账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 优化的智能表账单处理
     */
    private BillProcessingResult processIntelligentMeterBillsOptimized(List<WaterfeeMeterReadingRecord> records) {
        BillProcessingResult result = new BillProcessingResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始优化处理智能表账单，数量: {}", records.size());

        try {
            // 1. 批量审核记录并获取账单ID
            Map<Long, List<Long>> recordBillMap = batchAuditRecordsAndGetBillIds(records);

            // 2. 批量获取账单信息
            Set<Long> allBillIds = recordBillMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

            Map<Long, WaterfeeBillVo> billMap = batchGetBillInfo(new ArrayList<>(allBillIds));

            // 3. 批量处理自动支付
            result = batchProcessAutoPayments(records, recordBillMap, billMap);

            // 4. 统计账单生成和审核
            result.setBillsGenerated(recordBillMap.size());
            result.setBillsAudited(recordBillMap.size());

        } catch (Exception e) {
            log.error("智能表账单处理优化异常", e);
            result.getErrorMessages().add("智能表账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 优化的机械表账单处理
     */
    private BillProcessingResult processMechanicalMeterBillsOptimized(List<WaterfeeMeterReadingRecord> records) {
        BillProcessingResult result = new BillProcessingResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始优化处理机械表账单，数量: {}", records.size());

        try {
            // 批量审核记录（机械表不需要自动支付）
            Map<Long, List<Long>> recordBillMap = batchAuditRecordsAndGetBillIds(records);

            result.setBillsGenerated(recordBillMap.size());
            result.setBillsAudited(recordBillMap.size());

            log.info("机械表账单处理完成，生成账单: {}, 审核账单: {}",
                result.getBillsGenerated(), result.getBillsAudited());

        } catch (Exception e) {
            log.error("机械表账单处理优化异常", e);
            result.getErrorMessages().add("机械表账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量审核记录并获取账单ID
     */
    private Map<Long, List<Long>> batchAuditRecordsAndGetBillIds(List<WaterfeeMeterReadingRecord> records) {
        Map<Long, List<Long>> recordBillMap = new ConcurrentHashMap<>();

        // 分批处理以避免单次处理过多记录
        List<List<WaterfeeMeterReadingRecord>> batches = partitionList(records, BILL_BATCH_SIZE);

        List<CompletableFuture<Void>> futures = batches.stream()
            .map(batch -> CompletableFuture.runAsync(() -> {
                processBillAuditBatch(batch, recordBillMap);
            }))
            .toList();

        // 等待所有批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return recordBillMap;
    }

    /**
     * 处理账单审核批次
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processBillAuditBatch(List<WaterfeeMeterReadingRecord> batch, Map<Long, List<Long>> recordBillMap) {
        for (WaterfeeMeterReadingRecord record : batch) {
            try {
                List<Long> billIds = meterReadingRecordService.auditRecordRtnBillIds(record);
                if (billIds != null && !billIds.isEmpty()) {
                    recordBillMap.put(record.getRecordId(), billIds);
                }
            } catch (Exception e) {
                log.error("审核记录失败，记录ID: {}", record.getRecordId(), e);
            }
        }
    }

    /**
     * 批量获取账单信息
     */
    private Map<Long, WaterfeeBillVo> batchGetBillInfo(List<Long> billIds) {
        if (billIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 暂时使用现有服务逐个查询，后续可以优化为批量查询
            Map<Long, WaterfeeBillVo> billMap = new HashMap<>();
            for (Long billId : billIds) {
                try {
                    WaterfeeBillVo bill = waterfeeBillService.queryById(billId);
                    if (bill != null) {
                        billMap.put(billId, bill);
                    }
                } catch (Exception e) {
                    log.warn("获取账单信息失败，账单ID: {}", billId, e);
                }
            }
            return billMap;
        } catch (Exception e) {
            log.error("批量获取账单信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量处理自动支付
     */
    private BillProcessingResult batchProcessAutoPayments(List<WaterfeeMeterReadingRecord> records,
                                                          Map<Long, List<Long>> recordBillMap,
                                                          Map<Long, WaterfeeBillVo> billMap) {
        BillProcessingResult result = new BillProcessingResult();

        // 过滤出需要自动支付的记录
        List<WaterfeeMeterReadingRecord> paymentRecords = records.stream()
            .filter(record -> "1".equals(record.getIsAudited()))
            .filter(record -> recordBillMap.containsKey(record.getRecordId()))
            .collect(Collectors.toList());

        if (paymentRecords.isEmpty()) {
            return result;
        }

        log.info("开始批量处理自动支付，数量: {}", paymentRecords.size());

        // 分批处理支付
        List<List<WaterfeeMeterReadingRecord>> paymentBatches = partitionList(paymentRecords, PAYMENT_BATCH_SIZE);

        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger totalSucceeded = new AtomicInteger(0);
        AtomicInteger totalFailed = new AtomicInteger(0);

        List<CompletableFuture<Void>> futures = paymentBatches.stream()
            .map(batch -> CompletableFuture.runAsync(() -> {
                processPaymentBatch(batch, recordBillMap, billMap, totalProcessed, totalSucceeded, totalFailed);
            }))
            .toList();

        // 等待所有支付批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        result.setPaymentsProcessed(totalProcessed.get());
        result.setPaymentsSucceeded(totalSucceeded.get());
        result.setPaymentsFailed(totalFailed.get());

        log.info("批量自动支付完成，处理: {}, 成功: {}, 失败: {}",
            totalProcessed.get(), totalSucceeded.get(), totalFailed.get());

        return result;
    }

    /**
     * 处理支付批次
     */
    private void processPaymentBatch(List<WaterfeeMeterReadingRecord> batch,
                                     Map<Long, List<Long>> recordBillMap,
                                     Map<Long, WaterfeeBillVo> billMap,
                                     AtomicInteger totalProcessed,
                                     AtomicInteger totalSucceeded,
                                     AtomicInteger totalFailed) {

        for (WaterfeeMeterReadingRecord record : batch) {
            try {
                List<Long> billIds = recordBillMap.get(record.getRecordId());
                if (billIds == null || billIds.isEmpty()) {
                    continue;
                }

                WaterfeeBillVo bill = billMap.get(billIds.get(0));
                if (bill == null) {
                    totalFailed.incrementAndGet();
                    continue;
                }

                totalProcessed.incrementAndGet();

                boolean success = waterfeeCounterPaymentService.autoPayBillByDeposit(
                    bill.getCustomerId(),
                    billIds,
                    bill.getTotalAmount(),
                    "智能表自动扣款：" + bill.getBillNumber()
                );

                if (success) {
                    totalSucceeded.incrementAndGet();
                    log.debug("自动支付成功，水表编号: {}, 账单ID: {}", record.getMeterNo(), billIds);
                } else {
                    totalFailed.incrementAndGet();
                    log.warn("自动支付失败，水表编号: {}, 账单ID: {}", record.getMeterNo(), billIds);
                }

            } catch (Exception e) {
                totalFailed.incrementAndGet();
                log.error("自动支付异常，水表编号: {}", record.getMeterNo(), e);
            }
        }
    }

    /**
     * 合并处理结果
     */
    private void mergeResults(BillProcessingResult target, BillProcessingResult... sources) {
        for (BillProcessingResult source : sources) {
            target.setBillsGenerated(target.getBillsGenerated() + source.getBillsGenerated());
            target.setBillsAudited(target.getBillsAudited() + source.getBillsAudited());
            target.setPaymentsProcessed(target.getPaymentsProcessed() + source.getPaymentsProcessed());
            target.setPaymentsSucceeded(target.getPaymentsSucceeded() + source.getPaymentsSucceeded());
            target.setPaymentsFailed(target.getPaymentsFailed() + source.getPaymentsFailed());
            target.getErrorMessages().addAll(source.getErrorMessages());
        }
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 账单处理结果
     */
    @Data
    public static class BillProcessingResult {
        private int totalRecords = 0;
        private int billsGenerated = 0;
        private int billsAudited = 0;
        private int paymentsProcessed = 0;
        private int paymentsSucceeded = 0;
        private int paymentsFailed = 0;
        private long processingTime = 0;
        private List<String> errorMessages = new ArrayList<>();
    }
}
