package org.dromara.waterfee.counter.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.counter.domain.bo.*;
import org.dromara.waterfee.counter.domain.vo.WaterfeeUserInfoVo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 窗口收费管理
 *
 * <AUTHOR> Li
 * @date 2025-05-07
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/counter-payment")
public class WaterfeeCounterPaymentController extends BaseController {

    private final IWaterfeeCounterPaymentService counterPaymentService;
    private final PaymentDetailMapper paymentDetailMapper;

    /**
     * 根据关键字查询用户信息
     *
     * @param keyword 用户编号或姓名
     * @return 用户信息
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/user")
    public R<WaterfeeUserInfoVo> getUserByKeyword(@RequestParam String keyword) {
        return R.ok(counterPaymentService.getUserByKeyword(keyword));
    }

    /**
     * 根据用户ID查询详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/user/detail")
    public R<WaterfeeUser> getUserById(@RequestParam Long userId) {
        return R.ok(counterPaymentService.getUserById(userId));
    }

    /**
     * 根据关键字模糊查询用户列表
     *
     * @param keyword 关键字
     * @return 用户列表
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/users/search")
    public R<List<WaterfeeUser>> searchUsersByKeyword(@RequestParam String keyword) {
        return R.ok(counterPaymentService.searchUsersByKeyword(keyword));
    }

    /**
     * 获取用户账单列表
     *
     * @param userId 用户id
     * @return 账单列表
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/bills")
    public R<List<WaterfeeBillVo>> getUserBills(@RequestParam Long userId, @RequestParam(required = false) String status, @RequestParam(required = false) String month) {
        return R.ok(counterPaymentService.getUserBills(userId, status, month));
    }

    /**
     * 获取用户缴费明细
     *
     * @param userId    用户id
     * @param billMonth 账单月份（可选，格式：YYYY-MM）
     * @return 缴费明细列表
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/payment-details")
    public R<List<PaymentDetail>> getUserPaymentDetails(@RequestParam Long userId, @RequestParam(required = false) String billMonth) {
        if (billMonth != null && !billMonth.isEmpty()) {
            return R.ok(counterPaymentService.getUserPaymentDetailsByMonth(userId, billMonth));
        } else {
            return R.ok(counterPaymentService.getUserPaymentDetails(userId));
        }
    }

//    /**
//     * 获取用户抄表记录
//     *
//     * @param userNo 用户编号
//     * @return 抄表记录列表
//     */
//    @SaCheckPermission("waterfee:counter:query")
//    @GetMapping("/meter-readings")
//    public R<List<WaterfeeMeterReadingVo>> getUserMeterReadings(@RequestParam String userNo) {
//        return R.ok(counterPaymentService.getUserMeterReadings(userNo));
//    }

    /**
     * 缴费
     *
     * @param bo 缴费数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:pay")
    @Log(title = "窗口收费-缴费", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/pay")
    public R<Void> payBills(@Validated(AddGroup.class) @RequestBody WaterfeePayBillsBo bo) {
        return toAjax(counterPaymentService.payBills(bo));
    }

    /**
     * 预存充值
     *
     * @param bo 充值数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:deposit")
    @Log(title = "窗口收费-预存充值", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/deposit")
    public R<Void> addDeposit(@Validated(AddGroup.class) @RequestBody WaterfeeDepositBo bo) {
        return toAjax(counterPaymentService.addDeposit(bo));
    }

    /**
     * 账单调整（支持调整用量、读数、附加费、违约金）
     *
     * @param bo 调整数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:adjust")
    @Log(title = "窗口收费-账单调整", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/adjust")
    public R<Void> adjustConsumption(@Validated @RequestBody WaterfeeAdjustConsumptionBo bo) {
        return toAjax(counterPaymentService.adjustConsumption(bo));
    }

    /**
     * 账单退费
     *
     * @param bo 退费数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:refund")
    @Log(title = "窗口收费-账单退费", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/refund")
    public R<Void> refundBill(@Validated @RequestBody WaterfeeRefundBo bo) {
        return toAjax(counterPaymentService.refundBill(bo));
    }

    /**
     * 计算账单调整结果（不保存数据）
     *
     * @param bo 调整数据
     * @return 计算结果
     */
    @SaCheckPermission("waterfee:counter:adjust")
    @PostMapping("/calculate-adjustment")
    public R<Map<String, Object>> calculateAdjustment(@RequestBody WaterfeeAdjustConsumptionBo bo) {
        return R.ok(counterPaymentService.calculateAdjustment(bo));
    }

    /**
     * 实收账冲正
     *
     * @param bo 冲正数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:reversal")
    @Log(title = "窗口收费-实收账冲正", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/reversal")
    public R<Void> reversalPayment(@Validated(AddGroup.class) @RequestBody WaterfeeReversalBo bo) {
        return toAjax(counterPaymentService.reversalPayment(bo));
    }

    /**
     * 批量实收账冲正
     *
     * @param paymentDetailIds 收费明细ID列表
     * @param reversalReason   冲正原因
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:batch-reversal")
    @Log(title = "窗口收费-批量冲正", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batch-reversal")
    public R<Void> batchReversalPayment(@RequestParam List<String> paymentDetailIds,
                                        @RequestParam String reversalReason) {
        return toAjax(counterPaymentService.batchReversalPayment(paymentDetailIds, reversalReason));
    }

    /**
     * 预存款退款
     *
     * @param bo 退款数据
     * @return 结果
     */
    @SaCheckPermission("waterfee:counter:deposit-refund")
    @Log(title = "窗口收费-预存款退款", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/deposit-refund")
    public R<Void> refundDeposit(@Validated(AddGroup.class) @RequestBody WaterfeeDepositRefundBo bo) {
        return toAjax(counterPaymentService.refundDeposit(bo));
    }

    /**
     * 获取用户退款记录
     *
     * @param userId 用户ID
     * @return 退款记录列表
     */
    @SaCheckPermission("waterfee:counter:query")
    @GetMapping("/refund-records")
    public R<List<PaymentDetail>> getUserRefundRecords(@RequestParam Long userId) {
        LambdaQueryWrapper<PaymentDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentDetail::getUserId, userId)
            .in(PaymentDetail::getIncomeType, "deposit_refund", "reversal")
            .lt(PaymentDetail::getPaymentAmount, BigDecimal.ZERO) // 负数金额
            .orderByDesc(PaymentDetail::getPaymentTime);

        List<PaymentDetail> refundRecords = paymentDetailMapper.selectList(wrapper);
        return R.ok(refundRecords);
    }


    /**
     * 微信预存款支付账单
     *
     * @param bo 支付参数
     * @return 支付结果
     */
    @Log(title = "微信预存款支付", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/wechat/pay")
    public R<Void> wechatPayBill(@Validated(AddGroup.class) @RequestBody WechatPayBillBo bo) {
        return toAjax(counterPaymentService.wechatPayBillByDeposit(bo));
    }
}
