package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageNonOperatingIncomeVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageNonOperatingIncomeBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageNonOperatingIncomeService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 营业外收入记录
 * 前端访问路由地址为:/waterfee/chargeManageNonOperatingIncome
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageNonOperatingIncome")
public class WaterfeeChargeManageNonOperatingIncomeController extends BaseController {

    private final IWaterfeeChargeManageNonOperatingIncomeService waterfeeChargeManageNonOperatingIncomeService;

    /**
     * 查询营业外收入记录列表
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageNonOperatingIncomeVo> list(WaterfeeChargeManageNonOperatingIncomeBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageNonOperatingIncomeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出营业外收入记录列表
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:export")
    @Log(title = "营业外收入记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageNonOperatingIncomeBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageNonOperatingIncomeVo> list = waterfeeChargeManageNonOperatingIncomeService.queryList(bo);
        ExcelUtil.exportExcel(list, "营业外收入记录", WaterfeeChargeManageNonOperatingIncomeVo.class, response);
    }

    /**
     * 获取营业外收入记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageNonOperatingIncome> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageNonOperatingIncomeService.queryById(id));
    }

    /**
     * 新增营业外收入记录
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:add")
    @Log(title = "营业外收入记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageNonOperatingIncomeBo bo) {
        return toAjax(waterfeeChargeManageNonOperatingIncomeService.insertByBo(bo));
    }

    /**
     * 修改营业外收入记录
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:edit")
    @Log(title = "营业外收入记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageNonOperatingIncomeBo bo) {
        return toAjax(waterfeeChargeManageNonOperatingIncomeService.updateByBo(bo));
    }

    /**
     * 删除营业外收入记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageNonOperatingIncome:remove")
    @Log(title = "营业外收入记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageNonOperatingIncomeService.deleteWithValidByIds(List.of(ids), true));
    }
}
