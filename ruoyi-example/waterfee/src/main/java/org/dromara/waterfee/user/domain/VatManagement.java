package org.dromara.waterfee.user.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 增值税管理对象 vat_management
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("vat_management")
public class VatManagement extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联账单编号
     */
    private String billNumber;

    /**
     * 发票类型（普通/专用）（字典invoice_type）
     */
    private String invoiceType;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 发票状态（已开具/已红冲/已作废）（字典invoice_status）
     */
    private String invoiceStatus;

    /**
     * 价税合计金额
     */
    private Long totalAmount;

    /**
     * 不含税金额
     */
    private Long taxExclusiveAmount;

    /**
     * 税率（如0.13）
     */
    private Long taxRate;

    /**
     * 税额
     */
    private Long taxAmount;

    /**
     * 是否含税（1含税/0不含税）（字典yes_no）
     */
    private String isTaxIncluded;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方纳税人识别号
     */
    private String buyerTaxId;

    /**
     * 购方电话
     */
    private String buyerTel;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方开户行及账号
     */
    private String buyerBankAccount;

    /**
     * 销方名称
     */
    private String sellerName;

    /**
     * 销方纳税人识别号
     */
    private String sellerTaxId;

    /**
     * 销方电话
     */
    private String sellerTel;

    /**
     * 销方地址
     */
    private String sellerAddress;

    /**
     * 销方开户行及账号
     */
    private String sellerBankAccount;

    /**
     * 开票时间
     */
    private Date issueTime;

    /**
     * 红冲时间
     */
    private Date redFlushTime;

    /**
     * 作废时间
     */
    private Date cancelTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 红冲原因
     */
    private String redFlushReason;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
