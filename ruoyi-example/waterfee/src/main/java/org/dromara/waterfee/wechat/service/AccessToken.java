package org.dromara.waterfee.wechat.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.WxAccessToken;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;

@Slf4j
@Service
public class AccessToken {

    @Value("${wechat.appId}")
    private String appId;

    @Value("${wechat.appSecret}")
    private String appSecret;


    public String getWechatAccessToken() {
        // 先从Redis获取
        String accessToken = RedisUtils.getCacheObject("wechat_access_token");
        if (StringUtils.hasText(accessToken)) {
            return accessToken;
        }

        // Redis中没有则调用接口获取
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
        try {
            String response = HttpUtil.createGet(url).execute().body();
            WxAccessToken wxAccessToken = JsonUtils.parseObject(response, WxAccessToken.class);
            if (wxAccessToken != null && StringUtils.hasText(wxAccessToken.getAccess_token())) {
                // 存储到Redis，设置2小时过期
                RedisUtils.setCacheObject("wechat_access_token", wxAccessToken.getAccess_token(), Duration.ofSeconds(7200));
                return wxAccessToken.getAccess_token();
            }
        } catch (Exception e) {
            log.error("获取微信accessToken失败", e);
        }
        return null;
    }


}
