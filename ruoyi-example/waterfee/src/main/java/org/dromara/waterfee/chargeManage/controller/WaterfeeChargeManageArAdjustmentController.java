package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArAdjustmentBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageArAdjustmentService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 应收账追补记录
 * 前端访问路由地址为:/waterfee/chargeManageArAdjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageArAdjustment")
public class WaterfeeChargeManageArAdjustmentController extends BaseController {

    private final IWaterfeeChargeManageArAdjustmentService waterfeeChargeManageArAdjustmentService;

    /**
     * 查询应收账追补记录列表
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageArAdjustmentVo> list(WaterfeeChargeManageArAdjustmentBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageArAdjustmentService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应收账追补记录列表
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:export")
    @Log(title = "应收账追补记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageArAdjustmentBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageArAdjustmentVo> list = waterfeeChargeManageArAdjustmentService.queryList(bo);
        ExcelUtil.exportExcel(list, "应收账追补记录", WaterfeeChargeManageArAdjustmentVo.class, response);
    }

    /**
     * 获取应收账追补记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageArAdjustment> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageArAdjustmentService.queryById(id));
    }

    /**
     * 新增应收账追补记录
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:add")
    @Log(title = "应收账追补记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageArAdjustmentBo bo) {
        return toAjax(waterfeeChargeManageArAdjustmentService.insertByBo(bo));
    }

    /**
     * 修改应收账追补记录
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:edit")
    @Log(title = "应收账追补记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageArAdjustmentBo bo) {
        return toAjax(waterfeeChargeManageArAdjustmentService.updateByBo(bo));
    }

    /**
     * 删除应收账追补记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageArAdjustment:remove")
    @Log(title = "应收账追补记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageArAdjustmentService.deleteWithValidByIds(List.of(ids), true));
    }
}
