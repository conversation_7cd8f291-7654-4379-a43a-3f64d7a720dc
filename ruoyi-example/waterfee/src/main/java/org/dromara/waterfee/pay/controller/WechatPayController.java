package org.dromara.waterfee.pay.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.pay.domain.WechatPrePayParam;
import org.dromara.waterfee.pay.service.IWechatJsapiPaymentService;
import org.dromara.waterfee.pay.service.IWechatRefundService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechatPay")
public class WechatPayController extends BaseController {

    private final IWechatRefundService wechatRefundService;

    private final IWechatJsapiPaymentService wechatJsapiPaymentService;

//    @SaCheckPermission("wechat:pay:prepay")
    @PostMapping("/prepay")
    public R<PrepayWithRequestPaymentResponse> prepayWithRequestPayment(HttpServletRequest request, HttpServletResponse response, @RequestBody WechatPrePayParam wechatPrePayParam) {
        return R.ok(wechatJsapiPaymentService.prepayWithRequestPayment(request, response, wechatPrePayParam));
    }

    @PostMapping("/pay/callBack")
    public R<String> wechatPayCallBack(HttpServletRequest request, HttpServletResponse response) {
        return R.ok(wechatJsapiPaymentService.wechatPayCallBack(request, response));
    }

    @PostMapping("/refund/callBack")
    public R<String> wechatRefundCallBack(HttpServletRequest request, HttpServletResponse response) {
        return R.ok(wechatRefundService.wechatRefundCallBack(request, response));
    }

}
