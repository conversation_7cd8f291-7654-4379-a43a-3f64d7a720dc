package org.dromara.waterfee.invoice.mapper;

import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.invoice.domain.NuonuoToken;
import org.dromara.waterfee.invoice.domain.vo.NuonuoTokenVo;

/**
 * 诺诺发票平台访问令牌Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface NuonuoTokenMapper extends BaseMapperPlus<NuonuoToken, NuonuoTokenVo> {

    /**
     * 查询最新的有效令牌
     *
     * @return 令牌信息
     */
    NuonuoTokenVo selectLatestValidToken();
}