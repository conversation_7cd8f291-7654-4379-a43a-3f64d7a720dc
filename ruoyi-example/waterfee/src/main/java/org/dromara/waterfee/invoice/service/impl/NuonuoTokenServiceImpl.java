package org.dromara.waterfee.invoice.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.waterfee.invoice.accessToken.NuonuoAccessToken;
import org.dromara.waterfee.invoice.domain.NuonuoToken;
import org.dromara.waterfee.invoice.domain.bo.NuonuoTokenBo;
import org.dromara.waterfee.invoice.domain.vo.NuonuoTokenVo;
import org.dromara.waterfee.invoice.mapper.NuonuoTokenMapper;
import org.dromara.waterfee.invoice.service.INuonuoTokenService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Map;

/**
 * 诺诺发票平台访问令牌Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NuonuoTokenServiceImpl implements INuonuoTokenService {

    private final NuonuoTokenMapper baseMapper;
    private final NuonuoAccessToken nuonuoAccessToken;

    /**
     * Redis缓存键
     */
    private static final String NUONUO_TOKEN_KEY = "nuonuo_invoice_token";

    /**
     * 令牌即将过期的阈值（秒）
     * 当令牌剩余有效期小于此值时，视为即将过期，需要重新获取
     */
    private static final int TOKEN_EXPIRATION_THRESHOLD = 300; // 5分钟

    @Override
    public NuonuoTokenVo getLatestValidToken() {
        return baseMapper.selectLatestValidToken();
    }

    @Override
    public Boolean saveToken(NuonuoTokenBo bo) {
        NuonuoToken entity = new NuonuoToken();
        BeanUtils.copyProperties(bo, entity);
        int rows = baseMapper.insert(entity);
        return rows > 0;
    }

    @Override
    public Boolean invalidateToken(Long tokenId) {
        LambdaUpdateWrapper<NuonuoToken> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(NuonuoToken::getTokenId, tokenId)
            .set(NuonuoToken::getIsValid, 0);
        int rows = baseMapper.update(null, wrapper);
        return rows > 0;
    }

    @Override
    public void initToken() {
        log.info("初始化诺诺发票平台访问令牌");
        try {
            // 先从数据库查询最新的有效令牌
            NuonuoTokenVo tokenVo = getLatestValidToken();

            // 判断是否需要重新获取令牌
            boolean needRefresh = true;
            if (tokenVo != null) {
                Date now = DateUtils.getNowDate();
                // 计算令牌剩余有效期（秒）
                long remainingTime = (tokenVo.getExpireTime().getTime() - now.getTime()) / 1000;

                if (remainingTime > TOKEN_EXPIRATION_THRESHOLD) {
                    // 令牌有效且未接近过期，不需要重新获取
                    needRefresh = false;
                    // 将有效令牌存入Redis
                    storeTokenInRedis(tokenVo.getAccessToken(), remainingTime);
                    log.info("使用数据库中的有效令牌，剩余有效期: {}秒", remainingTime);
                } else {
                    // 令牌即将过期，标记为无效
                    invalidateToken(tokenVo.getTokenId());
                    log.info("令牌即将过期，已标记为无效，剩余有效期: {}秒", remainingTime);
                }
            }

            if (needRefresh) {
                // 重新获取令牌
                refreshToken();
            }
        } catch (Exception e) {
            log.error("初始化诺诺发票平台访问令牌异常", e);
        }
    }

    @Override
    public String getTokenFromRedis() {
        return RedisUtils.getCacheObject(NUONUO_TOKEN_KEY);
    }

    /**
     * 刷新令牌
     */
    private void refreshToken() {
        try {
            // 调用API获取新令牌
            Map<String, Object> tokenMap = nuonuoAccessToken.applyForAccessToken();
            String accessToken = (String) tokenMap.get("access_token");
            Integer expiresIn = (Integer) tokenMap.get("expires_in");

            // 计算过期时间点
            Date now = DateUtils.getNowDate();
            Date expireTime = DateUtils.addSeconds(now, expiresIn);

            // 保存到数据库
            NuonuoTokenBo tokenBo = new NuonuoTokenBo();
            tokenBo.setAccessToken(accessToken);
            tokenBo.setExpiresIn(expiresIn);
            tokenBo.setExpireTime(expireTime);
            tokenBo.setIsValid(1); // 0表示有效
            tokenBo.setCreateTime(now);
            saveToken(tokenBo);

            // 存入Redis
            storeTokenInRedis(accessToken, expiresIn);

            log.info("成功刷新诺诺发票平台访问令牌，有效期: {}秒", expiresIn);
        } catch (Exception e) {
            log.error("刷新诺诺发票平台访问令牌异常", e);
        }
    }

    /**
     * 将令牌存入Redis
     *
     * @param token    访问令牌
     * @param expiresIn 过期时间（秒）
     */
    private void storeTokenInRedis(String token, long expiresIn) {
        // 设置Redis缓存过期时间比令牌实际过期时间少一点，确保Redis中的令牌一定是有效的
        long redisExpireTime = expiresIn - 60; // 提前1分钟过期
        if (redisExpireTime <= 0) {
            redisExpireTime = expiresIn;
        }

        RedisUtils.setCacheObject(NUONUO_TOKEN_KEY, token, Duration.ofSeconds(redisExpireTime));
        log.info("令牌已存入Redis，缓存有效期: {}秒", redisExpireTime);
    }

    /**
     * 获取有效的访问令牌
     * 优先从Redis获取，如果Redis中不存在，则从数据库获取
     *
     * @return 访问令牌
     */
    public String getValidToken() {
        try {
            // 优先从Redis获取
            String token = this.getTokenFromRedis();
            if (token != null && !token.isEmpty()) {
                log.info("从Redis获取到有效的诺诺发票平台访问令牌");
                return token;
            }

            // Redis中不存在，初始化令牌（会检查数据库中的令牌，如果无效会重新获取）
            log.info("Redis中不存在有效的诺诺发票平台访问令牌，开始初始化令牌");
            this.initToken();

            // 再次从Redis获取
            token = this.getTokenFromRedis();
            if (token != null && !token.isEmpty()) {
                log.info("初始化后从Redis获取到有效的诺诺发票平台访问令牌");
                return token;
            }

            // 如果仍然获取不到，则直接调用API获取新令牌
            log.warn("无法获取有效的诺诺发票平台访问令牌");
//            Map<String, Object> tokenMap = applyForAccessToken();
//            return (String) tokenMap.get("access_token");
            throw new ServiceException("无法获取有效的诺诺发票平台访问令牌");
        } catch (Exception e) {
            log.error("获取有效的诺诺发票平台访问令牌异常", e);
            throw new ServiceException("获取有效的诺诺发票平台访问令牌失败: " + e.getMessage());
        }
    }
}
