package org.dromara.waterfee.bill.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j; // Added import
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.PaymentDetailVo;
import org.dromara.waterfee.bill.service.IPaymentDetailService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 缴费明细
 * 前端访问路由地址：/waterfee/paymentDetail
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j // Added annotation
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/paymentDetail") // Adjusted mapping
public class PaymentDetailController extends BaseController {

    private final IPaymentDetailService paymentDetailService;

    /**
     * 查询缴费明细列表
     */
    @SaCheckPermission("waterfee:paymentDetail:list")
    @GetMapping("/list")
    public TableDataInfo<PaymentDetailVo> list(PaymentDetailBo bo, PageQuery pageQuery) {
        return paymentDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出缴费明细列表
     */
    @SaCheckPermission("waterfee:paymentDetail:export")
    @Log(title = "缴费明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PaymentDetailBo bo, HttpServletResponse response) {
        List<PaymentDetailVo> list = paymentDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "缴费明细", PaymentDetailVo.class, response);
    }

    /**
     * 获取缴费明细详细信息
     *
     * @param paymentDetailId 主键
     */
    @SaCheckPermission("waterfee:paymentDetail:query")
    @GetMapping(value = "/{paymentDetailId}")
    public R<PaymentDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("paymentDetailId") String paymentDetailId) {
        return R.ok(paymentDetailService.queryById(paymentDetailId));
    }

    /**
     * 新增缴费明细
     */
    @SaCheckPermission("waterfee:paymentDetail:add")
    @Log(title = "缴费明细", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PaymentDetailBo bo) {
        return toAjax(paymentDetailService.insertByBo(bo));
    }

    /**
     * 修改缴费明细
     */
    @SaCheckPermission("waterfee:paymentDetail:edit")
    @Log(title = "缴费明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PaymentDetailBo bo) {
        return toAjax(paymentDetailService.updateByBo(bo));
    }

    /**
     * 删除缴费明细
     *
     * @param paymentDetailIds 主键串
     */
    @SaCheckPermission("waterfee:paymentDetail:remove")
    @Log(title = "缴费明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paymentDetailIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable List<String> paymentDetailIds) {
        return toAjax(paymentDetailService.deleteWithValidByIds(paymentDetailIds, true));
    }

    // 其他可能的接口，例如接收支付平台回调通知等
    /**
     * 接收支付平台异步通知
     * @param notifyData 通知数据 (具体格式取决于支付平台, 这里假设为JSON)
     * @return 处理结果 (通常是 "success" 或 "SUCCESS")
     */
    @PostMapping("/notify/callback")
    public String handlePaymentNotify(@RequestBody String notifyData) {
        log.info("Received payment notification: {}", notifyData);
        try {
            // TODO: 1. 验证签名 (非常重要, 需要根据支付平台文档实现)
            // boolean signatureValid = verifySignature(notifyData, requestHeaders); // Example
            // if (!signatureValid) {
            //     log.error("Payment notification signature verification failed.");
            //     return "fail"; // Or specific failure response
            // }

            // 2. 解析通知数据 (假设为JSON格式)
            JSONObject data = JSON.parseObject(notifyData);
            String billId = data.getString("billId"); // 账单ID (与商户订单号对应)
            BigDecimal paymentAmount = data.getBigDecimal("amount"); // 支付金额
            String transactionId = data.getString("transactionId"); // 支付平台流水号
            String paymentStatus = data.getString("status"); // 支付状态 (例如: SUCCESS)
            String paymentMethod = data.getString("paymentMethod"); // 支付方式 (例如: WECHAT_PAY)

            // 3 & 4 & 5: 如果支付成功, 创建缴费记录并更新账单状态
            if ("SUCCESS".equalsIgnoreCase(paymentStatus)) { // 根据实际情况调整状态判断
                PaymentDetailBo paymentDetailBo = new PaymentDetailBo();
                paymentDetailBo.setBillId(billId);
                paymentDetailBo.setPaymentAmount(paymentAmount);
                paymentDetailBo.setTransactionId(transactionId);
                paymentDetailBo.setPaymentStatus(paymentStatus.toUpperCase()); // Ensure consistent status casing
                paymentDetailBo.setPaymentMethod(paymentMethod);
                paymentDetailBo.setPaymentTime(new Date()); // Use notification time or current time
                paymentDetailBo.setRemark("支付成功回调");

                // 调用 Service 层处理
                Boolean success = paymentDetailService.insertByBo(paymentDetailBo);
                if (Boolean.TRUE.equals(success)) {
                    log.info("Payment notification processed successfully for billId: {}", billId);
                    // 6. 返回处理成功标识给支付平台
                    return "success"; // Or "SUCCESS", depending on the platform
                } else {
                    log.error("Failed to process payment notification for billId: {}", billId);
                    return "fail"; // Indicate processing failure
                }
            } else {
                log.warn("Received payment notification with non-success status: {} for billId: {}", paymentStatus, billId);
                // Optionally handle other statuses like FAILED or PENDING
                return "success"; // Still acknowledge receipt for non-success statuses if required
            }
        } catch (ServiceException e) {
            log.error("Service error processing payment notification: {}", e.getMessage(), e);
            // 根据具体业务决定是否返回 fail，避免支付平台重复通知
            return "fail";
        } catch (Exception e) {
            log.error("Error processing payment notification: {}", notifyData, e);
            // 返回失败，让支付平台重试
            return "fail";
        }
    }

    /**
     * 根据微信openid查询缴费明细列表（按收入时间倒序）
     *
     * @param openid 微信openid（必填）
     * @param pageQuery 分页查询参数
     * @return 缴费明细列表
     */
    @GetMapping("/wechat/paymentDetailList")
    public TableDataInfo<PaymentDetailVo> getWechatPaymentList(
            @RequestParam(required = true) String openid,
            PageQuery pageQuery) {
        return paymentDetailService.queryByOpenidOrderByIncomeTime(openid, pageQuery);
    }

}
