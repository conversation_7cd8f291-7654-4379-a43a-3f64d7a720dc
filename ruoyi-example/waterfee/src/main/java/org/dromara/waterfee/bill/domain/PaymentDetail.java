package org.dromara.waterfee.bill.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 缴费明细对象 waterfee_payment_detail
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_payment_detail")
public class PaymentDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 缴费明细ID (主键, 使用自定义生成的支付编号)
     */
    @TableId(value = "payment_detail_id", type = IdType.INPUT)
    private String paymentDetailId;

    /**
     * 关联账单ID
     */
    private String billId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 户号
     */
    private String userNo;

    /**
     * 账单月份 (格式: YYYY-MM)
     */
    private String billMonth;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 支付方式 (例如: WECHAT_PAY, ALIPAY, BANK_TRANSFER, CASH)
     */
    private String paymentMethod;

    /**
     * 交易流水号 (支付平台返回)
     */
    private String transactionId;

    /**
     * 支付状态 (例如: SUCCESS, FAILED, PENDING, REVERSED)
     */
    private String paymentStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收费员
     */
    private String tollCollector;

    /**
     * 收入类型 (bill_payment-账单支付, prestore-预存充值, refund-退费)
     */
    private String incomeType;

    /**
     * 收入时间
     */
    private Date incomeTime;

    /**
     * 关联微信用户
     */
    private String openid;

}
