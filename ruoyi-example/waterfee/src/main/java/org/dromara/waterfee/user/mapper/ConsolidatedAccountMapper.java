package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountVo;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合收户管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ConsolidatedAccountMapper extends BaseMapperPlus<ConsolidatedAccount, ConsolidatedAccountVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<ConsolidatedAccountVo>> P selectVoPage(IPage<ConsolidatedAccount> page, Wrapper<ConsolidatedAccount> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询合收户管理列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<ConsolidatedAccountVo> queryList(@Param("page") Page<ConsolidatedAccount> page, @Param("query") ConsolidatedAccountBo query);

    /**
     * 查询合收户下用户列表
     */
    List<WaterfeeUser> selectUserListByConsolidatedAccountId(@Param("consolidatedAccountId") Long consolidatedAccountId);
}
