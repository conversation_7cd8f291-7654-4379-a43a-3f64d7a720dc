package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.ConsolidatedAccountItem;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 合收户关系业务对象 consolidated_account_item
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ConsolidatedAccountItem.class, reverseConvertGenerate = false)
public class ConsolidatedAccountItemBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long consolidatedAccountItemId;

    /**
     * 关联的合收户id
     */
    @NotNull(message = "关联的合收户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long consolidatedAccountId;

    /**
     * 关联的用户id
     */
    @NotNull(message = "关联的用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;


}
