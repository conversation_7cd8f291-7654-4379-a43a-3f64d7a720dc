package org.dromara.waterfee.user.service;

import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountVo;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 合收户管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IConsolidatedAccountService {

    /**
     * 查询合收户管理
     *
     * @param consolidatedAccountId 主键
     * @return 合收户管理
     */
    ConsolidatedAccount queryById(Long consolidatedAccountId);

    ConsolidatedAccountVo getDetailById(Long consolidatedAccountId);
    /**
     * 分页查询合收户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合收户管理分页列表
     */
    TableDataInfo<ConsolidatedAccountVo> queryPageList(ConsolidatedAccountBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的合收户管理列表
     *
     * @param bo 查询条件
     * @return 合收户管理列表
     */
    List<ConsolidatedAccountVo> queryList(ConsolidatedAccountBo bo);

    /**
     * 新增合收户管理
     *
     * @param bo 合收户管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ConsolidatedAccountBo bo);

    /**
     * 修改合收户管理
     *
     * @param bo 合收户管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ConsolidatedAccountBo bo);

    /**
     * 校验并批量删除合收户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
