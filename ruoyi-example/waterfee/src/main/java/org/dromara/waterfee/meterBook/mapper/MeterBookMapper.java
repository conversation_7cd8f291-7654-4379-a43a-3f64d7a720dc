package org.dromara.waterfee.meterBook.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.meterBook.domain.MeterBook;
import org.dromara.waterfee.meterBook.domain.vo.MeterBookVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.statisticalReport.domain.MechanicalMeterReadingRateStatisticsVO;
import org.dromara.waterfee.statisticalReport.domain.MechanicalWatchMeterNumberStatisticsVO;
import org.dromara.waterfee.statisticalReport.domain.TableBookStatisticsNumberOfUsersVO;

import java.util.Date;
import java.util.List;

/**
 * u6284u8868u624bu518c Mapperu63a5u53e3
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface MeterBookMapper extends BaseMapperPlus<MeterBook, MeterBookVo> {

    /**
     * 报表统计 - 按表册统计用户数（包括机械表和物联网表/不包括销户和停户的户数）
     * @return
     */
    List<TableBookStatisticsNumberOfUsersVO> getTableBookStatisticsNumberOfUsers();

    /**
     * 报表统计 - 机械表抄表员户数统计明细表（搜索条件是抄表员和状态）
     * @return
     */
    List<MechanicalWatchMeterNumberStatisticsVO> getMechanicalWatchMeterNumberStatistics(
                                                                    @Param("readerName") String readerName,
                                                                    @Param("userStatus") String userStatus);

    /**
     * 报表统计 - 机械表抄表率统计
     * @param quarterStartTime 当前季度开始时间
     * @param quarterEndTime 当前季度结束时间
     * @return
     */
    List<MechanicalMeterReadingRateStatisticsVO> getMechanicalMeterReadingRateStatistics(@Param("quarterStartTime") Date quarterStartTime,
                                                                                         @Param("quarterEndTime") Date quarterEndTime);
}
