package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.waterfee.user.domain.WaterfeeUser;

import java.util.List;

/**
 * 合收户管理业务对象 consolidated_account
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ConsolidatedAccount.class, reverseConvertGenerate = false)
public class ConsolidatedAccountBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long consolidatedAccountId;

    /**
     * 合收户编号
     */
    @NotBlank(message = "合收户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String consolidatedAccountNo;

    /**
     * 合收户名
     */
    private String consolidatedAccountName;

    /**
     * 户主姓名
     */
    @NotBlank(message = "户主姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ownerName;

    /**
     * 户主电话
     */
    @NotBlank(message = "户主电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ownerPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合收户用户列表
     */
    private List<WaterfeeUser> userList;


}
