package org.dromara.waterfee.statisticalReport.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.statisticalReport.domain.*;
import org.dromara.waterfee.statisticalReport.service.StatisticalReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/reportStatistics")
@Tag(name = "报表统计")
public class StatisticalReportController {
    @Autowired
    private StatisticalReportService statisticalReportService;

    @GetMapping("/getTableBookStatisticsNumberOfUsers")
    @Operation(summary = "1.按表册统计用户数（包括机械表和物联网表/不包括销户和停户的户数）")
    public R<List<TableBookStatisticsNumberOfUsersVO>> getTableBookStatisticsNumberOfUsers() {
        return R.ok(statisticalReportService.getTableBookStatisticsNumberOfUsers());
    }

    @GetMapping("/getTableBookStatisticsNumberOfUsersExcel")
    @Operation(summary = "导出 - 1.按表册统计用户数（包括机械表和物联网表/不包括销户和停户的户数）")
    public void getTableBookStatisticsNumberOfUsersExcel(HttpServletResponse response) {
        List<TableBookStatisticsNumberOfUsersVO> list = statisticalReportService.getTableBookStatisticsNumberOfUsers();
        ExcelUtil.exportExcel(list, "1.按表册统计用户数", TableBookStatisticsNumberOfUsersVO.class, response);
    }

    @GetMapping("/getNatureStatisticsUser")
    @Operation(summary = "2.按性质统计用户明细表（搜索条件是用户性质/不包括销户和停户的户数）")
    public R<List<NatureStatisticsUserVO>> getNatureStatisticsUser(String customerNature) {
        return R.ok(statisticalReportService.getNatureStatisticsUser(customerNature));
    }

    @GetMapping("/getNatureStatisticsUserExcel")
    @Operation(summary = "导出 - 2.按性质统计用户明细表（搜索条件是用户性质/不包括销户和停户的户数）")
    public void getNatureStatisticsUserExcel(String customerNature, HttpServletResponse response) {
        List<NatureStatisticsUserVO> list = statisticalReportService.getNatureStatisticsUser(customerNature);
        ExcelUtil.exportExcel(list, "2.按性质统计用户明细表", NatureStatisticsUserVO.class, response);
    }

    @GetMapping("/getMechanicalWatchMeterNumberStatistics")
    @Operation(summary = "4.机械表抄表员户数统计明细表（搜索条件是抄表员和状态）")
    public R<List<MechanicalWatchMeterNumberStatisticsVO>> getMechanicalWatchMeterNumberStatistics(String readerName, String userStatus) {
        return R.ok(statisticalReportService.getMechanicalWatchMeterNumberStatistics(readerName, userStatus));
    }

    @GetMapping("/getMechanicalWatchMeterNumberStatisticsExcel")
    @Operation(summary = "导出 - 4.机械表抄表员户数统计明细表")
    public void getMechanicalWatchMeterNumberStatisticsExcel(String readerName, String userStatus, HttpServletResponse response) {
        List<MechanicalWatchMeterNumberStatisticsVO> list = statisticalReportService.getMechanicalWatchMeterNumberStatistics(readerName, userStatus);
        ExcelUtil.exportExcel(list, "4.机械表抄表员户数统计明细表", MechanicalWatchMeterNumberStatisticsVO.class, response);
    }

    @GetMapping("/getMechanicalMeterReadingRateStatistics")
    @Operation(summary = "5.机械表抄标率统计")
    public R<List<MechanicalMeterReadingRateStatisticsVO>> getMechanicalMeterReadingRateStatistics() {
        return R.ok(statisticalReportService.getMechanicalMeterReadingRateStatistics());
    }

    @GetMapping("/getMechanicalMeterReadingRateStatisticsExcel")
    @Operation(summary = "导出 - 5.机械表抄标率统计")
    public void getMechanicalMeterReadingRateStatisticsExcel(HttpServletResponse response) {
        List<MechanicalMeterReadingRateStatisticsVO> list = statisticalReportService.getMechanicalMeterReadingRateStatistics();
        ExcelUtil.exportExcel(list, "5.机械表抄标率统计", MechanicalMeterReadingRateStatisticsVO.class, response);
    }

    @GetMapping("/getMechanicalReadingIndicatesFine")
    @Operation(summary = "6.机械表抄表明细表")
    public R<List<MechanicalReadingIndicatesFineVO>> getMechanicalReadingIndicatesFine(String startTime, String endTime) {
        return R.ok(statisticalReportService.getMechanicalReadingIndicatesFine(startTime, endTime));
    }

    @GetMapping("/getMechanicalReadingIndicatesFineExcel")
    @Operation(summary = "导出 - 6.机械表抄表明细表")
    public void getMechanicalReadingIndicatesFineExcel(String startTime, String endTime, HttpServletResponse response) {
        List<MechanicalReadingIndicatesFineVO> list = statisticalReportService.getMechanicalReadingIndicatesFine(startTime, endTime);
        ExcelUtil.exportExcel(list, "6.机械表抄表明细表", MechanicalReadingIndicatesFineVO.class, response);
    }

    @GetMapping("/getMechanicalWatchesByYear")
    @Operation(summary = "7.机械表按年份统计抄表数据")
    public R<List<MechanicalWatchesByYearVO>> getMechanicalWatchesByYear() {
        return R.ok(statisticalReportService.getMechanicalWatchesByYear());
    }

    @GetMapping("/getMechanicalWatchesByYearExcel")
    @Operation(summary = "导出 - 7.机械表按年份统计抄表数据")
    public void getMechanicalWatchesByYearExcel(HttpServletResponse response) {
        List<MechanicalWatchesByYearVO> list = statisticalReportService.getMechanicalWatchesByYear();
        ExcelUtil.exportExcel(list, "7.机械表按年份统计抄表数据", MechanicalWatchesByYearVO.class, response);
    }

    @GetMapping("/getMechanicalMeterNonResidentUsersPayMeterReading")
    @Operation(summary = "8.机械表非居民用户抄表缴费明细表（搜索条件按月份）")
    public R<List<MechanicalMeterNonResidentUsersPayMeterReadingVO>> getMechanicalMeterNonResidentUsersPayMeterReading(String searchTime) {
        return R.ok(statisticalReportService.getMechanicalMeterNonResidentUsersPayMeterReading(searchTime));
    }

    @GetMapping("/getMechanicalMeterNonResidentUsersPayMeterReadingExcel")
    @Operation(summary = "8.机械表非居民用户抄表缴费明细表（搜索条件按月份）")
    public void getMechanicalMeterNonResidentUsersPayMeterReadingExcel(String searchTime, HttpServletResponse response) {
        List<MechanicalMeterNonResidentUsersPayMeterReadingVO> list = statisticalReportService.getMechanicalMeterNonResidentUsersPayMeterReading(searchTime);
        ExcelUtil.exportExcel(list, "8.机械表非居民用户抄表缴费明细表", MechanicalMeterNonResidentUsersPayMeterReadingVO.class, response);
    }

    @GetMapping("/getMechanicalWatchArrearsDetails")
    @Operation(summary = "9.机械表欠费明细表（搜索条件是区册）")
    public R<List<MechanicalWatchArrearsDetailsVO>> getMechanicalWatchArrearsDetails(Long meterBookId) {
        return R.ok(statisticalReportService.getMechanicalWatchArrearsDetails(meterBookId));
    }

    @GetMapping("/getMechanicalWatchArrearsDetailsExcel")
    @Operation(summary = "9.机械表欠费明细表（搜索条件是区册）")
    public void getMechanicalWatchArrearsDetailsExcel(Long meterBookId, HttpServletResponse response) {
        List<MechanicalWatchArrearsDetailsVO> mechanicalWatchArrearsDetails = statisticalReportService.getMechanicalWatchArrearsDetails(meterBookId);
        ExcelUtil.exportExcel(mechanicalWatchArrearsDetails, "9.机械表欠费明细表", MechanicalWatchArrearsDetailsVO.class, response);
    }

    @GetMapping("/getPaymentDetails")
    @Operation(summary = "10.收款明细表（搜索条件是收费员、支付方式、起始日期和截止日期）")
    public R<List<PaymentDetailsVO>> getPaymentDetails(String tollCollector, String paymentMethod, String startTime, String endTime) {
        return R.ok(statisticalReportService.getPaymentDetails(tollCollector, paymentMethod, startTime, endTime));
    }

    @GetMapping("/getPaymentDetailsExcel")
    @Operation(summary = "导出 - 10.收款明细表（搜索条件是收费员、支付方式、起始日期和截止日期）")
    public void getPaymentDetailsExcel(String tollCollector, String paymentMethod, String startTime, String endTime, HttpServletResponse response) {
        List<PaymentDetailsVO> list = statisticalReportService.getPaymentDetails(tollCollector, paymentMethod, startTime, endTime);
        ExcelUtil.exportExcel(list, "10.收款明细表", PaymentDetailsVO.class, response);
    }

    @GetMapping("/getOnlinePaymentDetailSummary")
    @Operation(summary = "11.网上缴费明细汇总表（搜索条件是支付方式）")
    public R<List<OnlinePaymentDetailSummaryVO>> getOnlinePaymentDetailSummary(String paymentMethod) {
        return R.ok(statisticalReportService.getOnlinePaymentDetailSummary(paymentMethod));
    }

    @GetMapping("/getOnlinePaymentDetailSummaryExcel")
    @Operation(summary = "导出 - 11.网上缴费明细汇总表（搜索条件是支付方式）")
    public void getOnlinePaymentDetailSummaryExcel(String paymentMethod, HttpServletResponse response) {
        statisticalReportService.getOnlinePaymentDetailSummary(paymentMethod);
        ExcelUtil.exportExcel(statisticalReportService.getOnlinePaymentDetailSummary(paymentMethod), "11.网上缴费明细汇总表", OnlinePaymentDetailSummaryVO.class, response);
    }

    @GetMapping("/getNewAccountUserDetails")
    @Operation(summary = "12.新开户用户明细表（搜索条件是年份）")
    public R<List<NewAccountUserDetailsVO>> getNewAccountUserDetails(String year) {
        return R.ok(statisticalReportService.getNewAccountUserDetails(year));
    }

    @GetMapping("/getNewAccountUserDetailsExcel")
    @Operation(summary = "导出 - 12.新开户用户明细表（搜索条件是年份）")
    public void getNewAccountUserDetailsExcel(String year, HttpServletResponse response) {
        List<NewAccountUserDetailsVO> list = statisticalReportService.getNewAccountUserDetails(year);
        ExcelUtil.exportExcel(list, "12.新开户用户明细表", NewAccountUserDetailsVO.class, response);
    }

    @GetMapping("/getUserTransferDetails")
    @Operation(summary = "13.用户过户明细表")
    public R<List<UserTransferDetailsVO>> getUserTransferDetails() {
        return R.ok(statisticalReportService.getUserTransferDetails());
    }

    @GetMapping("/getUserTransferDetailsExcel")
    @Operation(summary = "导出 - 13.用户过户明细表")
    public void getUserTransferDetailsExcel(HttpServletResponse response) {
        List<UserTransferDetailsVO> list = statisticalReportService.getUserTransferDetails();
        ExcelUtil.exportExcel(list, "13.用户过户明细表", UserTransferDetailsVO.class, response);
    }

    @GetMapping("/getUserChangeTableCount")
    @Operation(summary = "14.用户换表修改表数明细表")
    public R<List<UserChangeTableCountVO>> getUserChangeTableCount() {
        return R.ok(statisticalReportService.getUserChangeTableCount());
    }

    @GetMapping("/getUserChangeTableCountExcel")
    @Operation(summary = "导出 - 14.用户换表修改表数明细表")
    public void getUserChangeTableCountExcel(HttpServletResponse response) {
        List<UserChangeTableCountVO> list = statisticalReportService.getUserChangeTableCount();
        ExcelUtil.exportExcel(list, "14.用户换表修改表数明细表", UserChangeTableCountVO.class, response);
    }

    @GetMapping("/getWaterTypeChangeDetails")
    @Operation(summary = "15.用水类型更改明细表")
    public R<List<WaterTypeChangeDetailsVO>> getWaterTypeChangeDetails() {
        return R.ok(statisticalReportService.getWaterTypeChangeDetails());
    }

    @GetMapping("/getWaterTypeChangeDetailsExcel")
    @Operation(summary = "15.用水类型更改明细表")
    public void getWaterTypeChangeDetailsExcel(HttpServletResponse response) {
        List<WaterTypeChangeDetailsVO> list = statisticalReportService.getWaterTypeChangeDetails();
        ExcelUtil.exportExcel(list, "15.用水类型更改明细表", WaterTypeChangeDetailsVO.class, response);
    }
}
