package org.dromara.waterfee.repair.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.repair.domain.bo.WaterfeeRepairReportBo;
import org.dromara.waterfee.repair.domain.vo.WaterfeeRepairReportVo;
import org.dromara.waterfee.repair.domain.WaterfeeRepairReport;
import org.dromara.waterfee.repair.mapper.WaterfeeRepairReportMapper;
import org.dromara.waterfee.repair.service.IWaterfeeRepairReportService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 报修管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RequiredArgsConstructor
@Service
public class WaterfeeRepairReportServiceImpl implements IWaterfeeRepairReportService {

    private final WaterfeeRepairReportMapper baseMapper;

    /**
     * 查询报修管理
     *
     * @param repairId 主键
     * @return 报修管理
     */
    @Override
    public WaterfeeRepairReport queryById(Long repairId){
        return baseMapper.selectById(repairId);
    }

    /**
     * 分页查询报修管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报修管理分页列表
     */
    @Override
    public TableDataInfo<WaterfeeRepairReportVo> queryPageList(WaterfeeRepairReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeRepairReport> lqw = buildQueryWrapper(bo);
        Page<WaterfeeRepairReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的报修管理列表
     *
     * @param bo 查询条件
     * @return 报修管理列表
     */
    @Override
    public List<WaterfeeRepairReportVo> queryList(WaterfeeRepairReportBo bo) {
        LambdaQueryWrapper<WaterfeeRepairReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeRepairReport> buildQueryWrapper(WaterfeeRepairReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeRepairReport> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeRepairReport::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getReportContent()), WaterfeeRepairReport::getReportContent, bo.getReportContent());
        lqw.like(StringUtils.isNotBlank(bo.getReporterName()), WaterfeeRepairReport::getReporterName, bo.getReporterName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), WaterfeeRepairReport::getContactPhone, bo.getContactPhone());
        lqw.eq(bo.getReportTime() != null, WaterfeeRepairReport::getReportTime, bo.getReportTime());
        return lqw;
    }

    /**
     * 新增报修管理
     *
     * @param bo 报修管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeRepairReportBo bo) {
        WaterfeeRepairReport add = MapstructUtils.convert(bo, WaterfeeRepairReport.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepairId(add.getRepairId());
        }
        return flag;
    }

    /**
     * 修改报修管理
     *
     * @param bo 报修管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeRepairReportBo bo) {
        WaterfeeRepairReport update = MapstructUtils.convert(bo, WaterfeeRepairReport.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeRepairReport entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除报修管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
