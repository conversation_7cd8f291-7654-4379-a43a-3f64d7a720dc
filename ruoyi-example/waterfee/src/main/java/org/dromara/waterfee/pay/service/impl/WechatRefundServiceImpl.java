package org.dromara.waterfee.pay.service.impl;

import com.google.gson.Gson;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.waterfee.pay.domain.WechatQueryRefundParam;
import org.dromara.waterfee.pay.domain.WechatRefundParam;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.waterfee.pay.enums.RefundStatusEnums;
import org.dromara.waterfee.pay.service.IWechatRefundRecordService;
import org.dromara.waterfee.pay.service.IWechatRefundService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.mq.config.RabbitConfig;
import org.dromara.waterfee.pay.utils.UniqueKeyGenerator;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

@Slf4j
@Service
public class WechatRefundServiceImpl implements IWechatRefundService {

    private final RefundService refundService;

    private final NotificationParser notificationParser;

    @Autowired
    private IWechatRefundRecordService wechatRefundRecordService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /** 商户号 */
    @Value("${wechatPay.merchant-id}")
    private String merchantId;

    /** 应用ID */
    @Value("${wechatPay.app-id}")
    private String appId;

    /** 通知地址 */
    @Value("${wechatPay.refund-notify-url}")
    private String refundNotifyUrl;


    /**
     * 加载配置
     */
    @Autowired
    public WechatRefundServiceImpl(Config wechatPayConfig, NotificationConfig notificationConfig) {
        this.refundService = new RefundService.Builder().config(wechatPayConfig).build();
        this.notificationParser = new NotificationParser(notificationConfig);
    }

    /** 申请退款 */
    @Transactional(rollbackFor = Exception.class)
    public Refund applyForRefund(WechatRefundParam wechatRefundParam) {
        CreateRequest request = new CreateRequest();
        if(wechatRefundParam == null) {
            throw new IllegalArgumentException("退款参数不能为空");
        }
        if(StringUtils.isNotBlank(wechatRefundParam.getOutTradeNo())) {
            request.setOutTradeNo(wechatRefundParam.getOutTradeNo());
        }else if(StringUtils.isNotBlank(wechatRefundParam.getTransactionId())) {
            request.setTransactionId(wechatRefundParam.getTransactionId());
        }else {
            throw new IllegalArgumentException("商户订单号和微信支付订单号不能同时为空");
        }
        if(StringUtils.isNotBlank(wechatRefundParam.getReason())) {
            if(wechatRefundParam.getReason().length() < 80) {
                request.setReason(wechatRefundParam.getReason());
            }else {
                throw new IllegalArgumentException("退款原因不能超过80个字符");
            }
        }
        AmountReq amount = new AmountReq();
        if(wechatRefundParam.getRefundFee() != null && wechatRefundParam.getRefundFee().intValue() > 0) {
            amount.setRefund(wechatRefundParam.getRefundFee());
        }else {
            throw new IllegalArgumentException("退款金额不能小于等于0");
        }
        if(wechatRefundParam.getTotalFee() != null && wechatRefundParam.getTotalFee().intValue() > 0) {
            amount.setTotal(wechatRefundParam.getTotalFee());
        }else {
            throw new IllegalArgumentException("订单金额不能小于等于0");
        }
        amount.setCurrency("CNY");
        request.setAmount(amount);
        request.setNotifyUrl(refundNotifyUrl);
        String outRefundNo = UniqueKeyGenerator.generateUniqueKey("REFUND");
        request.setOutRefundNo(outRefundNo);
        //创建退款记录
        WechatRefundRecordBo wechatRefundRecordBo = new WechatRefundRecordBo();
        wechatRefundRecordBo.setRefundId(outRefundNo);
        wechatRefundRecordBo.setPaymentDetailId(wechatRefundParam.getOutTradeNo());
        wechatRefundRecordBo.setTransactionId(wechatRefundParam.getTransactionId());
        wechatRefundRecordBo.setRefundStatus(RefundStatusEnums.PROCESSING.getCode());
        wechatRefundRecordBo.setPaymentAmount(new BigDecimal(wechatRefundParam.getTotalFee()));
        wechatRefundRecordBo.setPaymentRefund(new BigDecimal(wechatRefundParam.getRefundFee()));
        if(StringUtils.isNotBlank(wechatRefundParam.getReason())) {
            wechatRefundRecordBo.setRemark(wechatRefundParam.getReason());
        }
        wechatRefundRecordService.insertByBo(wechatRefundRecordBo);
        return refundService.create(request);
    }

    /** 查询退款 */
    public Refund queryByOutRefundNo(WechatQueryRefundParam wechatQueryRefundParam) {

        QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
        if(wechatQueryRefundParam == null) {
            throw new IllegalArgumentException("查询退款参数不能为空");
        }
        if(StringUtils.isNotBlank(wechatQueryRefundParam.getOutRefundNo())) {
            request.setOutRefundNo(wechatQueryRefundParam.getOutRefundNo());
        }else {
            throw new IllegalArgumentException("商户退款单号不能为空");
        }
        return refundService.queryByOutRefundNo(request);
    }

    /** 微信退款回调 */
    public String wechatRefundCallBack(HttpServletRequest request, HttpServletResponse response) {
        // 构造 RequestParam
        RequestParam requestParam = new RequestParam.Builder()
            .serialNumber(request.getHeader("Wechatpay-Serial"))
            .nonce(request.getHeader("Wechatpay-Nonce"))
            .signature(request.getHeader("Wechatpay-Signature"))
            .timestamp(request.getHeader("Wechatpay-Timestamp"))
            .body(getRequestBody(request))
            .build();
        RefundNotification refundNotification;
        Gson gson = new Gson();
        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            refundNotification = notificationParser.parse(requestParam, RefundNotification.class);
        } catch (ValidationException e) {
            // 签名验证失败，返回 401 UNAUTHORIZED 状态码
            log.error("sign verification failed", e);
            response.setStatus(500);
            final HashMap<String, Object> map = new HashMap<>();
            map.put("code", "ERROR");
            map.put("message", "验签失败");
            return gson.toJson(map);
        }
        //根据回调结果更新业务表状态
        final HashMap<String, Object> map = new HashMap<>();
        if(refundNotification != null) {
            // 推送回调状态到消息队列 异步更新业务信息
            rabbitTemplate.convertAndSend(RabbitConfig.PAY_EXCHANGE, RabbitConfig.REFUND_CALL_BACK_ROUTING_KEY, gson.toJson(refundNotification).getBytes(StandardCharsets.UTF_8));
            response.setStatus(200);
            map.put("code", "SUCCESS");
            map.put("message", "成功");
            return gson.toJson(map);
        }else {
            response.setStatus(500);
            map.put("code", "ERROR");
            map.put("message", "解析微信退款回调结果失败");
            return gson.toJson(map);
        }
    }

    /**
     * 获取requestBody
     *
     * @param request
     * @return
     */
    private String getRequestBody(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();

        try (ServletInputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        ) {
            String line;

            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }

        } catch (IOException e) {
            log.error("读取数据流异常:{}", e);
        }

        return sb.toString();

    }

}
