package org.dromara.waterfee.branch.domain.vo;

import org.dromara.waterfee.branch.domain.BranchOffice;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 营业网点视图对象 branch_office
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchOffice.class)
public class BranchOfficeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long branchId;

    /**
     * 网点名称
     */
    @ExcelProperty(value = "网点名称")
    private String branchName;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String address;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String lon;

    /**
     * 维度
     */
    @ExcelProperty(value = "维度")
    private String lat;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
