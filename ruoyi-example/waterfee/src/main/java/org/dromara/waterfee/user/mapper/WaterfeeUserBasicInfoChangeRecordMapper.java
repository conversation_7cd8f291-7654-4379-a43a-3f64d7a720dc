package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.statisticalReport.domain.WaterTypeChangeDetailsVO;
import org.dromara.waterfee.user.domain.WaterfeeUserBasicInfoChangeRecord;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserBasicInfoChangeRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBasicInfoChangeRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用水用户基础信息变更记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface WaterfeeUserBasicInfoChangeRecordMapper extends BaseMapperPlus<WaterfeeUserBasicInfoChangeRecord, WaterfeeUserBasicInfoChangeRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeUserBasicInfoChangeRecordVo>> P selectVoPage(IPage<WaterfeeUserBasicInfoChangeRecord> page, Wrapper<WaterfeeUserBasicInfoChangeRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询用水用户基础信息变更记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeUserBasicInfoChangeRecordVo> queryList(@Param("page") Page<WaterfeeUserBasicInfoChangeRecord> page, @Param("query") RecordQo query);

    /**
     * 报表统计 - 用水类型更改明细表
     * @return
     */
    List<WaterTypeChangeDetailsVO> getWaterTypeChangeDetails();
}
