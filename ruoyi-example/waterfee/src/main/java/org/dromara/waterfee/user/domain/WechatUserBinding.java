package org.dromara.waterfee.user.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 微信账号绑定户号关系对象 wechat_user_binding
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wechat_user_binding")
public class WechatUserBinding extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 微信用户ID
     */
    @TableId(value = "wechat_user_id")
    private Long wechatUserId;

    /**
     * 微信用户标识
     */
    private String openid;

    /**
     * 用水户编号
     */
    private String userNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
