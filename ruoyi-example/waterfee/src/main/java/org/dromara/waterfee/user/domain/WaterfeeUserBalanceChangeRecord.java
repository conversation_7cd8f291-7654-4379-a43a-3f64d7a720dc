package org.dromara.waterfee.user.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户余额变更记录对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_user_balance_change_record")
public class WaterfeeUserBalanceChangeRecord extends TenantEntity {

    /**
     * 记录ID
     */
    @TableId
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 变更类型（PAYMENT-支付扣款, RECHARGE-充值, REFUND-退款）
     */
    private String changeType;

    /**
     * 变更前余额
     */
    private BigDecimal beforeBalance;

    /**
     * 变更金额
     */
    private BigDecimal changeAmount;

    /**
     * 变更后余额
     */
    private BigDecimal afterBalance;

    /**
     * 关联业务ID（账单ID、充值记录ID等）
     */
    private String businessId;

    /**
     * 关联业务类型（BILL-账单支付, DEPOSIT-预存充值, REFUND-退款）
     */
    private String businessType;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 变更时间
     */
    private Date changeTime;

    /**
     * 备注
     */
    private String remark;
}