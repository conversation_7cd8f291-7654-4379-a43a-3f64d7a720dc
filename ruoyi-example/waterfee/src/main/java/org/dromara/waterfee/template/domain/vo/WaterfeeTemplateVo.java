package org.dromara.waterfee.template.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.waterfee.template.domain.entity.WaterfeeTemplate;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 模板管理视图对象 WaterfeeTemplateVo
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeTemplate.class)
public class WaterfeeTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @ExcelProperty(value = "模板ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 模板名称
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板类型
     */
    @ExcelProperty(value = "模板类型")
    private String templateType;

    /**
     * 模板类型名称
     */
    private String templateTypeName;

    /**
     * 模板分类
     */
    @ExcelProperty(value = "模板分类")
    private String templateCategory;

    /**
     * 模板分类名称
     */
    private String templateCategoryName;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 是否启用
     */
    @ExcelProperty(value = "是否启用")
    private String isEnabled;

    /**
     * 启用状态名称
     */
    private String enabledStatusName;

    /**
     * 模板描述
     */
    @ExcelProperty(value = "模板描述")
    private String templateDescription;

    /**
     * 上传者
     */
    @ExcelProperty(value = "上传者")
    private String uploadBy;

    /**
     * 上传时间
     */
    @ExcelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 启用者
     */
    @ExcelProperty(value = "启用者")
    private String enabledBy;

    /**
     * 启用时间
     */
    @ExcelProperty(value = "启用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enabledTime;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    private String version;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
