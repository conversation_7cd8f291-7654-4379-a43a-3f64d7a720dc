package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用水用户管理业务对象 waterfee_user
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeUser.class, reverseConvertGenerate = false)
public class WaterfeeUserBo extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 营业区域id
     */
    @NotNull(message = "营业区域id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long areaId;

    /**
     * 小区id
     */
    @NotNull(message = "小区id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long communityId;

    /**
     * 单元房号
     */
    @NotBlank(message = "单元房号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unitRoomNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @NotBlank(message = "客户性质（字典waterfee_user_customer_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerNature;

    /**
     * 用水性质（字典waterfee_user_use_water_nature）
     */
    @NotBlank(message = "用水性质（字典waterfee_user_use_water_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String useWaterNature;

    /**
     * 用水人数
     */
    @NotNull(message = "用水人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long useWaterNumber;

    /**
     * 用水户名称
     */
    @NotBlank(message = "用水户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateType;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNumber;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    @NotBlank(message = "用户状态（字典waterfee_user_user_status）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userStatus;

    /**
     * 用水地址
     */
    @NotBlank(message = "用水地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 纳税人识别号
     */
//    @NotBlank(message = "纳税人识别号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxpayerIdentificationNumber;

    /**
     * 供水日期
     */
    @NotNull(message = "供水日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date supplyDate;

    /**
     * 开票名称
     */
    private String invoiceName;

    /**
     * 发票类型（字典waterfee_user_invoice_type）
     */
//    @NotBlank(message = "发票类型（字典waterfee_user_invoice_type）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceType;

    /**
     * 水表编号
     */
//    private String meterNo;

    /**
     * 价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    @NotBlank(message = "价格-用水性质（字典waterfee_user_use_water_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String priceUseWaterNature;

    /**
     * 计费方式（字典waterfee_user_billing_method）
     */
    @NotBlank(message = "计费方式（字典waterfee_user_billing_method）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billingMethod;

    /**
     * 是否有违约金（字典yes_no）
     */
    private String ifPenalty;

    /**
     * 违约金类型（字典waterfee_user_penalty_type）
     */
    private String penaltyType;

    /**
     * 是否有附加费（字典yes_no）
     */
    private String ifExtraCharge;

    /**
     * 附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
    private String extraChargeType;

    /**
     * 审核状态（字典audit_status）
     */
    private String auditStatus;

    /**
     * 是否重点户（字典yes_no）
     */
    private String ifSpecific;

    /**
     * 余额
     */
    //注释掉是为了防止使用更新接口余额信息
//    private BigDecimal balance;

    /**
     * 用户编号模糊查询
     */
    private String userNoLike;

    /**
     * 用水户名称模糊查询
     */
    private String userNameLike;

    /**
     * 手机号码模糊查询
     */
    private String phoneNumberLike;

    /**
     * 用水地址模糊查询
     */
    private String addressLike;

    /**
     * 电子邮箱模糊查询
     */
    private String emailLike;

    /**
     * 上传图片
     */
    private String picture;

    /**
     * 上传文件
     */
    private String file;
}
