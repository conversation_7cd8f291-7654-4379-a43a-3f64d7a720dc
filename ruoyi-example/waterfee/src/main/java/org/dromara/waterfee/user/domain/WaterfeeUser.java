package org.dromara.waterfee.user.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用水用户管理对象 waterfee_user
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_user")
public class WaterfeeUser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 营业区域id
     */
    private Long areaId;

    /**
     * 小区id
     */
    private Long communityId;

    /**
     * 单元房号
     */
    private String unitRoomNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    private String customerNature;

    /**
     * 用水性质（字典waterfee_user_use_water_nature）
     */
    private String useWaterNature;

    /**
     * 用水人数
     */
    private Long useWaterNumber;

    /**
     * 用水户名称
     */
    private String userName;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 证件类型
     */
    private String certificateType;

    /**
     * 证件号码
     */
    private String certificateNumber;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    private String userStatus;

    /**
     * 用水地址
     */
    private String address;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 纳税人识别号
     */
    private String taxpayerIdentificationNumber;

    /**
     * 供水日期
     */
    private Date supplyDate;

    /**
     * 开票名称
     */
    private String invoiceName;

    /**
     * 发票类型（字典waterfee_user_invoice_type）
     */
    private String invoiceType;

    /**
     * 水表编号
     */
//    private String meterNo;

    /**
     * 价格-用水性质（字典waterfee_user_use_water_nature）
     */
    private String priceUseWaterNature;

    /**
     * 计费方式（字典waterfee_user_billing_method）
     */
    private String billingMethod;

    /**
     * 是否有违约金（字典yes_no）
     */
    private String ifPenalty;

    /**
     * 违约金类型（字典waterfee_user_penalty_type）
     */
    private String penaltyType;

    /**
     * 是否有附加费（字典yes_no）
     */
    private String ifExtraCharge;

    /**
     * 附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
    private String extraChargeType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 审核状态（字典audit_status）
     */
    private String auditStatus;

    /**
     * 是否重点户（字典yes_no）
     */
    private String ifSpecific;

    /**
     * 用户阶梯用水量
     */
    private Double ladderUsage;

    /**
     * 总用水量
     */
    private Double totalUsage;

    /**
     * 供水日期
     */
    private Date lastQuarterResetTime;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 上传图片
     */
    private String picture;

    /**
     * 上传文件
     */
    private String file;
}
