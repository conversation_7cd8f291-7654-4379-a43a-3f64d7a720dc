package org.dromara.waterfee.user.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.waterfee.api.RemoteWaterfeeUserService;
import org.dromara.waterfee.api.domain.WaterfeeUserDTO;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBasicInfoBo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserPriceBo;
import org.dromara.waterfee.user.domain.dto.WaterfeeMeterAddDto;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 用水用户远程服务实现
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteWaterfeeUserServiceImpl implements RemoteWaterfeeUserService {

    private final IWaterfeeUserService waterfeeUserService;

    /**
     * 根据用户ID查询用水用户信息
     *
     * @param userId 用户ID
     * @param source 调用来源
     * @return 用水用户信息
     */
    @Override
    public WaterfeeUserDTO getWaterfeeUserById(Long userId, String source) {
        log.info("远程调用获取用水用户信息，用户ID：{}，调用来源：{}", userId, source);
        WaterfeeUser user = waterfeeUserService.queryById(userId);
        if (user == null) {
            log.warn("用户ID为{}的用水用户不存在", userId);
            return null;
        }
        WaterfeeUserDTO waterfeeUserDTO = new WaterfeeUserDTO();
        BeanUtils.copyProperties(user, waterfeeUserDTO);
        return waterfeeUserDTO;
    }

    /**
     * 更新用水用户信息
     *
     * @param waterfeeUser 用水用户信息
     * @param source       调用来源
     * @return 是否更新成功
     */
    @Override
    public Boolean updateWaterfeeUser(WaterfeeUserDTO waterfeeUser, String source) {
        log.info("远程调用更新用水用户信息，用户ID：{}，调用来源：{}", waterfeeUser.getUserId(), source);
        if (waterfeeUser.getUserId() == null) {
            log.warn("更新用水用户信息失败，用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        WaterfeeUser existingUser = waterfeeUserService.queryById(waterfeeUser.getUserId());
        if (existingUser == null) {
            log.warn("用户ID为{}的用水用户不存在", waterfeeUser.getUserId());
            return false;
        }

        // 转换为Bo对象并更新
        WaterfeeUserBo userBo = new WaterfeeUserBo();
        BeanUtils.copyProperties(waterfeeUser, userBo);
        return waterfeeUserService.updateByBo(userBo);
    }

    /**
     * 删除用水用户
     *
     * @param userId 用户ID
     * @param source 调用来源
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWaterfeeUser(Long userId, String source) {
        log.info("远程调用删除用水用户，用户ID：{}，调用来源：{}", userId, source);
        if (userId == null) {
            log.warn("删除用水用户失败，用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        WaterfeeUser existingUser = waterfeeUserService.queryById(userId);
        if (existingUser == null) {
            log.warn("用户ID为{}的用水用户不存在", userId);
            return false;
        }

        // 删除用户
        return waterfeeUserService.deleteWithValidByIds(Collections.singleton(userId), true);
    }

    /**
     * 工作流 - 新增用水用户基本信息
     *
     * @param userDTO 用水用户基本信息
     * @param source  调用来源
     * @return 新增后的用水用户信息
     */
    @Override
    public WaterfeeUserDTO addUserBasicInfo(WaterfeeUserDTO userDTO, String source) {
        log.info("远程调用工作流 - 新增用水用户基本信息，调用来源：{}", source);

        // 转换为Bo对象
        WaterfeeUserBasicInfoBo userBo = new WaterfeeUserBasicInfoBo();
        BeanUtils.copyProperties(userDTO, userBo);

        // 调用服务新增用户基本信息
        WaterfeeUser user = waterfeeUserService.insertUserBasicInfo(userBo);
        if (user == null) {
            log.warn("新增用水用户基本信息失败");
            return null;
        }

        // 转换为DTO返回
        WaterfeeUserDTO newUserDTO = new WaterfeeUserDTO();
        BeanUtils.copyProperties(user, newUserDTO);
        return newUserDTO;
    }

    /**
     * 工作流 - 用水用户关联水表
     *
     * @param userId  用户ID
     * @param meterNo 水表编号
     * @param source  调用来源
     * @return 是否关联成功
     */
    @Override
    public Boolean bindUserMeter(Long userId, String meterNo, String source) {
        log.info("远程调用工作流 - 用水用户关联水表，用户ID：{}，水表编号：{}，调用来源：{}",
            userId, meterNo, source);

        // 构建水表添加DTO
        WaterfeeMeterAddDto dto = new WaterfeeMeterAddDto();
        dto.setUserId(userId);

        // 如果有用户编号，则设置用户编号
        if (userId != null) {
            WaterfeeUser user = waterfeeUserService.queryById(userId);
            if (user != null) {
                dto.setUserNo(user.getUserNo());
            }
        }

        // 设置水表信息和标志
        WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
        meterBo.setMeterNo(meterNo);
        dto.setWaterfeeMeterBo(meterBo);
        //标记为已存在的水表
        dto.setFlag(true);

        // 调用服务绑定水表
//        return waterfeeUserService.bindUserMeter(dto);
        return true;
    }

    /**
     * 工作流 - 设定用水用户价格信息
     *
     * @param userDTO 用水用户价格信息
     * @param source  调用来源
     * @return 是否设置成功
     */
    @Override
    public Boolean updateUserPriceInfo(WaterfeeUserDTO userDTO, String source) {
        log.info("远程调用工作流 - 设定用水用户价格信息，用户ID：{}，调用来源：{}",
            userDTO.getUserId(), source);

        if (userDTO.getUserId() == null) {
            log.warn("设定用水用户价格信息失败，用户ID不能为空");
            return false;
        }

        // 检查用户是否存在
        WaterfeeUser existingUser = waterfeeUserService.queryById(userDTO.getUserId());
        if (existingUser == null) {
            log.warn("用户ID为{}的用水用户不存在", userDTO.getUserId());
            return false;
        }

        // 转换为价格Bo对象
        WaterfeeUserPriceBo priceBo = new WaterfeeUserPriceBo();
        priceBo.setUserId(userDTO.getUserId());
        priceBo.setUserNo(userDTO.getUserNo());
        priceBo.setPriceUseWaterNature(userDTO.getPriceUseWaterNature());
        priceBo.setBillingMethod(userDTO.getBillingMethod());
        priceBo.setIfPenalty(userDTO.getIfPenalty());
        priceBo.setPenaltyType(userDTO.getPenaltyType());
        priceBo.setIfExtraCharge(userDTO.getIfExtraCharge());
        priceBo.setExtraChargeType(userDTO.getExtraChargeType());

        // 调用服务更新价格信息
        return waterfeeUserService.updateUserPriceInfo(priceBo);
    }
}
