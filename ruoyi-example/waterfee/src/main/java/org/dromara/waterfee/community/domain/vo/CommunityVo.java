package org.dromara.waterfee.community.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.waterfee.community.domain.Community;

import java.io.Serial;
import java.io.Serializable;

/**
 * 社区管理视图对象
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Community.class)
public class CommunityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 社区编号
     */
    @ExcelProperty(value = "社区编号")
    private String communityNo;

    /**
     * 社区名称
     */
    @ExcelProperty(value = "社区名称")
    private String communityName;

    /**
     * 所属区域ID
     */
    @ExcelProperty(value = "所属区域ID")
    private Long areaId;

    /**
     * 所属区域名称
     */
    @ExcelProperty(value = "所属区域名称")
    private String areaName;

    /**
     * 社区地址
     */
    @ExcelProperty(value = "社区地址")
    private String address;

    /**
     * 社区描述
     */
    @ExcelProperty(value = "社区描述")
    private String description;
}
