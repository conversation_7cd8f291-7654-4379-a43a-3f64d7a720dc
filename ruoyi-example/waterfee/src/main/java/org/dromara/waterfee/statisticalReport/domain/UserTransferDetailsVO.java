package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UserTransferDetailsVO {
    /**
     * 更改时间（创建时间）
     */
    @ExcelProperty(value = "更改时间")
    private String createTime;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 原用户名称
     */
    @ExcelProperty(value = "原用户名称")
    private String beforeUserName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "电话")
    private String phoneNumber;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "身份证号（信用代码）")
    private String certificateNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "用户类型")
    private String customerNature;

    /**
     * 用水类型
     */
    @ExcelProperty(value = "用水类型")
    private String useWaterNature;

    /**
     * 更改后用户姓名
     */
    @ExcelProperty(value = "更改后用户姓名")
    private String afterUserName;
}
