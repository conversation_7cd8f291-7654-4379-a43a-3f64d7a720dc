package org.dromara.waterfee.template.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 模板管理对象 waterfee_template
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_template")
public class  WaterfeeTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableId(value = "template_id")
    private Long templateId;

    /**
     * 租户编号 ··············
     */
    private String tenantId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型（WORD-Word文档 EXCEL-Excel文档 PDF-PDF文档）
     */
    private String templateType;

    /**
     * 模板分类（BILL-账单模板 REPORT-报表模板 NOTICE-通知模板）
     */
    private String templateCategory;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 是否启用（0-禁用 1-启用）
     */
    private String isEnabled;

    /**
     * 模板描述
     */
    private String templateDescription;

    /**
     * 上传者
     */
    private String uploadBy;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 启用者
     */
    private String enabledBy;

    /**
     * 启用时间
     */
    private Date enabledTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
