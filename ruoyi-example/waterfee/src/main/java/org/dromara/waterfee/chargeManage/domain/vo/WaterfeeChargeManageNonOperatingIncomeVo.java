package org.dromara.waterfee.chargeManage.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 营业外收入记录视图对象 waterfee_charge_manage_non_operating_income
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageNonOperatingIncome.class)
public class WaterfeeChargeManageNonOperatingIncomeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 收入类型
     */
    @ExcelProperty(value = "收入类型")
    private String incomeType;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private Long amount;

    /**
     * 收入时间
     */
    @ExcelProperty(value = "收入时间")
    private Date incomeTime;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
