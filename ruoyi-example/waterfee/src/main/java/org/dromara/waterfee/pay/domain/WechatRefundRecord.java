package org.dromara.waterfee.pay.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 微信退款记录对象 wechat_refund_record
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wechat_refund_record")
public class WechatRefundRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退款单号 (主键)
     */
    @TableId(value = "refund_id")
    private String refundId;

    /**
     * 缴费明细ID (订单号)
     */
    private String paymentDetailId;

    /**
     * 微信退款单号
     */
    private String wechatRefundId;

    /**
     * 退款状态SUCCESS—退款成功 CLOSED—退款关闭 PROCESSING—退款处理中 ABNORMAL—退款异常
     */
    private String refundStatus;

    /**
     * 退款成功时间
     */
    private Date successTime;

    /**
     * 退款入账账户
     */
    private String userReceivedAccount;

    /**
     * 交易流水号 (支付平台返回)
     */
    private String transactionId;

    /**
     * 订单总金额
     */
    private BigDecimal paymentAmount;

    /**
     * 退款金额
     */
    private BigDecimal paymentRefund;

    /**
     * 实际支付金额（不包含代金券）
     */
    private BigDecimal payerAmount;

    /**
     * 用户实际退款金额（不包含代金券）
     */
    private BigDecimal payerRefund;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
