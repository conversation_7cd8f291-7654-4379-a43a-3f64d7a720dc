package org.dromara.waterfee.income.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.income.domain.bo.WaterfeeIncomeRecordBo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeSummaryVo;

import java.util.Collection;
import java.util.List;

/**
 * 收入记录Service接口
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
public interface IWaterfeeIncomeRecordService {

    /**
     * 查询收入记录
     *
     * @param incomeId 收入记录主键
     * @return 收入记录
     */
    WaterfeeIncomeRecordVo queryById(Long incomeId);

    /**
     * 查询收入记录列表
     *
     * @param bo 收入记录
     * @return 收入记录集合
     */
    TableDataInfo<WaterfeeIncomeRecordVo> queryPageList(WaterfeeIncomeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询收入记录列表
     *
     * @param bo 收入记录
     * @return 收入记录集合
     */
    List<WaterfeeIncomeRecordVo> queryList(WaterfeeIncomeRecordBo bo);

    /**
     * 新增收入记录
     *
     * @param bo 收入记录
     * @return 结果
     */
    Boolean insertByBo(WaterfeeIncomeRecordBo bo);

    /**
     * 修改收入记录
     *
     * @param bo 收入记录
     * @return 结果
     */
    Boolean updateByBo(WaterfeeIncomeRecordBo bo);

    /**
     * 校验并批量删除收入记录信息
     *
     * @param ids     需要删除的收入记录主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据用户编号查询收入记录
     *
     * @param userNo 用户编号
     * @return 收入记录集合
     */
    List<WaterfeeIncomeRecordVo> queryListByUserNo(String userNo);

    /**
     * 统计不同收入类型的金额
     *
     * @return 收入统计对象
     */
    WaterfeeIncomeSummaryVo getIncomeSummary();
}
