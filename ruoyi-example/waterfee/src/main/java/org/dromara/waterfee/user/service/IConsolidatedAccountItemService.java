package org.dromara.waterfee.user.service;

import org.dromara.waterfee.user.domain.ConsolidatedAccountItem;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountItemVo;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountItemBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 合收户关系Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IConsolidatedAccountItemService {

    /**
     * 查询合收户关系
     *
     * @param consolidatedAccountItemId 主键
     * @return 合收户关系
     */
    ConsolidatedAccountItem queryById(Long consolidatedAccountItemId);

    /**
     * 分页查询合收户关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合收户关系分页列表
     */
    TableDataInfo<ConsolidatedAccountItemVo> queryPageList(ConsolidatedAccountItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的合收户关系列表
     *
     * @param bo 查询条件
     * @return 合收户关系列表
     */
    List<ConsolidatedAccountItemVo> queryList(ConsolidatedAccountItemBo bo);

    /**
     * 新增合收户关系
     *
     * @param bo 合收户关系
     * @return 是否新增成功
     */
    Boolean insertByBo(ConsolidatedAccountItemBo bo);
    
    /**
     * 批量新增合收户关系
     *
     * @param boList 合收户关系列表
     * @return 是否批量新增成功
     */
    Boolean insertByBoList(List<ConsolidatedAccountItemBo> boList);

    /**
     * 修改合收户关系
     *
     * @param bo 合收户关系
     * @return 是否修改成功
     */
    Boolean updateByBo(ConsolidatedAccountItemBo bo);

    /**
     * 校验并批量删除合收户关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据合收户ID批量删除合收户关系
     *
     * @param consolidatedAccountIds 合收户ID集合
     * @return 是否删除成功
     */
    Boolean deleteByConsolidatedAccountIds(Collection<Long> consolidatedAccountIds);
}
