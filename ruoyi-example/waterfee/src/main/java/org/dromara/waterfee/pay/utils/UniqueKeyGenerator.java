package org.dromara.waterfee.pay.utils;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicInteger;

public class UniqueKeyGenerator {

    private static final SecureRandom random = new SecureRandom();
    private static final AtomicInteger counter = new AtomicInteger(0);


    public static String generateUniqueKey(String prefix) {
        // 1. 日期部分（精确到天）
        String datePart = Instant.now().toString().substring(0, 10).replace("-", "");

        // 2. 序列号（单机自增，保证同一毫秒内的唯一性）
        int seq = counter.getAndIncrement() % 99999; // 限制范围防止溢出
        String sequence = String.format("%05d", seq);

        // 3. 随机后缀（增强唯一性）
        String randomSuffix = generateRandomHex(8);

        return prefix + datePart + "-" + sequence + "-" + randomSuffix;
    }

    private static String generateRandomHex(int length) {
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString().substring(0, length);
    }

}
