package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArSplitVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArSplitBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageArSplitService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 应收账分账记录
 * 前端访问路由地址为:/waterfee/chargeManageArSplit
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageArSplit")
public class WaterfeeChargeManageArSplitController extends BaseController {

    private final IWaterfeeChargeManageArSplitService waterfeeChargeManageArSplitService;

    /**
     * 查询应收账分账记录列表
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageArSplitVo> list(WaterfeeChargeManageArSplitBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageArSplitService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应收账分账记录列表
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:export")
    @Log(title = "应收账分账记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageArSplitBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageArSplitVo> list = waterfeeChargeManageArSplitService.queryList(bo);
        ExcelUtil.exportExcel(list, "应收账分账记录", WaterfeeChargeManageArSplitVo.class, response);
    }

    /**
     * 获取应收账分账记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageArSplit> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageArSplitService.queryById(id));
    }

    /**
     * 新增应收账分账记录
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:add")
    @Log(title = "应收账分账记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageArSplitBo bo) {
        return toAjax(waterfeeChargeManageArSplitService.insertByBo(bo));
    }

    /**
     * 修改应收账分账记录
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:edit")
    @Log(title = "应收账分账记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageArSplitBo bo) {
        return toAjax(waterfeeChargeManageArSplitService.updateByBo(bo));
    }

    /**
     * 删除应收账分账记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageArSplit:remove")
    @Log(title = "应收账分账记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageArSplitService.deleteWithValidByIds(List.of(ids), true));
    }
}
