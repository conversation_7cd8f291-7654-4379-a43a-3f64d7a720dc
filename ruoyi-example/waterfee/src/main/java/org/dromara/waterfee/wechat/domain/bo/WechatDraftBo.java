package org.dromara.waterfee.wechat.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.wechat.domain.entity.WechatDraft;

import java.util.Date;

/**
 * 微信草稿业务对象 wechat_draft
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WechatDraft.class)
public class WechatDraftBo extends BaseEntity {

    /**
     * 草稿ID
     */
    @NotNull(message = "草稿ID不能为空", groups = {EditGroup.class})
    private Long draftId;

    /**
     * 微信草稿媒体ID
     */
    private String mediaId;

    /**
     * 草稿标题
     */
    @NotBlank(message = "草稿标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 草稿类型（WATER_OUTAGE-停水通知 WATER_SUPPLY-供水通知）
     */
    private String draftType;

    /**
     * 草稿内容
     */
    @NotBlank(message = "草稿内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 封面图片媒体ID
     */
    private String thumbMediaId;

    /**
     * 封面图片URL
     */
    private String thumbUrl;

    /**
     * 作者
     */
    private String author;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 是否显示封面（0-不显示 1-显示）
     */
//    private Boolean showCoverPic;

    /**
     * 原文链接
     */
    private String contentSourceUrl;

    /**
     * 是否开启评论（0-不开启 1-开启）
     */
    private Boolean needOpenComment;

    /**
     * 是否粉丝才可评论（0-所有人 1-仅粉丝）
     */
    private Boolean onlyFansCanComment;

    /**
     * 草稿状态（DRAFT-草稿 PUBLISHED-已发布）
     */
    private String draftStatus;

    /**
     * 文章状态（UNPUBLISHED-未发布文章 PUBLISHED-已发布文章）
     */
    private String articleStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 文章发布时间
     */
    private Date articlePublishTime;

    /**
     * 备注
     */
    private String remark;
}
