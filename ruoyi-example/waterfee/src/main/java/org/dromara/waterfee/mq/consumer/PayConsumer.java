package org.dromara.waterfee.mq.consumer;

import com.google.gson.Gson;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.model.RefundNotification;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.PaymentDetailVo;
import org.dromara.waterfee.bill.enums.PaymentStatusEnum;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.bill.service.IPaymentDetailService;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.income.service.IWaterfeeIncomeRecordService;
import org.dromara.waterfee.mq.config.RabbitConfig;
import org.dromara.waterfee.pay.domain.WechatRefundParam;
import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.waterfee.pay.enums.RefundStatusEnums;
import org.dromara.waterfee.pay.service.IWechatRefundRecordService;
import org.dromara.waterfee.pay.service.IWechatRefundService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.lock.UserLockManager;
import org.dromara.waterfee.user.service.IWaterfeeUserBalanceChangeRecordService;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class PayConsumer {

    @Autowired
    private IPaymentDetailService paymentDetailService;

    @Autowired
    private IWechatRefundService wechatRefundService;

    @Autowired
    private IWechatRefundRecordService wechatRefundRecordService;

    @Autowired
    private IWaterfeeUserService waterfeeUserService;

    @Autowired
    private UserLockManager userLockManager;

    @Autowired
    private IWaterfeeIncomeRecordService incomeRecordService;

    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    @Autowired
    private IWaterfeeBillService billService;

    @Autowired
    private IWaterfeeUserBalanceChangeRecordService balanceChangeRecordService;

    @RabbitListener(queues = RabbitConfig.PAY_CALL_BACK_QUEUE)
    public void listenPayCallBackQueue(Message message) {
        log.info("【消费者】Start consuming data：{}", new String(message.getBody()));
        Gson gson = new Gson();
        Transaction transaction = gson.fromJson(new String(message.getBody()), Transaction.class);
        if (transaction.getOutTradeNo() != null && StringUtils.isNotEmpty(transaction.getOutTradeNo())) {
            PaymentDetailVo paymentDetailVo = paymentDetailService.queryById(transaction.getOutTradeNo());
            if (paymentDetailVo == null) {
                log.error("订单不存在，订单号：{}", transaction.getOutTradeNo());
                return;
            }
            paymentDetailVo.setTransactionId(transaction.getTransactionId());
            if (transaction.getTradeState().equals(Transaction.TradeStateEnum.SUCCESS)) {
                paymentDetailVo.setPaymentStatus("SUCCESS");
                //比较金额信息，防止金额不匹配导致的错误支付
                if (paymentDetailVo.getPaymentAmount().multiply(new BigDecimal(100)).compareTo(new BigDecimal(transaction.getAmount().getTotal())) != 0) {
                    log.error("支付金额不匹配，开启退款流程，订单号：{}", transaction.getOutTradeNo());
                    //开始退款流程
                    WechatRefundParam wechatRefundParam = new WechatRefundParam();
                    wechatRefundParam.setOutTradeNo(transaction.getOutTradeNo());
                    wechatRefundParam.setTransactionId(transaction.getTransactionId());
                    wechatRefundParam.setReason("支付金额不匹配");
                    wechatRefundParam.setRefundFee(transaction.getAmount().getTotal().longValue());
                    wechatRefundParam.setTotalFee(transaction.getAmount().getTotal().longValue());
                    wechatRefundService.applyForRefund(wechatRefundParam);
                    paymentDetailVo.setPaymentStatus(PaymentStatusEnum.REFUNDING.getCode());
                } else {
                    //支付成功 执行其他的业务业务逻辑，比如校验是否停水后自动供水;更新账户余额等;
                    paymentDetailVo.setPaymentStatus("SUCCESS");
                    //支付订单or预存
                    if(paymentDetailVo.getBillId() != null) {
                        billService.payBill(paymentDetailVo.getBillId(), new BigDecimal(transaction.getAmount().getTotal()));
                    }else {
                        // 更新用户余额
                        updateUserBalance(paymentDetailVo);
                    }
                }
            } else if (transaction.getTradeState().equals(Transaction.TradeStateEnum.NOTPAY)) {
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
            } else if (transaction.getTradeState().equals(Transaction.TradeStateEnum.CLOSED)) {
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
            } else if (transaction.getTradeState().equals(Transaction.TradeStateEnum.REFUND)) {
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.REFUND.getCode());
            } else {
                log.error("支付状态未知，订单号：{}, 状态：{}", transaction.getOutTradeNo(), transaction.getTradeState());
            }
            // 更新订单状态
            PaymentDetailBo paymentDetailBo = new PaymentDetailBo();
            BeanUtils.copyProperties(paymentDetailVo, paymentDetailBo);
            paymentDetailService.updateByBo(paymentDetailBo);
        } else {
            log.error("支付回调信息不完整，订单号：{}", transaction.getOutTradeNo());
        }

    }

    /**
     * 更新用户余额
     *
     * @param paymentDetailVo 支付详情
     */
    @Transactional(rollbackFor = Exception.class)
    private void updateUserBalance(PaymentDetailVo paymentDetailVo) {
        if (paymentDetailVo.getUserNo() == null) {
            log.error("支付详情中户号为空，无法更新余额，订单号：{}", paymentDetailVo.getPaymentDetailId());
            return;
        }

        String userNo = paymentDetailVo.getUserNo();
        BigDecimal paymentAmount = paymentDetailVo.getPaymentAmount();

        // 获取用户锁
        ReentrantLock lock = userLockManager.getUserLock(userNo);
        try {
            // 尝试获取锁，最多等待3秒
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    // 查询用户信息
                    WaterfeeUser user = waterfeeUserService.queryByUserNo(userNo);
                    if (user == null) {
                        log.error("用户不存在，户号：{}", userNo);
                        return;
                    }

                    // 更新用户余额
                    BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                    BigDecimal newBalance = currentBalance.add(paymentAmount);
                    user.setBalance(newBalance);

                    // 保存更新后的用户信息
                    boolean updated = waterfeeUserService.updateById(user);
                    if (updated) {
                        log.info("用户余额更新成功，户号：{}，当前余额：{}，支付金额：{}，更新后余额：{}",
                            userNo, currentBalance, paymentAmount, newBalance);

                        // 记录余额变更
                        balanceChangeRecordService.recordBalanceChange(
                            user.getUserId(), userNo, "RECHARGE",
                            currentBalance, paymentAmount, newBalance,
                            paymentDetailVo.getPaymentDetailId(), "DEPOSIT", "预存充值",
                            "系统", "预存充值"
                        );
                    } else {
                        log.error("用户余额更新失败，户号：{}", userNo);
                    }
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            } else {
                log.warn("获取用户余额锁超时，户号：{}", userNo);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("更新用户余额时被中断，户号：{}", userNo, e);
        } catch (Exception e) {
            log.error("更新用户余额异常，户号：{}", userNo, e);
        }
    }

    @RabbitListener(queues = RabbitConfig.REFUND_CALL_BACK_QUEUE)
    public void listenRefundCallBackQueue(Message message) {
        log.info("【消费者】Start consuming data：{}", new String(message.getBody()));
        Gson gson = new Gson();
        RefundNotification refundNotification = gson.fromJson(new String(message.getBody()), RefundNotification.class);
        if (refundNotification.getOutRefundNo() != null && StringUtils.isNotEmpty(refundNotification.getOutRefundNo())) {
            WechatRefundRecord wechatRefundRecord = wechatRefundRecordService.queryById(refundNotification.getOutRefundNo());
            if (wechatRefundRecord == null) {
                log.error("退款记录不存在，退款单号：{}", refundNotification.getOutRefundNo());
                return;
            }
            wechatRefundRecord.setWechatRefundId(refundNotification.getRefundId());
            if (refundNotification.getSuccessTime() != null && StringUtils.isNotEmpty(refundNotification.getSuccessTime())) {
                // 定义 RFC 3339 格式化器
                DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

                // 解析为 OffsetDateTime（自动处理时区）
                OffsetDateTime offsetDateTime = OffsetDateTime.parse(refundNotification.getSuccessTime(), formatter);

                // 转换为 Instant，再转为 Date
                Date date = Date.from(offsetDateTime.toInstant());
                wechatRefundRecord.setSuccessTime(date);
            }
            //查询支付订单信息
            PaymentDetailVo paymentDetailVo = paymentDetailService.queryById(refundNotification.getOutTradeNo());
            if (paymentDetailVo == null) {
                log.error("退款关联支付订单信息不存在，订单号：{}", refundNotification.getOutTradeNo());
            }
            //根据回调更新退款记录表的信息和支付订单的状态
            wechatRefundRecord.setUserReceivedAccount(refundNotification.getUserReceivedAccount());
            wechatRefundRecord.setPaymentAmount(new BigDecimal(refundNotification.getAmount().getTotal()).divide(new BigDecimal(100)));
            wechatRefundRecord.setPaymentRefund(new BigDecimal(refundNotification.getAmount().getRefund()).divide(new BigDecimal(100)));
            wechatRefundRecord.setPayerAmount(new BigDecimal(refundNotification.getAmount().getPayerTotal()).divide(new BigDecimal(100)));
            wechatRefundRecord.setPayerRefund(new BigDecimal(refundNotification.getAmount().getPayerRefund()).divide(new BigDecimal(100)));
            if (refundNotification.getRefundStatus().equals(RefundStatusEnums.SUCCESS.getCode())) {
                wechatRefundRecord.setRefundStatus(RefundStatusEnums.SUCCESS.getCode());
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.REFUND.getCode());
            } else if (refundNotification.getRefundStatus().equals(RefundStatusEnums.PROCESSING.getCode())) {
                wechatRefundRecord.setRefundStatus(RefundStatusEnums.PROCESSING.getCode());
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.REFUNDING.getCode());
            } else if (refundNotification.getRefundStatus().equals(RefundStatusEnums.CLOSED.getCode())) {
                wechatRefundRecord.setRefundStatus(RefundStatusEnums.CLOSED.getCode());
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
            } else if (refundNotification.getRefundStatus().equals(RefundStatusEnums.ABNORMAL.getCode())) {
                wechatRefundRecord.setRefundStatus(RefundStatusEnums.ABNORMAL.getCode());
                paymentDetailVo.setPaymentStatus(PaymentStatusEnum.REFUND_ABNORMAL.getCode());
            } else {
                wechatRefundRecord.setRefundStatus("Unknown");
                log.error("退款状态未知，退款单号：{}, 状态：{}", refundNotification.getOutRefundNo(), refundNotification.getRefundStatus());
            }
            //更新退款记录表的信息和支付订单的状态
            WechatRefundRecordBo wechatRefundRecordBo = new WechatRefundRecordBo();
            BeanUtils.copyProperties(wechatRefundRecord, wechatRefundRecordBo);
            wechatRefundRecordService.updateByBo(wechatRefundRecordBo);
            PaymentDetailBo paymentDetailBo = new PaymentDetailBo();
            BeanUtils.copyProperties(paymentDetailVo, paymentDetailBo);
            paymentDetailService.updateByBo(paymentDetailBo);
        } else {
            log.error("退款回调信息不完整，退款单号：{}", refundNotification.getOutRefundNo());
        }
    }

}
