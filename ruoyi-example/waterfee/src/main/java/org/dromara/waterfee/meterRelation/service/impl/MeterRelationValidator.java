package org.dromara.waterfee.meterRelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterRelation;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterRelationMapper;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:17
 **/

@Component
@RequiredArgsConstructor
public class MeterRelationValidator {

    private final WaterfeeMeterRelationMapper relationMapper;

    /**
     * 校验是否存在循环依赖
     */
    public void validateNoLoop(Long parentId, Long childId) {
        if (Objects.equals(parentId, childId)) {
            throw new RuntimeException("不能将水表自己作为子表");
        }

        Set<Long> visited = new HashSet<>();
        Queue<Long> queue = new LinkedList<>();
        queue.add(childId); // 从 childId 向下查找，看是否能回到 parentId

        while (!queue.isEmpty()) {
            Long current = queue.poll();
            if (Objects.equals(current, parentId)) {
                throw new RuntimeException("添加关系会导致循环依赖，请检查关系链");
            }
            if (visited.contains(current)) continue;
            visited.add(current);

            List<WaterfeeMeterRelation> children = relationMapper.selectList(new LambdaQueryWrapper<WaterfeeMeterRelation>()
                .eq(WaterfeeMeterRelation::getParentMeterId, current)
                .eq(WaterfeeMeterRelation::getDelFlag, "0"));
            children.forEach(rel -> queue.add(rel.getChildMeterId()));
        }
    }
}
