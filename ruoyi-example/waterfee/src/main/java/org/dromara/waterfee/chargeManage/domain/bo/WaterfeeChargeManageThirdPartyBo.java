package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 第三方对账记录业务对象 waterfee_charge_manage_third_party
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageThirdParty.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageThirdPartyBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 渠道编码（UNIONPAY、WECHAT等）
     */
    @NotBlank(message = "渠道编码（UNIONPAY、WECHAT等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelCode;

    /**
     * 本地订单号
     */
    @NotBlank(message = "本地订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String localOrderNo;

    /**
     * 第三方订单号
     */
    private String thirdOrderNo;

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 状态（MATCHED、MISMATCHED、PENDING）
     */
    private String status;

    /**
     * 对账日期
     */
    private Date reconciliationDate;

    /**
     * 
     */
    private String remark;


}
