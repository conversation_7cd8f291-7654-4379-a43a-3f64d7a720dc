package org.dromara.waterfee.invoice.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class InvoiceCancellationParam {

    /**
     * 发票流水号
     */
    @NotBlank(message = "发票流水号不能为空")
    private String serialNo;

    /**
     * 发票代码
     */
    @NotBlank(message = "发票代码不能为空")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @NotBlank(message = "发票号码不能为空")
    private String invoiceNo;

}
