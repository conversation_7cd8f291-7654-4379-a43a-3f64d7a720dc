package org.dromara.waterfee.user.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

@Data
public class WaterfeeUserBasicInfoBo extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 营业区域id
     */
    @NotNull(message = "营业区域id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long areaId;

    /**
     * 小区id
     */
    @NotNull(message = "小区id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long communityId;

    /**
     * 单元房号
     */
    @NotBlank(message = "单元房号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unitRoomNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @NotBlank(message = "客户性质（字典waterfee_user_customer_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerNature;

    /**
     * 用水性质（字典waterfee_user_use_water_nature）
     */
    @NotBlank(message = "用水性质（字典waterfee_user_use_water_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String useWaterNature;

    /**
     * 用水人数
     */
    @NotNull(message = "用水人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long useWaterNumber;

    /**
     * 用水户名称
     */
    @NotBlank(message = "用水户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateType;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNumber;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    @NotBlank(message = "用户状态（字典waterfee_user_user_status）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userStatus;

    /**
     * 用水地址
     */
    @NotBlank(message = "用水地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 纳税人识别号
     */
//    @NotBlank(message = "纳税人识别号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxpayerIdentificationNumber;

    /**
     * 供水日期
     */
    @NotNull(message = "供水日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date supplyDate;

    /**
     * 开票名称
     */
    private String invoiceName;

    /**
     * 发票类型（字典waterfee_user_invoice_type）
     */
//    @NotBlank(message = "发票类型（字典waterfee_user_invoice_type）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceType;

}
