package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageNonOperatingIncomeBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageNonOperatingIncomeVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageNonOperatingIncomeMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageNonOperatingIncomeService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 营业外收入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageNonOperatingIncomeServiceImpl implements IWaterfeeChargeManageNonOperatingIncomeService {

    private final WaterfeeChargeManageNonOperatingIncomeMapper baseMapper;

    /**
     * 查询营业外收入记录
     *
     * @param id 主键
     * @return 营业外收入记录
     */
    @Override
    public WaterfeeChargeManageNonOperatingIncome queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询营业外收入记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 营业外收入记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageNonOperatingIncomeVo> queryPageList(WaterfeeChargeManageNonOperatingIncomeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageNonOperatingIncome> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageNonOperatingIncomeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的营业外收入记录列表
     *
     * @param bo 查询条件
     * @return 营业外收入记录列表
     */
    @Override
    public List<WaterfeeChargeManageNonOperatingIncomeVo> queryList(WaterfeeChargeManageNonOperatingIncomeBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageNonOperatingIncome> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageNonOperatingIncome> buildQueryWrapper(WaterfeeChargeManageNonOperatingIncomeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageNonOperatingIncome> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageNonOperatingIncome::getCreateTime);
        lqw.eq(bo.getUserId() != null, WaterfeeChargeManageNonOperatingIncome::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getIncomeType()), WaterfeeChargeManageNonOperatingIncome::getIncomeType, bo.getIncomeType());
        lqw.eq(bo.getAmount() != null, WaterfeeChargeManageNonOperatingIncome::getAmount, bo.getAmount());
        lqw.eq(bo.getIncomeTime() != null, WaterfeeChargeManageNonOperatingIncome::getIncomeTime, bo.getIncomeTime());
        return lqw;
    }

    /**
     * 新增营业外收入记录
     *
     * @param bo 营业外收入记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageNonOperatingIncomeBo bo) {
        WaterfeeChargeManageNonOperatingIncome add = MapstructUtils.convert(bo, WaterfeeChargeManageNonOperatingIncome.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改营业外收入记录
     *
     * @param bo 营业外收入记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageNonOperatingIncomeBo bo) {
        WaterfeeChargeManageNonOperatingIncome update = MapstructUtils.convert(bo, WaterfeeChargeManageNonOperatingIncome.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageNonOperatingIncome entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除营业外收入记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
