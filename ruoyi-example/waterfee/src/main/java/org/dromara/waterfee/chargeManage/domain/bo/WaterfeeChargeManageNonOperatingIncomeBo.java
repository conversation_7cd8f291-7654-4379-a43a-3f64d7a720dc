package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 营业外收入记录业务对象 waterfee_charge_manage_non_operating_income
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageNonOperatingIncome.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageNonOperatingIncomeBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收入类型
     */
    @NotBlank(message = "收入类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String incomeType;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 收入时间
     */
    @NotNull(message = "收入时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date incomeTime;

    /**
     * 
     */
    private String remark;


}
