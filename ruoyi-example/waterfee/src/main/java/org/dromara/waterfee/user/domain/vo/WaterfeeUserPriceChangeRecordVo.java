package org.dromara.waterfee.user.domain.vo;

import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.waterfee.user.domain.WaterfeeUserPriceChangeRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用水用户用水价格变更记录视图对象 waterfee_user_price_change_record
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeUserPriceChangeRecord.class)
public class WaterfeeUserPriceChangeRecordVo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long priceChangeId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 原价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    @ExcelProperty(value = "原价格-用水性质", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_use_water_nature")
//    private String beforePriceUseWaterNature;

    /**
     * 原计费方式（字典waterfee_user_billing_method）
     */
    @ExcelProperty(value = "原计费方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_billing_method")
    private String beforeBillingMethod;

    private String beforeBillingMethodName;

    /**
     * 原是否有违约金（字典yes_no）
     */
//    @ExcelProperty(value = "原是否有违约金", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String beforeIfPenalty;

    /**
     * 原违约金类型（字典waterfee_user_penalty_type）
     */
//    @ExcelProperty(value = "原违约金类型", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_penalty_type")
//    private String beforePenaltyType;

    /**
     * 原是否有附加费（字典yes_no）
     */
//    @ExcelProperty(value = "原是否有附加费", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String beforeIfExtraCharge;

    /**
     * 原附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
//    @ExcelProperty(value = "原附加费内容", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_extra_charge_type")
//    private String beforeExtraChargeType;

    /**
     * 新价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    @ExcelProperty(value = "新价格-用水性质", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_use_water_nature")
//    private String afterPriceUseWaterNature;

    /**
     * 新计费方式（字典waterfee_user_billing_method）
     */
    @ExcelProperty(value = "新计费方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_billing_method")
    private String afterBillingMethod;

    private String afterBillingMethodName;

    /**
     * 新是否有违约金（字典yes_no）
     */
//    @ExcelProperty(value = "新是否有违约金", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String afterIfPenalty;

    /**
     * 新违约金类型（字典waterfee_user_penalty_type）
     */
//    @ExcelProperty(value = "新违约金类型", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_penalty_type")
//    private String afterPenaltyType;

    /**
     * 新是否有附加费（字典yes_no）
     */
//    @ExcelProperty(value = "新是否有附加费", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String afterIfExtraCharge;

    /**
     * 新附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
//    @ExcelProperty(value = "新附加费内容", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_extra_charge_type")
//    private String afterExtraChargeType;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用水户名称")
    private String userName;

    /**
     * 创建人名称
     */
    @ExcelProperty(value = "处理人")
    private String CreateByUserName;
}
