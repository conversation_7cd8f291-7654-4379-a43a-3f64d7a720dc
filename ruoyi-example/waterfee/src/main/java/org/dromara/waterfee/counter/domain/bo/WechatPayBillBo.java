package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;

import java.math.BigDecimal;

/**
 * 微信支付账单业务对象
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class WechatPayBillBo {

    /**
     * 账单ID
     */
    @NotNull(message = "账单ID不能为空", groups = {AddGroup.class})
    private Long billId;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class})
    @PositiveOrZero(message = "支付金额必须>=0", groups = {AddGroup.class})
    private BigDecimal amount;

    /**
     * 微信用户openid
     */
    @NotNull(message = "微信openid不能为空", groups = {AddGroup.class})
    private String openid;

}
