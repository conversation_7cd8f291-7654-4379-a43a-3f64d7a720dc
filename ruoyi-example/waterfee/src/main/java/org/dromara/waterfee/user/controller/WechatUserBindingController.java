package org.dromara.waterfee.user.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.user.domain.WechatUserBinding;
import org.dromara.waterfee.user.domain.bo.WechatUserBindingBo;
import org.dromara.waterfee.user.domain.vo.WechatUserBindingVo;
import org.dromara.waterfee.user.domain.vo.WechatUserInfoVo;
import org.dromara.waterfee.user.service.IWechatUserBindingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 公众号绑定户号缴费
 *
 * <AUTHOR>
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wechatUserBinding")
public class WechatUserBindingController extends BaseController {

    private final IWechatUserBindingService wechatUserBindingService;

    @GetMapping("/list/{openid}")
    public R queryBindingUserListByOpenid(@PathVariable("openid") String openid, HttpServletRequest request, HttpServletResponse response) {
        return R.ok(wechatUserBindingService.queryBindingUserListByOpenid(openid, request, response));
    }

    /**
     * 根据用户编码查询用户信息
     *
     * @param userNo 用户编码
     * @return 用户信息（包含脱敏处理的姓名）
     */
    @GetMapping("/userInfo/{userNo}")
    public R<WechatUserInfoVo> queryUserInfoByUserNo(@NotBlank(message = "用户编码不能为空")
                                             @PathVariable("userNo") String userNo) {
        WechatUserInfoVo userInfo = wechatUserBindingService.queryUserInfoByUserNo(userNo);
        return R.ok(userInfo);
    }

    /**
     * 绑定户号
     * @param wechatUserBinding
     * @return
     */
    @PostMapping("/bindUser")
    public R bindUser(@RequestBody WechatUserBinding wechatUserBinding) {
        return toAjax(wechatUserBindingService.bindUser(wechatUserBinding));
    }

    /**
     * 解绑户号
     * @param wechatUserBinding
     * @return 结果
     */
    @PostMapping("/unbindUser")
    public R unbindUser(@RequestBody WechatUserBinding wechatUserBinding) {
        return toAjax(wechatUserBindingService.unbindUser(wechatUserBinding));
    }

    /**
     * 根据id查询用户余额
     */
    @GetMapping("/balance/{wechatUserId}")
    public R<WechatUserBindingVo> getBalance(@PathVariable("wechatUserId") Long wechatUserId) {
        return R.ok(wechatUserBindingService.queryUserBalanceById(wechatUserId));
    }

}
