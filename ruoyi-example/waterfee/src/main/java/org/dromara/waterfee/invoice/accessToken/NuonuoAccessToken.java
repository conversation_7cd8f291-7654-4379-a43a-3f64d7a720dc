package org.dromara.waterfee.invoice.accessToken;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNOpenSDK;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.waterfee.invoice.service.INuonuoTokenService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class NuonuoAccessToken {

    /** 填写应用的appKey */
    @Value("${invoice.nuonuo.client_id}")
    private String clientId;

    /** 填写应用的appSecret */
    @Value("${invoice.nuonuo.client_secret}")
    private String clientSecret;

    @Value("${invoice.nuonuo.get_token_url}")
    private String getTokenUrl;

    private final String grantType = "client_credential";

    /**
     * 获取诺诺发票平台的访问令牌
     *
     * @return 包含access_token和expires_in的Map
     */
    public Map<String, Object> applyForAccessToken() {
        try {
            log.info("开始获取诺诺发票平台访问令牌");

//            // 构建请求参数
//            Map<String, Object> params = new HashMap<>();
//            params.put("client_id", clientId);
//            params.put("client_secret", clientSecret);
//            params.put("grant_type", grantType);
//
//            // 发送POST请求，设置Content-Type为application/x-www-form-urlencoded
//            String result = HttpUtil.createPost(getTokenUrl)
//                .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
//                .form(params)
//                .execute()
//                .body();

            String result = NNOpenSDK.getIntance().getMerchantToken(clientId,clientSecret);

            log.info("获取诺诺发票平台访问令牌响应: {}", result);

            // 解析响应JSON
            Map<String, Object> resultMap = JsonUtils.parseMap(result);

            // 检查响应是否包含错误信息
            if (resultMap.containsKey("error")) {
                log.error("获取诺诺发票平台访问令牌失败: {}", MapUtil.getStr(resultMap, "error_description", "未知错误"));
                throw new ServiceException(MapUtil.getStr(resultMap, "error_description", "获取访问令牌失败"));
            }

            // 验证必要的字段是否存在
            if (!resultMap.containsKey("access_token") || !resultMap.containsKey("expires_in")) {
                log.error("获取诺诺发票平台访问令牌失败: 响应中缺少必要字段");
                throw new ServiceException("获取诺诺发票平台访问令牌失败: 响应中缺少必要字段");
            }

            log.info("成功获取诺诺发票平台访问令牌");
            return resultMap;
        } catch (Exception e) {
            log.error("获取诺诺发票平台访问令牌异常", e);
            throw new ServiceException("获取诺诺发票平台访问令牌失败: " + e.getMessage());
        }
    }

}
