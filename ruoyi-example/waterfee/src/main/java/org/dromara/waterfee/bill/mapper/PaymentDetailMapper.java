package org.dromara.waterfee.bill.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.PaymentDetailVo;
import org.dromara.waterfee.statisticalReport.domain.OnlinePaymentDetailSummaryVO;
import org.dromara.waterfee.statisticalReport.domain.PaymentDetailsVO;

import java.util.List;

/**
 * 缴费明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface PaymentDetailMapper extends BaseMapperPlus<PaymentDetail, PaymentDetailVo> {
    IPage<PaymentDetailVo> selectVoPage(@Param("page") Page<PaymentDetailVo> page,
                                        @Param("bo") PaymentDetailBo bo);

    /**
     * 收款明细表（搜索条件是收费员、支付方式、起始日期和截止日期）
     * @param tollCollector
     * @param paymentMethod
     * @param startTime
     * @param endTime
     * @return
     */
    List<PaymentDetailsVO> getPaymentDetails(@Param("tollCollector") String tollCollector,
                                             @Param("paymentMethod") String paymentMethod,
                                             @Param("startTime") String startTime,
                                             @Param("endTime") String endTime);

    /**
     * 网上缴费明细汇总表（搜索条件是支付方式）
     * @param paymentMethod
     * @return
     */
    List<OnlinePaymentDetailSummaryVO> getOnlinePaymentDetailSummary(String paymentMethod);
}
