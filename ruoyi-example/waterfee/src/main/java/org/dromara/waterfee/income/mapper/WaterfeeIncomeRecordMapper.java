package org.dromara.waterfee.income.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.income.domain.WaterfeeIncomeRecord;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeSummaryVo;

/**
 * 收入记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
public interface WaterfeeIncomeRecordMapper extends BaseMapperPlus<WaterfeeIncomeRecord, WaterfeeIncomeRecordVo> {

    /**
     * 查询收入记录列表，支持数据权限
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 收入记录列表
     */
    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeIncomeRecordVo>> P selectVoPage(IPage<WaterfeeIncomeRecord> page,
                                                                     Wrapper<WaterfeeIncomeRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
     * 查询收入汇总信息
     *
     * @return WaterfeeIncomeSummaryVo
     */
    WaterfeeIncomeSummaryVo selectIncomeSummary();

}
