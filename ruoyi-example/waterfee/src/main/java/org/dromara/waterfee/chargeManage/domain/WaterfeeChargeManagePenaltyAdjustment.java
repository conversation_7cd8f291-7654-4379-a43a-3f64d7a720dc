package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 违约金调整/减免对象 waterfee_charge_manage_penalty_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_penalty_adjustment")
public class WaterfeeChargeManagePenaltyAdjustment extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private Long billId;

    /**
     * 原违约金
     */
    private Long originalPenalty;

    /**
     * 调整后违约金
     */
    private Long adjustedPenalty;

    /**
     * 
     */
    private String reason;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
