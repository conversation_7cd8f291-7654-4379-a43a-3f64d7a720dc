package org.dromara.waterfee.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.PaymentDetailVo;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.bill.service.IPaymentDetailService;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 缴费明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@RequiredArgsConstructor
@Service
public class PaymentDetailServiceImpl implements IPaymentDetailService {

    private final PaymentDetailMapper baseMapper;
    private final IWaterfeeBillService waterfeeBillService; // 注入账单服务

    private final IWaterfeeUserService waterfeeUserService; // 注入用户服务

    /**
     * 查询缴费明细
     */
    @Override
    public PaymentDetailVo queryById(String paymentDetailId) {
        PaymentDetailVo paymentDetailVo = baseMapper.selectVoById(paymentDetailId);
        if (paymentDetailVo == null) {
            return null;
        }
        if (StringUtils.isNotBlank(paymentDetailVo.getUserNo())) {
            WaterfeeUser waterfeeUser = waterfeeUserService.queryByUserNo(paymentDetailVo.getUserNo());
            if (waterfeeUser == null) {
                throw new RuntimeException("用户不存在");
            }
            paymentDetailVo.setUserName(waterfeeUser.getUserName());
        }
        return paymentDetailVo;
    }

    /**
     * 查询缴费明细列表
     */
    @Override
    public TableDataInfo<PaymentDetailVo> queryPageList(PaymentDetailBo bo, PageQuery pageQuery) {
        Page<PaymentDetailVo> page = pageQuery.build();
        IPage<PaymentDetailVo> result = baseMapper.selectVoPage(page, bo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询缴费明细列表
     */
    @Override
    public List<PaymentDetailVo> queryList(PaymentDetailBo bo) {
        LambdaQueryWrapper<PaymentDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PaymentDetail> buildQueryWrapper(PaymentDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PaymentDetail> lqw = Wrappers.lambdaQuery();

        lqw.eq(StringUtils.isNotBlank(bo.getBillId()), PaymentDetail::getBillId, bo.getBillId());
        lqw.eq(bo.getPaymentAmount() != null, PaymentDetail::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), PaymentDetail::getPaymentMethod, bo.getPaymentMethod());
        lqw.like(StringUtils.isNotBlank(bo.getTransactionId()), PaymentDetail::getTransactionId, bo.getTransactionId());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentStatus()), PaymentDetail::getPaymentStatus, bo.getPaymentStatus());
        // Handle date range query for paymentTime
        if (params.get("beginPaymentTime") != null && params.get("endPaymentTime") != null) {
            lqw.between(PaymentDetail::getPaymentTime, params.get("beginPaymentTime"), params.get("endPaymentTime"));
        }
        return lqw;
    }

    /**
     * 新增缴费明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // Ensure atomicity
    public Boolean insertByBo(PaymentDetailBo bo) {
        PaymentDetail add = MapstructUtils.convert(bo, PaymentDetail.class);
        // Set default payment time if not provided
//        if (add.getPaymentTime() == null) {
//            add.setPaymentTime(new java.util.Date());
//        }
        // Assume payment is successful for now, set status accordingly
        // In a real scenario, this status might come from the payment gateway notification
//        if (StringUtils.isBlank(add.getPaymentStatus())) {
//            add.setPaymentStatus("SUCCESS"); // Or use a constant/enum
//        }
        boolean insertSuccess = baseMapper.insert(add) > 0;
//        if (insertSuccess && "SUCCESS".equals(add.getPaymentStatus())) {
//            bo.setPaymentDetailId(add.getPaymentDetailId()); // Update BO with the generated ID if needed elsewhere
//            // Update the related bill's status and amounts
//            try {
//                // Assuming billId in PaymentDetailBo corresponds to WaterfeeBill's Long billId
//                Long billIdLong = Long.parseLong(bo.getBillId());
//                // Call the bill service to handle payment update logic
//                boolean billUpdateSuccess = waterfeeBillService.payBill(billIdLong, bo.getPaymentAmount());
//                if (!billUpdateSuccess) {
//                    // Handle bill update failure, maybe throw exception to rollback
//                    throw new RuntimeException("更新账单状态失败, billId: " + billIdLong);
//                }
//            } catch (NumberFormatException e) {
//                throw new RuntimeException("无效的账单ID格式: " + bo.getBillId(), e);
//            } catch (Exception e) {
//                // Log error and rethrow to ensure transaction rollback
//                // log.error("处理缴费更新账单失败", e);
//                throw new RuntimeException("处理缴费更新账单失败: " + e.getMessage(), e);
//            }
//        }
        return insertSuccess;
    }

    /**
     * 修改缴费明细
     */
    @Override
    public Boolean updateByBo(PaymentDetailBo bo) {
        // Generally, payment details are immutable once created.
        // If updates are needed, consider what fields are allowed to change.
        // For example, maybe only remark or status (e.g., refund) can be updated.
        PaymentDetail update = MapstructUtils.convert(bo, PaymentDetail.class);
        // Add validation or specific logic for allowed updates here
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除缴费明细
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        // Deleting payment details might have financial implications.
        // Usually, payments are marked as refunded or cancelled rather than deleted.
        // Implement deletion logic carefully based on requirements.
        if (isValid) {
            // Add validation if needed, e.g., check if associated bill allows deletion
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据微信openid查询缴费明细列表（按收入时间倒序）
     */
    @Override
    public TableDataInfo<PaymentDetailVo> queryByOpenidOrderByIncomeTime(String openid, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<PaymentDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(openid), PaymentDetail::getOpenid, openid);
        lqw.eq(PaymentDetail::getPaymentStatus, "SUCCESS");
        // 按收入时间倒序排列（最近的在前面）
        lqw.orderByDesc(PaymentDetail::getIncomeTime);

        // 分页查询
        Page<PaymentDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        return TableDataInfo.build(result);
    }
}
