package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;

import java.math.BigDecimal;

/**
 * 预存充值业务对象
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
public class WaterfeeDepositBo {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class})
    private Long userId;

    /**
     * 充值金额
     */
    @NotNull(message = "充值金额不能为空", groups = {AddGroup.class})
    @PositiveOrZero(message = "充值金额必须>=0", groups = {AddGroup.class})
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空", groups = {AddGroup.class})
    private String paymentMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收费员
     */
    private String tollCollector;
}
