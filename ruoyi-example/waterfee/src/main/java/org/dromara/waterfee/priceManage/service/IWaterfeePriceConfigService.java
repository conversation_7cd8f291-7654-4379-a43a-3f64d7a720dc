package org.dromara.waterfee.priceManage.service;

import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeePriceConfigBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 阶梯价格配置Service接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IWaterfeePriceConfigService {

    /**
     * 查询阶梯价格配置
     *
     * @param id 主键
     * @return 阶梯价格配置
     */
    WaterfeePriceConfigVo queryById(Long id);

    /**
     * 分页查询阶梯价格配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 阶梯价格配置分页列表
     */
    TableDataInfo<WaterfeePriceConfigVo> queryPageList(WaterfeePriceConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的阶梯价格配置列表
     *
     * @param bo 查询条件
     * @return 阶梯价格配置列表
     */
    List<WaterfeePriceConfigVo> queryList(WaterfeePriceConfigBo bo);

    /**
     * 新增阶梯价格配置
     *
     * @param bo 阶梯价格配置
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeePriceConfigBo bo);

    /**
     * 修改阶梯价格配置
     *
     * @param bo 阶梯价格配置
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeePriceConfigBo bo);

    /**
     * 校验并批量删除阶梯价格配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取价格配置下拉选择框数据
     *
     * @return ID和名称的Map集合，key为id，value为name
     */
    Map<String, String> getSelectPriceConfigMap();
}
