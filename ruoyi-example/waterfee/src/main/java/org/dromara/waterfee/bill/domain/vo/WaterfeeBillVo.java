package org.dromara.waterfee.bill.domain.vo;

import org.dromara.waterfee.bill.domain.WaterfeeBill;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serial;
import java.io.Serializable;

/**
 * 账单管理视图对象 waterfee_bills
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeBill.class)
public class WaterfeeBillVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账单唯一ID
     */
    @ExcelProperty(value = "账单唯一ID")
    private Long billId;

    /**
     * 账单编号 (业务)
     */
    @ExcelProperty(value = "账单编号")
    private String billNumber;

    /**
     * 关联客户ID
     */
    @ExcelProperty(value = "关联客户ID")
    private Long customerId;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 关联水表ID
     */
    @ExcelProperty(value = "关联水表ID")
    private Long meterId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 表册ID
     */
    @ExcelProperty(value = "表册ID")
    private Long meterBookId;

    /**
     * 表册名称
     */
    @ExcelProperty(value = "表册名称")
    private String bookName;

    /**
     * 价格方案ID
     */
    @ExcelProperty(value = "价格方案ID")
    private Long pricePlanId;

    /**
     * 价格方案名称
     */
    @ExcelProperty(value = "价格方案名称")
    private String pricePlanName;

    /**
     * 计费周期开始日期
     */
    @ExcelProperty(value = "计费周期开始日期")
    private Date billingPeriodStart;

    /**
     * 计费周期结束日期
     */
    @ExcelProperty(value = "计费周期结束日期")
    private Date billingPeriodEnd;

    /**
     * 账单发行日期
     */
    @ExcelProperty(value = "账单发行日期")
    private Date billingIssueDate;

    /**
     * 账单到期日期
     */
    @ExcelProperty(value = "账单到期日期")
    private Date billingDueDate;

    /**
     * 上期结算读数ID
     */
    private Long previousReadingId;

    /**
     * 本期结算读数ID
     */
    private Long currentReadingId;

    /**
     * 上期读数值
     */
    @ExcelProperty(value = "上期读数值")
    private BigDecimal previousReadingValue;

    /**
     * 本期读数值
     */
    @ExcelProperty(value = "本期读数值")
    private BigDecimal currentReadingValue;

    /**
     * 本期消费量
     */
    @ExcelProperty(value = "本期消费量")
    private BigDecimal consumptionVolume;

    /**
     * 消费量单位
     */
    @ExcelProperty(value = "消费量单位")
    private String consumptionUnit;

    /**
     * 标准水费 (标准用量费用)
     */
    @ExcelProperty(value = "标准水费")
    private BigDecimal baseChargeAmount;

    /**
     * 阶梯1消费量
     */
    @ExcelProperty(value = "阶梯1消费量")
    private BigDecimal tier1;

    /**
     * 阶梯1费用
     */
    @ExcelProperty(value = "阶梯1费用")
    private BigDecimal tier1Amount;

    /**
     * 阶梯2消费量
     */
    @ExcelProperty(value = "阶梯2消费量")
    private BigDecimal tier2;

    /**
     * 阶梯2费用
     */
    @ExcelProperty(value = "阶梯2费用")
    private BigDecimal tier2Amount;

    /**
     * 阶梯3消费量
     */
    @ExcelProperty(value = "阶梯3消费量")
    private BigDecimal tier3;

    /**
     * 阶梯3费用
     */
    @ExcelProperty(value = "阶梯3费用")
    private BigDecimal tier3Amount;

    /**
     * 违约金
     */
    @ExcelProperty(value = "违约金")
    private BigDecimal surchargeAmount;

    /**
     * 附加费用 (如 污水处理费)
     */
    @ExcelProperty(value = "附加费用")
    private BigDecimal additionalChargeAmount;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 调整金额 (正数为增加, 负数为减免)
     */
    @ExcelProperty(value = "调整金额")
    private BigDecimal adjustmentsAmount;

    /**
     * 账单总金额 (含调整)
     */
    @ExcelProperty(value = "账单总金额")
    private BigDecimal totalAmount;

    /**
     * 已支付金额
     */
    @ExcelProperty(value = "已支付金额")
    private BigDecimal amountPaid;

    /**
     * 应付余额 (total_amount - amount_paid)
     */
    @ExcelProperty(value = "应付余额")
    private BigDecimal balanceDue;

    /**
     * 账单状态
     */
    @ExcelProperty(value = "账单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_bill_status")
    private String billStatus;

    /**
     * 账单月份(格式：YYYYMM)
     */
    @ExcelProperty(value = "账单月份")
    private String billMonth;

    /**
     * 账单备注
     */
    @ExcelProperty(value = "账单备注")
    private String notes;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用水性质
     */
    @ExcelProperty(value = "用水性质")
    private String useWaterNature;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用水地址")
    private String address;

    /**
     * 客户性质
     */
    @ExcelProperty(value = "客户性质")
    private String customerNature;

    /**
     * 用水人数
     */
    @ExcelProperty(value = "用水人数")
    private Long useWaterNumber;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 证件类型
     */
    @ExcelProperty(value = "证件类型")
    private String certificateType;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码")
    private String certificateNumber;

    /**
     * 用户状态
     */
    @ExcelProperty(value = "用户状态")
    private String userStatus;

    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "电子邮箱")
    private String email;

    /**
     * 供水日期
     */
    @ExcelProperty(value = "供水日期")
    private Date supplyDate;

    /**
     * 单元房号
     */
    @ExcelProperty(value = "单元房号")
    private String unitRoomNumber;

    /**
     * 小区ID
     */
    private Long communityId;

    /**
     * 小区名称
     */
    @ExcelProperty(value = "小区名称")
    private String communityName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 详情JSON
     */
    private String detailJson;

    /**
     * 水资源税
     */
    private BigDecimal waterResourceTax;

    /**
     * 污水处理费
     */
    private BigDecimal sewageTreatmentFee;

    /**
     * 仅水费，不含其他
     */
    private BigDecimal waterBillOnly;
}
