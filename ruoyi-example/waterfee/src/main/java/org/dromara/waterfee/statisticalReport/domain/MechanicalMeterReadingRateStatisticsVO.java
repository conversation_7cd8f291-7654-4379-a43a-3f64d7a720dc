package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class MechanicalMeterReadingRateStatisticsVO {
    /**
     * 抄表员姓名
     */
    @ExcelProperty(value = "抄表员")
    private String readerName;

    /**
     * 总户数
     */
    @ExcelProperty(value = "总户数")
    private Integer totalCount;

    /**
     * 销户
     */
    @ExcelProperty(value = "销户")
    private Integer cancellationCount;

    /**
     * 停户
     */
    @ExcelProperty(value = "停户")
    private Integer deactivateCount;

    /**
     * 本季度应抄
     */
    @ExcelProperty(value = "本季度应抄")
    private Integer planReadCount;

    /**
     * 本季度实抄
     */
    @ExcelProperty(value = "本季度实抄")
    private Integer readCount;

    /**
     * 抄表率（%）
     */
    @ExcelProperty(value = "抄表率（%）")
    private String readingRate;

    /**
     * 应收户
     */
    @ExcelProperty(value = "应收户")
    private Integer planReceivableCount;

    /**
     * 实收户
     */
    @ExcelProperty(value = "实收户")
    private Integer receivableCount;

    /**
     * 回收率（%）
     */
    @ExcelProperty(value = "回收率（%）")
    private String recoveryRate;

    /**
     * 应收费
     */
    @ExcelProperty(value = "应收费")
    private String planPayment;

    /**
     * 实收费
     */
    @ExcelProperty(value = "实收费")
    private String payment;

    /**
     * 水费回收率（%）
     */
    @ExcelProperty(value = "水费回收率（%）")
    private String paymentRate;

}
