package org.dromara.waterfee.repair.service;

import org.dromara.waterfee.repair.domain.WaterfeeRepairReport;
import org.dromara.waterfee.repair.domain.vo.WaterfeeRepairReportVo;
import org.dromara.waterfee.repair.domain.bo.WaterfeeRepairReportBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 报修管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IWaterfeeRepairReportService {

    /**
     * 查询报修管理
     *
     * @param repairId 主键
     * @return 报修管理
     */
    WaterfeeRepairReport queryById(Long repairId);

    /**
     * 分页查询报修管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报修管理分页列表
     */
    TableDataInfo<WaterfeeRepairReportVo> queryPageList(WaterfeeRepairReportBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的报修管理列表
     *
     * @param bo 查询条件
     * @return 报修管理列表
     */
    List<WaterfeeRepairReportVo> queryList(WaterfeeRepairReportBo bo);

    /**
     * 新增报修管理
     *
     * @param bo 报修管理
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeRepairReportBo bo);

    /**
     * 修改报修管理
     *
     * @param bo 报修管理
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeRepairReportBo bo);

    /**
     * 校验并批量删除报修管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
