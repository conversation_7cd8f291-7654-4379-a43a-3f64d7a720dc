package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 代扣配置信息对象 waterfee_charge_manage_withhold_config
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_withhold_config")
public class WaterfeeChargeManageWithholdConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private String bankAccount;

    /**
     * 
     */
    private String bankName;

    /**
     * 是否签约
     */
    private Long signed;

    /**
     * 签约时间
     */
    private Date signTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
