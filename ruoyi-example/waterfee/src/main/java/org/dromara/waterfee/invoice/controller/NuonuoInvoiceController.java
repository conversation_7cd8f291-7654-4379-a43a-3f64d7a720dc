package org.dromara.waterfee.invoice.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.invoice.domain.InvoiceCancellationParam;
import org.dromara.waterfee.invoice.domain.QueryInvoiceParam;
import org.dromara.waterfee.invoice.domain.QuickRedInvoiceParam;
import org.dromara.waterfee.invoice.domain.RequestInvoiceParam;
import org.dromara.waterfee.invoice.service.NuonuoInvoiceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 诺诺发票接口
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/nuonuoInvoice")
public class NuonuoInvoiceController extends BaseController {

    private final NuonuoInvoiceService nuonuoInvoiceService;

    /**
     * 发起开票请求
     */
//    @SaCheckPermission("invoice:invoiceRecord:list")
    @PostMapping("/requestInvoice")
    public R requestInvoice(@RequestBody RequestInvoiceParam requestInvoiceParam) {
        return nuonuoInvoiceService.webRequestInvoice(requestInvoiceParam);
    }

    @PostMapping("/quickRedInvoice")
    public R requestRedInvoice(@RequestBody QuickRedInvoiceParam quickRedInvoiceParam) {
        return nuonuoInvoiceService.quickRedInvoice(quickRedInvoiceParam);
    }

    /**
     * 查询发票详情
     */
    @PostMapping("/queryInvoiceStatus")
    public String queryInvoiceStatus(@RequestBody QueryInvoiceParam queryInvoiceParam) {
        return nuonuoInvoiceService.queryInvoiceStatus(queryInvoiceParam);
    }

    /**
     * 发票作废
     */
//    @GetMapping("/invoiceCancellation")
//    public R invoiceCancellation(@RequestParam Long invoiceId) {
//        return nuonuoInvoiceService.webInvoiceCancellation(invoiceId);
//    }

    /**
     * 手动刷新发票状态
     */
    @GetMapping("/refreshInvoiceStatus")
    public R refreshInvoiceStatus(@RequestParam String serialNo) {
        return nuonuoInvoiceService.refreshInvoiceStatusBySerialNo(serialNo);
    }

}
