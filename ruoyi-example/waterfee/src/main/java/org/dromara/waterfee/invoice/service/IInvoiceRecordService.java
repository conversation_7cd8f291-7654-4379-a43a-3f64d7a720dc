package org.dromara.waterfee.invoice.service;

import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import org.dromara.waterfee.invoice.domain.vo.InvoiceRecordVo;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 发票记录信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IInvoiceRecordService {

    /**
     * 查询发票记录信息
     *
     * @param invoiceId 主键
     * @return 发票记录信息
     */
    InvoiceRecord queryById(Long invoiceId);

    /**
     * 分页查询发票记录信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 发票记录信息分页列表
     */
    TableDataInfo<InvoiceRecordVo> queryPageList(InvoiceRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的发票记录信息列表
     *
     * @param bo 查询条件
     * @return 发票记录信息列表
     */
    List<InvoiceRecordVo> queryList(InvoiceRecordBo bo);

    /**
     * 新增发票记录信息
     *
     * @param bo 发票记录信息
     * @return 是否新增成功
     */
    Boolean insertByBo(InvoiceRecordBo bo);

    /**
     * 修改发票记录信息
     *
     * @param bo 发票记录信息
     * @return 是否修改成功
     */
    Boolean updateByBo(InvoiceRecordBo bo);

    /**
     * 校验并批量删除发票记录信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
