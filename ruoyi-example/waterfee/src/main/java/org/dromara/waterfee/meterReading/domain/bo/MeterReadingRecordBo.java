package org.dromara.waterfee.meterReading.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;

import java.util.Date;

/**
 * 抄表记录业务对象
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@AutoMapper(target = WaterfeeMeterReadingRecord.class, reverseConvertGenerate = false)
public class MeterReadingRecordBo {

    /**
     * 抄表记录ID
     */
    @NotNull(message = "抄表记录ID不能为空", groups = {EditGroup.class})
    private Long recordId;

    /**
     * 水表编号
     */
    @NotBlank(message = "水表编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String meterNo;

    /**
     * 水表类型（字典waterfee_meter_type）
     */
    private String meterType;

    /**
     * 上期抄表读数
     */
    private Double lastReading;

    /**
     * 上期抄表时间
     */
    private Date lastReadingTime;

    /**
     * 本期抄表读数
     */
    @NotNull(message = "本期抄表读数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double currentReading;

    /**
     * 旧表止数
     */
    private Double oldMeterStopReading;

    /**
     * 本期水量
     */
    private Double waterUsage;

    /**
     * 本期抄表时间
     */
    private Date readingTime;

    /**
     * 抄表手册ID
     */
    private Long meterBookId;

    /**
     * 抄表任务ID
     */
    private Long taskId;

    /**
     * 来源（normal-正常抄表 manual-人工补录）
     */
    private String sourceType;

    /**
     * 是否审核（0-未审核 1-已审核）
     */
    private String isAudited;

    /**
     * 补录ID（若是补录数据则关联）
     */
    private Long manualId;

    /**
     * 备注
     */
    private String remark;
}
