package org.dromara.waterfee.meter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 水表信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface IWaterfeeMeterService extends IService<WaterfeeMeter> {

    /**
     * 查询水表信息
     *
     * @param meterId 主键
     * @return 水表信息
     */
    WaterfeeMeterVo queryById(Long meterId);

    /**
     * 查询水表信息列表
     *
     * @param bo 水表信息
     * @return 水表信息集合
     */
    TableDataInfo<WaterfeeMeterVo> queryPageList(WaterfeeMeterBo bo, PageQuery pageQuery);

    /**
     * 查询水表信息列表
     *
     * @param bo 水表信息
     * @return 水表信息集合
     */
    List<WaterfeeMeterVo> queryList(WaterfeeMeterBo bo);

    /**
     * 新增水表信息
     *
     * @param bo 水表信息
     * @return 结果
     */
    Boolean insertByBo(WaterfeeMeterBo bo);

    /**
     * 修改水表信息
     *
     * @param bo 水表信息
     * @return 结果
     */
    Boolean updateByBo(WaterfeeMeterBo bo);

    /**
     * 校验并批量删除水表信息
     *
     * @param ids     需要删除的水表信息主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 关联用水户
     *
     * @param meterId 水表ID
     * @param userId  用水户ID
     * @return 结果
     */
    Boolean associateUser(Long meterId, Long userId);

    /**
     * 关联抄表手册
     *
     * @param meterId     水表ID
     * @param meterBookId 抄表手册ID
     * @return 结果
     */
    Boolean associateMeterBook(Long meterId, Long meterBookId);

    /**
     * 根据水表编号查询
     *
     * @param meterNo 编号
     * @return 水表信息集合
     */
    WaterfeeMeterVo queryByNo(@NotNull(message = "编号不能为空") String meterNo);


    @Transactional(rollbackFor = Exception.class)
    Map<String, Object> importMeter(List<?> importList, Integer meterType);

    /**
     * 根据表册ID获取水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    List<String> getMeterNosByBookId(Long meterBookId);

    /**
     * 根据表册ID获取机械水表编号列表
     *
     * @param meterBookId 表册ID
     * @return 水表编号列表
     */
    List<String> getMechMeterNosByBookId(Long meterBookId);

    /**
     * 根据用户编号查询水表信息
     *
     * @param userNo 用户编号
     * @return 水表信息集合
     */
    List<WaterfeeMeterVo> queryByUserNo(@NotNull(message = "用户编号不能为空") String userNo);

    /**
     * 根据多个水表ID查询水表信息
     *
     * @param meterIds 水表ID集合
     * @return 水表信息集合
     */
    List<WaterfeeMeterVo> queryByIds(Collection<Long> meterIds);

    /**
     * 根据多个水表编号查询水表信息
     *
     * @param meterNos 水表编号集合
     * @return 水表信息集合
     */
    List<WaterfeeMeterVo> queryByNos(Collection<String> meterNos);
}
