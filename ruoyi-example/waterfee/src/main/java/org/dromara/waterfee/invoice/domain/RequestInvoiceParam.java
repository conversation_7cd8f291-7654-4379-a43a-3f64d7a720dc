package org.dromara.waterfee.invoice.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 开票参数
 */

@Data
public class RequestInvoiceParam {

    /**
     * 购方名称 必填
     */
    @NotBlank(message = "购方名称不能为空")
    private String buyerName;

    /**
     * 购方税号 企业必填 个人可为空
     */
    private String buyerTaxNumber;

    /**
     * 是否企业 必填
     */
    @NotNull(message = "是否企业不能为空")
    private Boolean isEnterprise;

    /** 关联账单编号 必填 */
    private String billNumber;

    /** 必填 推送方式：-1,不推送;0,邮箱;1,手机（默认
     ）;2,邮箱、手机 */
    @NotNull(message = "推送方式不能为空")
    private String pushMode;

    /** 购方手机 用于推送 选择时必填 */
    private String buyerPhone;

    /** 购方邮箱 用于推送 选择时必填 */
    private String email;

    /** 发票类型 必填 1蓝票 2红票 */
    @NotBlank(message = "开票类型不能为空")
    private String invoiceType;

//    /** 发票代码 红票必填 这里前端不填写 后端自动补充 */
//    private String invoiceCode;
//
//    /** 发票号码 红票必填 这里前端不填写 后端自动补充 */
//    private String invoiceNum;

    /** 开票员 */
    private String clerk;

    /** 发票ID 红票必填*/
    private Long invoiceId;

    /** 发票种类标识 0普通发票（电票），1数电专票(电子) */
    private String invoiceLine;
}
