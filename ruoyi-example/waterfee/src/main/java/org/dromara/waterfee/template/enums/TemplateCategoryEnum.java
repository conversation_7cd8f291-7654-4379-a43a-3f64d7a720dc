package org.dromara.waterfee.template.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板分类枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum TemplateCategoryEnum {

    /**
     * 账单模板
     */
    BILL("BILL", "账单模板"),

    /**
     * 报表模板
     */
    REPORT("REPORT", "报表模板"),

    /**
     * 通知模板
     */
    NOTICE("NOTICE", "通知模板"),

    /**
     * 合同模板
     */
    CONTRACT("CONTRACT", "合同模板"),

    /**
     * 证书模板
     */
    CERTIFICATE("CERTIFICATE", "证书模板");

    /**
     * 分类码
     */
    private final String code;

    /**
     * 分类名称
     */
    private final String name;

    /**
     * 根据分类码获取分类名称
     *
     * @param code 分类码
     * @return 分类名称
     */
    public static String getNameByCode(String code) {
        for (TemplateCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category.getName();
            }
        }
        return code;
    }

    /**
     * 根据分类码获取枚举
     *
     * @param code 分类码
     * @return 枚举
     */
    public static TemplateCategoryEnum getByCode(String code) {
        for (TemplateCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 获取所有分类
     *
     * @return 分类列表
     */
    public static java.util.List<java.util.Map<String, String>> getAllCategories() {
        java.util.List<java.util.Map<String, String>> categories = new java.util.ArrayList<>();
        for (TemplateCategoryEnum category : values()) {
            java.util.Map<String, String> categoryMap = new java.util.HashMap<>();
            categoryMap.put("code", category.getCode());
            categoryMap.put("name", category.getName());
            categories.add(categoryMap);
        }
        return categories;
    }
}
