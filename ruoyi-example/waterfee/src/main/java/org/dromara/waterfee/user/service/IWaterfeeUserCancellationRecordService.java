package org.dromara.waterfee.user.service;

import org.dromara.waterfee.user.domain.WaterfeeUserCancellationRecord;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserCancellationRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserCancellationRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用水用户销户记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface IWaterfeeUserCancellationRecordService {

    /**
     * 查询用水用户销户记录
     *
     * @param cancellationId 主键
     * @return 用水用户销户记录
     */
    WaterfeeUserCancellationRecord queryById(Long cancellationId);

    /**
     * 分页查询用水用户销户记录列表
     *
     * @param recordQo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户销户记录分页列表
     */
    TableDataInfo<WaterfeeUserCancellationRecordVo> queryPageList(RecordQo recordQo, PageQuery pageQuery);

    /**
     * 查询符合条件的用水用户销户记录列表
     *
     * @param bo 查询条件
     * @return 用水用户销户记录列表
     */
    List<WaterfeeUserCancellationRecordVo> queryList(WaterfeeUserCancellationRecordBo bo);

    /**
     * 新增用水用户销户记录
     *
     * @param bo 用水用户销户记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeUserCancellationRecordBo bo);

    /**
     * 修改用水用户销户记录
     *
     * @param bo 用水用户销户记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeUserCancellationRecordBo bo);

    /**
     * 校验并批量删除用水用户销户记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
