package org.dromara.waterfee.app.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * App用户账单消费查询视图对象
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
public class AppUserBillConsumptionVo implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 账单ID
   */
  private Long billId;

  /**
   * 用水量
   */
  private BigDecimal consumptionVolume;

  /**
   * 本期读数
   */
  private BigDecimal currentReadingValue;

  /**
   * 水费
   */
  private BigDecimal totalAmount;

  /**
   * 账单月份
   */
  private String billMonth;

  /**
   * 阶梯用水量和费用
   * key为阶梯级数(1,2,3,4...)，value为包含用水量和费用的Map
   */
  private Map<Integer, TierInfo> tierInfoMap;

  /**
   * 阶梯信息内部类
   */
  @Data
  public static class TierInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 阶梯用水量
     */
    private BigDecimal usage;

    /**
     * 阶梯费用
     */
    private BigDecimal amount;
  }
}