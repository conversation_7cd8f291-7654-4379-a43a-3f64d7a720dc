package org.dromara.waterfee.meter.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meter.domain.WaterfeeMeterAlert;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterAlertVo;

import java.util.List;

/**
 * 设备告警Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
public interface WaterfeeMeterAlertMapper extends BaseMapperPlus<WaterfeeMeterAlert, WaterfeeMeterAlertVo> {

    /**
     * 根据ID查询设备告警（直接SQL查询，避免字段不匹配问题）
     *
     * @param alertId 告警ID
     * @return 设备告警对象
     */
    WaterfeeMeterAlert selectAlertById(@Param("alertId") Long alertId);

    /**
     * 根据ID查询设备告警
     *
     * @param alertId 告警ID
     * @return 设备告警视图对象
     */
    WaterfeeMeterAlertVo selectAlertVoById(@Param("alertId") Long alertId);

    /**
     * 查询设备告警列表
     *
     * @param queryWrapper 查询条件
     * @return 设备告警视图对象列表
     */
    List<WaterfeeMeterAlertVo> selectAlertVoList(@Param(Constants.WRAPPER) Wrapper<WaterfeeMeterAlert> queryWrapper);

    /**
     * 分页查询设备告警列表
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @return 设备告警视图对象分页列表
     */
    Page<WaterfeeMeterAlertVo> selectAlertVoPage(Page<WaterfeeMeterAlert> page, @Param(Constants.WRAPPER) Wrapper<WaterfeeMeterAlert> queryWrapper);
}
