package org.dromara.waterfee.bill.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.bill.domain.bo.PaymentDetailBo;
import org.dromara.waterfee.bill.domain.vo.PaymentDetailVo;

import java.util.Collection;
import java.util.List;

/**
 * 缴费明细Service接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IPaymentDetailService {

    /**
     * 查询缴费明细
     */
    PaymentDetailVo queryById(String paymentDetailId);

    /**
     * 查询缴费明细列表
     */
    TableDataInfo<PaymentDetailVo> queryPageList(PaymentDetailBo bo, PageQuery pageQuery);

    /**
     * 查询缴费明细列表
     */
    List<PaymentDetailVo> queryList(PaymentDetailBo bo);

    /**
     * 新增缴费明细
     */
    Boolean insertByBo(PaymentDetailBo bo);

    /**
     * 修改缴费明细
     */
    Boolean updateByBo(PaymentDetailBo bo);

    /**
     * 校验并批量删除缴费明细信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 根据微信openid查询缴费明细列表（按收入时间倒序）
     *
     * @param openid 微信openid
     * @param pageQuery 分页查询参数
     * @return 缴费明细列表
     */
    TableDataInfo<PaymentDetailVo> queryByOpenidOrderByIncomeTime(String openid, PageQuery pageQuery);
}
