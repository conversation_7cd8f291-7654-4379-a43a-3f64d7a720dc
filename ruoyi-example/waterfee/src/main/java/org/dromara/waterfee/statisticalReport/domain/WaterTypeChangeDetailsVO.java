package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class WaterTypeChangeDetailsVO {
    /**
     * 更改时间（创建时间）
     */
    @ExcelProperty(value = "更改时间")
    private String createTime;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "电话")
    private String phoneNumber;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "身份证号（信用代码）")
    private String certificateNumber;

    /**
     * 原客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "原用户类型")
    private String beforeCustomerNature;

    /**
     * 原用水类型
     */
    @ExcelProperty(value = "原用水类型")
    private String beforeUseWaterNature;

    /**
     * 现客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "现用户类型")
    private String afterCustomerNature;

    /**
     * 现用水类型
     */
    @ExcelProperty(value = "现用水类型")
    private String afterUseWaterNature;
}
