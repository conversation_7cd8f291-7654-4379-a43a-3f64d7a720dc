package org.dromara.waterfee.invoice.mapper;

import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import org.dromara.waterfee.invoice.domain.vo.InvoiceRecordVo;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 发票记录信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface InvoiceRecordMapper extends BaseMapperPlus<InvoiceRecord, InvoiceRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<InvoiceRecordVo>> P selectVoPage(IPage<InvoiceRecord> page, Wrapper<InvoiceRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询发票记录信息列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<InvoiceRecordVo> queryList(@Param("page") Page<InvoiceRecord> page, @Param("query") InvoiceRecordBo query);

}
