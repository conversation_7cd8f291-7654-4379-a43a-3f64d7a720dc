package org.dromara.waterfee.meter.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 水表信息视图对象 waterfee_meter
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeMeter.class)
public class WaterfeeMeterVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 水表ID
     */
    @ExcelProperty(value = "水表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long meterId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 水表类型(1-机械表 2-智能表)
     */
    @ExcelProperty(value = "水表类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_type")
    private Integer meterType;

    /**
     * 生产厂家
     */
    @ExcelProperty(value = "生产厂家", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "meter_factory")
    private String manufacturer;

    /**
     * 水表类别(自来水表、中水表、直饮水表)
     */
    @ExcelProperty(value = "水表类别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_category")
    private String meterCategory;

    /**
     * 水表分类(总表、分表)
     */
    @ExcelProperty(value = "水表分类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_classification")
    private String meterClassification;

    /**
     * 计量用途(居民用水、非居民用水)
     */
    @ExcelProperty(value = "计量用途", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_measurement_purpose")
    private String measurementPurpose;

    /**
     * 口径
     */
    @ExcelProperty(value = "口径", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "dnmm")
    private String caliber;

    /**
     * 精度
     */
    @ExcelProperty(value = "精度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "water_meter_accuracy")
    private String accuracy;

    /**
     * 初始读数
     */
    @ExcelProperty(value = "初始读数")
    private BigDecimal initialReading;

    /**
     * 安装日期
     */
    @ExcelProperty(value = "安装日期")
    private Date installDate;

    /**
     * 业务区域ID
     */
    @ExcelProperty(value = "业务区域ID")
    private Long businessAreaId;

    /**
     * 业务区域名称
     */
    @ExcelProperty(value = "业务区域名称")
    private String businessAreaName;

    /**
     * 抄表手册ID
     */
    @ExcelProperty(value = "抄表手册ID")
    private Long meterBookId;

    /**
     * 抄表手册名称
     */
    @ExcelProperty(value = "抄表手册名称")
    private String meterBookName;

    /**
     * 安装地址
     */
    @ExcelProperty(value = "安装地址")
    private String installAddress;

    /**
     * 表倍率
     */
    @ExcelProperty(value = "表倍率")
    private BigDecimal meterRatio;

    /**
     * 通讯方式(NB-IoT、4G、Cat.1、LoRa)
     */
    @ExcelProperty(value = "通讯方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_communication_mode")
    private String communicationMode;

    /**
     * 阀控功能(0-无 1-有)
     */
    @ExcelProperty(value = "阀控功能", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=无,1=有")
    private Integer valveControl;

    /**
     * IMEI号
     */
    @ExcelProperty(value = "IMEI号")
    private String imei;

    /**
     * IMSI号
     */
    @ExcelProperty(value = "IMSI号")
    private String imsi;

    /**
     * 物联网平台
     */
    @ExcelProperty(value = "物联网平台", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_iot_platform")
    private String iotPlatform;

    /**
     * 是否预付费(0-否 1-是)
     */
    @ExcelProperty(value = "是否预付费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=否,1=是")
    private Integer prepaid;

    /**
     * 关联用户ID
     */
    @ExcelProperty(value = "关联用户ID")
    private Long userId;

    /**
     * 关联用户编号
     */
    @ExcelProperty(value = "关联用户编号")
    private String userNo;

    /**
     * 关联用户名称
     */
    @ExcelProperty(value = "关联用户名称")
    private String userName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 当前读数
     */
    @ExcelProperty(value = "当前读数")
    private BigDecimal currentReading;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createBy;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注信息
     */
    private String remark;
}
