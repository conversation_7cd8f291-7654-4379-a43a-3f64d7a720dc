package org.dromara.waterfee.wechat.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.waterfee.wechat.domain.entity.WechatDraft;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信草稿视图对象 wechat_draft
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@AutoMapper(target = WechatDraft.class)
public class WechatDraftVo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 草稿ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long draftId;

    /**
     * 微信草稿媒体ID
     */
    private String mediaId;

    /**
     * 草稿标题
     */
    private String title;

    /**
     * 草稿类型（WATER_OUTAGE-停水通知 WATER_SUPPLY-供水通知）
     */
    private String draftType;

    /**
     * 草稿类型名称
     */
    private String draftTypeName;

    /**
     * 草稿内容
     */
    private String content;

    /**
     * 封面图片媒体ID
     */
    private String thumbMediaId;

    /**
     * 封面图片URL
     */
    private String thumbUrl;

    /**
     * 作者
     */
    private String author;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 是否显示封面（0-不显示 1-显示）
     */
//    private Boolean showCoverPic;

    /**
     * 原文链接
     */
    private String contentSourceUrl;

    /**
     * 是否开启评论（0-不开启 1-开启）
     */
    private Boolean needOpenComment;

    /**
     * 是否粉丝才可评论（0-所有人 1-仅粉丝）
     */
    private Boolean onlyFansCanComment;

    /**
     * 草稿状态（DRAFT-草稿 PUBLISHED-已发布）
     */
    private String draftStatus;

    /**
     * 草稿状态名称
     */
    private String draftStatusName;

    /**
     * 文章状态（UNPUBLISHED-未发布文章 PUBLISHED-已发布文章）
     */
    private String articleStatus;

    /**
     * 文章状态名称
     */
    private String articleStatusName;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 文章发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date articlePublishTime;

    /**
     * 备注
     */
    private String remark;
}
