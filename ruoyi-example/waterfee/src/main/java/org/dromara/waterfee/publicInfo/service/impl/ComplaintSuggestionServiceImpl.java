package org.dromara.waterfee.complaint.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.complaint.domain.bo.ComplaintSuggestionBo;
import org.dromara.waterfee.complaint.domain.vo.ComplaintSuggestionVo;
import org.dromara.waterfee.complaint.domain.ComplaintSuggestion;
import org.dromara.waterfee.complaint.mapper.ComplaintSuggestionMapper;
import org.dromara.waterfee.complaint.service.IComplaintSuggestionService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 投诉建议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RequiredArgsConstructor
@Service
public class ComplaintSuggestionServiceImpl implements IComplaintSuggestionService {

    private final ComplaintSuggestionMapper baseMapper;

    /**
     * 查询投诉建议
     *
     * @param complaintSuggestionId 主键
     * @return 投诉建议
     */
    @Override
    public ComplaintSuggestion queryById(Long complaintSuggestionId){
        return baseMapper.selectById(complaintSuggestionId);
    }

    /**
     * 分页查询投诉建议列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 投诉建议分页列表
     */
    @Override
    public TableDataInfo<ComplaintSuggestionVo> queryPageList(ComplaintSuggestionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ComplaintSuggestion> lqw = buildQueryWrapper(bo);
        Page<ComplaintSuggestionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的投诉建议列表
     *
     * @param bo 查询条件
     * @return 投诉建议列表
     */
    @Override
    public List<ComplaintSuggestionVo> queryList(ComplaintSuggestionBo bo) {
        LambdaQueryWrapper<ComplaintSuggestion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ComplaintSuggestion> buildQueryWrapper(ComplaintSuggestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ComplaintSuggestion> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ComplaintSuggestion::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getSubmitterName()), ComplaintSuggestion::getSubmitterName, bo.getSubmitterName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), ComplaintSuggestion::getContactPhone, bo.getContactPhone());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), ComplaintSuggestion::getContent, bo.getContent());
        lqw.eq(bo.getSubmitTime() != null, ComplaintSuggestion::getSubmitTime, bo.getSubmitTime());
        return lqw;
    }

    /**
     * 新增投诉建议
     *
     * @param bo 投诉建议
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ComplaintSuggestionBo bo) {
        ComplaintSuggestion add = MapstructUtils.convert(bo, ComplaintSuggestion.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setComplaintSuggestionId(add.getComplaintSuggestionId());
        }
        return flag;
    }

    /**
     * 修改投诉建议
     *
     * @param bo 投诉建议
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ComplaintSuggestionBo bo) {
        ComplaintSuggestion update = MapstructUtils.convert(bo, ComplaintSuggestion.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ComplaintSuggestion entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除投诉建议信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
