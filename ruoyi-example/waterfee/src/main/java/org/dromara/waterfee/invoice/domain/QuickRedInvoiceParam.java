package org.dromara.waterfee.invoice.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 快速冲红
 */

@Data
public class QuickRedInvoiceParam {

    /** 必填 推送方式：-1,不推送;0,邮箱;1,手机（默认
     ）;2,邮箱、手机 */
    @NotNull(message = "推送方式不能为空")
    private String pushMode;

    /** 购方手机 用于推送 选择时必填 */
    private String buyerPhone;

    /** 购方邮箱 用于推送 选择时必填 */
    private String email;

    /** 发票ID 红票必填*/
    @NotNull(message = "发票ID不能为空")
    private Long invoiceId;

}
