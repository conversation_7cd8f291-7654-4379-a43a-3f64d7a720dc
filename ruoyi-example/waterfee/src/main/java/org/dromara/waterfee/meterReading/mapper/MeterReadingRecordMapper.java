package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;

import java.util.List;

/**
 * 抄表记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface MeterReadingRecordMapper extends BaseMapperPlus<WaterfeeMeterReadingRecord, MeterReadingRecordVo> {

    /**
     * 根据水表编号查询最新一次的抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    MeterReadingRecordVo selectLatestByMeterNo(@Param("meterNo") String meterNo);

    /**
     * 根据水表编号查询最新一次的抄表记录（实体）
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    WaterfeeMeterReadingRecord selectLatestEntityByMeterNo(@Param("meterNo") String meterNo);

    /**
     * 根据水表编号查询所有抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录列表
     */
    List<MeterReadingRecordVo> selectAllByMeterNo(@Param("meterNo") String meterNo);

    /**
     * 根据表册ID和任务ID查询抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     * @return 抄表记录列表
     */
    List<WaterfeeMeterReadingRecord> selectByBookIdAndTaskId(@Param("meterBookId") Long meterBookId, @Param("taskId") Long taskId);

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 机械表抄表记录列表
     */
    List<MeterReadingRecordVo> selectMechanicalMetersByBookId(@Param("meterBookId") Long meterBookId);

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 智能表抄表记录列表
     */
    List<MeterReadingRecordVo> selectIntelligentMetersByBookId(@Param("meterBookId") Long meterBookId);
}
