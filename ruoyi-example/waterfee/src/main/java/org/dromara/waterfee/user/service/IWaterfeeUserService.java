package org.dromara.waterfee.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.*;
import org.dromara.waterfee.user.domain.dto.WaterfeeMeterAddDto;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserWorkFlowVo;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 用水用户管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface IWaterfeeUserService extends IService<WaterfeeUser> {

    /**
     * 查询用水用户管理
     *
     * @param userId 主键
     * @return 用水用户管理
     */
    WaterfeeUser queryById(Long userId);

    /**
     * 分页查询用水用户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户管理分页列表
     */
    TableDataInfo<WaterfeeUserVo> queryPageList(WaterfeeUserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用水用户管理列表
     *
     * @param bo 查询条件
     * @return 用水用户管理列表
     */
    List<WaterfeeUserVo> queryList(WaterfeeUserBo bo);

    /**
     * 根据用户编号查询用户信息
     *
     * @param userNo 用户编号
     * @return 用户信息
     */
    WaterfeeUser queryByUserNo(String userNo);

    /**
     * 根据用户名称查询用户信息
     *
     * @param userName 用户名称
     * @return 用户信息
     */
    WaterfeeUser queryByUserName(String userName);

    /**
     * 新增用水用户管理
     *
     * @param bo 用水用户管理
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeUserBo bo);

    /**
     * 修改用水用户管理
     *
     * @param bo 用水用户管理
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeUserBo bo);

    /**
     * 校验并批量删除用水用户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 审核用水用户
     *
     * @param userId 用户ID
     * @return 是否审核成功
     */
    Boolean auditUser(Long userId);

    /**
     * 执行用户过户操作
     *
     * @param bo 过户记录业务对象
     * @return 是否成功
     */
    Boolean transferOwnership(WaterfeeUserTransferOwnershipRecordBo bo);

    /**
     * 更新用户价格信息
     *
     * @param bo 价格变更记录业务对象
     * @return 是否成功
     */
    Boolean updateUserPrice(WaterfeeUserPriceChangeRecordBo bo);

    /**
     * 启用用水用户
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean activateUser(Long userId);

    /**
     * 报停用水用户
     *
     * @param bo 报停记录
     * @return 是否成功
     */
    Boolean deactivateUser(WaterfeeUserDeactivateRecordBo bo);

    /**
     * 销户用水用户
     *
     * @param bo 销户记录
     * @return 是否成功
     */
    Boolean cancellationUser(WaterfeeUserCancellationRecordBo bo);

    /**
     * 更新用户基础信息
     *
     * @param bo 基础信息变更记录业务对象
     * @return 是否成功
     */
    Boolean updateUserBasicInfo(WaterfeeUserBasicInfoChangeRecordBo bo);

    /**
     * 批量启用用水用户
     *
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    Boolean batchActivateUsers(List<Long> userIds);

    /**
     * 批量报停用水用户
     *
     * @param bo 批量报停业务对象
     * @return 操作结果
     */
    Boolean batchDeactivateUsers(WaterfeeUserBatchDeactivateBo bo);

    /**
     * 批量销户用水用户
     *
     * @param bo 批量销户业务对象
     * @return 操作结果
     */
    Boolean batchCancellationUsers(WaterfeeUserBatchCancellationBo bo);

    /**
     * 新增用水用户基本信息
     *
     * @param bo 用水用户基本信息业务对象
     * @return 结果
     */
    WaterfeeUser insertUserBasicInfo(WaterfeeUserBasicInfoBo bo);

    /**
     * 编辑用水用户基本信息草稿
     *
     * @param bo 用水用户基本信息业务对象
     * @return 结果
     */
    WaterfeeUser updateUserDraftBasicInfo(WaterfeeUserBasicInfoBo bo);

    /**
     * 为用水用户绑定水表
     *
     * @param dto 水表信息DTO
     * @return 结果
     */
//    Boolean bindUserMeter(WaterfeeMeterAddDto dto);

    /**
     * 修改用水用户价格信息
     *
     * @param bo 用水用户价格信息业务对象
     * @return 结果
     */
    Boolean updateUserPriceInfo(WaterfeeUserPriceBo bo);

    /**
     * 工作流获取用水用户详细信息
     *
     * @param userId 主键
     * @return 用水用户信息
     */
    WaterfeeUserWorkFlowVo workFlowGetDetail(Long userId);

    /**
     * 根据水表编号查询用户信息 未删除的数据
     *
     * @param meterNo
     * @return
     */
//    WaterfeeUser selectUserByMeterNo(String meterNo);

    /**
     * 根据证件类型、证件号和水表编号查询用户 未删除的数据
     *
     * @param
     * @return 用户对象信息
     */
//    WaterfeeUser selectUserByCertificateTypeAndCertificateNumberAndMeterNo(String certificateType, String certificateNumber, String meterNo);

    /**
     * 导入用水用户数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     * @throws Exception 异常
     */
    String importUser(MultipartFile file, Boolean updateSupport) throws Exception;

    /**
     * 生成用户编号
     *
     * @return 用户编号
     */
//    String generateUserNo(Long areaId, Long communityId);

    /**
     * 批量设定为重点户
     *
     * @param userIds 用户ID集合
     * @return 结果
     */
    boolean batchSetSpecificUsers(List<Long> userIds);

    /**
     * 批量取消重点户
     *
     * @param userIds 用户ID集合
     * @return 结果
     */
    boolean batchCancelSpecificUsers(List<Long> userIds);

    /**
     * 返回用户余额
     */
    BigDecimal getUserBalance(Long userId);
}
