package org.dromara.waterfee.community.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.community.domain.bo.CommunityBo;
import org.dromara.waterfee.community.domain.vo.CommunityVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 小区信息服务接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ICommunityService {

    /**
     * 查询小区信息
     *
     * @param id 主键
     * @return 小区信息
     */
    CommunityVo queryById(Long id);

    /**
     * 分页查询小区信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 小区信息分页列表
     */
    TableDataInfo<CommunityVo> queryPageList(CommunityBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的小区信息列表
     *
     * @param bo 查询条件
     * @return 小区信息列表
     */
    List<CommunityVo> queryList(CommunityBo bo);

    /**
     * 新增小区信息
     *
     * @param bo 小区信息
     * @return 是否新增成功
     */
    Boolean insertByBo(CommunityBo bo);

    /**
     * 修改小区信息
     *
     * @param bo 小区信息
     * @return 是否修改成功
     */
    Boolean updateByBo(CommunityBo bo);

    /**
     * 批量删除小区信息
     *
     * @param ids     需要删除的小区信息ID集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 获取社区下拉选择框数据
     *
     * @return 社区ID和名称的Map集合，key为id，value为communityName
     */
    Map<String, String> getSelectCommunityMap();
}
