package org.dromara.waterfee.income.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.income.domain.WaterfeeIncomeRecord;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收入记录业务对象 waterfee_income_record
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeIncomeRecord.class, reverseConvertGenerate = false)
public class WaterfeeIncomeRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long incomeId;

    /**
     * 收入类型（bill_payment：账单缴费，prestore：预存充值，refund：账单退费）
     */
    @NotBlank(message = "收入类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String incomeType;

    /**
     * 用户编号
     */
    @NotBlank(message = "用户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userNo;

    /**
     * 收费员姓名
     */
    private String collectorName;

    /**
     * 收入时间
     */
    @NotNull(message = "收入时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date incomeTime;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = {AddGroup.class, EditGroup.class})
    @DecimalMin(value = "0.01", message = "金额必须大于0", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal amount;

    /**
     * 支付方式（wechat：微信，alipay：支付宝，cash：现金，other：其他）
     */
    @NotBlank(message = "支付方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String payMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;

    /**
     * 结束时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;
}
