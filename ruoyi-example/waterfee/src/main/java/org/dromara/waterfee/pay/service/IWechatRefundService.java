package org.dromara.waterfee.pay.service;

import com.wechat.pay.java.service.refund.model.Refund;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.waterfee.pay.domain.WechatQueryRefundParam;
import org.dromara.waterfee.pay.domain.WechatRefundParam;

public interface IWechatRefundService {


    /** 申请退款 */
    Refund applyForRefund(WechatRefundParam wechatRefundParam);

    /** 查询退款 */
    Refund queryByOutRefundNo(WechatQueryRefundParam wechatQueryRefundParam);

    String wechatRefundCallBack(HttpServletRequest request, HttpServletResponse response);

}
