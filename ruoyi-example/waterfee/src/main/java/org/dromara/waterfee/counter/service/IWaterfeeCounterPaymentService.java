package org.dromara.waterfee.counter.service;

import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.counter.domain.bo.*;
import org.dromara.waterfee.counter.domain.vo.WaterfeeUserInfoVo;
import org.dromara.waterfee.user.domain.WaterfeeUser;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 窗口收费服务接口
 *
 * <AUTHOR> Li
 * @date 2025-05-07
 */
public interface IWaterfeeCounterPaymentService {

    /**
     * 根据关键字查询用户信息
     *
     * @param keyword 用户编号或姓名
     * @return 用户信息
     */
    WaterfeeUserInfoVo getUserByKeyword(String keyword);

    /**
     * 根据用户ID查询详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    WaterfeeUser getUserById(Long userId);

    /**
     * 根据关键字模糊查询用户列表
     *
     * @param keyword 关键字
     * @return 用户列表
     */
    List<WaterfeeUser> searchUsersByKeyword(String keyword);

    /**
     * 获取用户账单列表
     *
     * @return 账单列表
     */
    List<WaterfeeBillVo> getUserBills(Long userId, String status, String month);

    /**
     * 获取用户缴费明细
     *
     * @param userId 用户编号
     * @return 缴费明细列表
     */
    List<PaymentDetail> getUserPaymentDetails(Long userId);

    /**
     * 根据用户ID和账单月份获取缴费明细
     *
     * @param userId    用户ID
     * @param billMonth 账单月份
     * @return 缴费明细列表
     */
    List<PaymentDetail> getUserPaymentDetailsByMonth(Long userId, String billMonth);

//    /**
//     * 获取用户抄表记录
//     *
//     * @param userNo 用户编号
//     * @return 抄表记录列表
//     */
//    List<WaterfeeMeterReadingVo> getUserMeterReadings(String userNo);

    /**
     * 缴费
     *
     * @param bo 缴费数据
     * @return 结果
     */
    Boolean payBills(WaterfeePayBillsBo bo);

    /**
     * 预存充值
     *
     * @param bo 充值数据
     * @return 结果
     */
    Boolean addDeposit(WaterfeeDepositBo bo);

    /**
     * 调整用量
     *
     * @param bo 调整数据
     * @return 结果
     */
    Boolean adjustConsumption(WaterfeeAdjustConsumptionBo bo);

    /**
     * 账单退费
     *
     * @param bo 退费数据
     * @return 结果
     */
    Boolean refundBill(WaterfeeRefundBo bo);

    /**
     * 计算账单调整结果（不保存数据）
     *
     * @param bo 调整数据
     * @return 计算结果
     */
    Map<String, Object> calculateAdjustment(WaterfeeAdjustConsumptionBo bo);

    /**
     * 智能表自动扣款（只记录余额变更，不创建交易记录）
     *
     * @param userId  用户ID
     * @param billIds 账单ID列表
     * @param amount  扣款金额
     * @param remark  备注
     * @return 结果
     */
    Boolean autoPayBillByDeposit(Long userId, List<Long> billIds, BigDecimal amount, String remark);

    /**
     * 实收账冲正
     *
     * @param bo 冲正数据
     * @return 结果
     */
    Boolean reversalPayment(WaterfeeReversalBo bo);

    /**
     * 批量实收账冲正
     *
     * @param paymentDetailIds 收费明细ID列表
     * @param reversalReason   冲正原因
     * @return 结果
     */
    Boolean batchReversalPayment(List<String> paymentDetailIds, String reversalReason);

    /**
     * 预存款退款
     *
     * @param bo 退款数据
     * @return 结果
     */
    Boolean refundDeposit(WaterfeeDepositRefundBo bo);

    /**
     * 微信预存款支付账单
     *
     * @return 结果
     */
    Boolean wechatPayBillByDeposit(WechatPayBillBo bo);
}
