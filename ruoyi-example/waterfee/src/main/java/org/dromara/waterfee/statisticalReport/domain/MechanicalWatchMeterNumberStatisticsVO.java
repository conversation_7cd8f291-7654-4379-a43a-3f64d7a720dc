package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class MechanicalWatchMeterNumberStatisticsVO {
    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "电话")
    private String phoneNumber;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "身份证号（信用代码）")
    private String certificateNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "用户性质")
    private String customerNature;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    @ExcelProperty(value = "用户状态")
    private String userStatus;

    /**
     * 抄表员
     */
    @ExcelProperty(value = "抄表员")
    private String readerName;
}
