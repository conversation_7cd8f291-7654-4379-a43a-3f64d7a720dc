package org.dromara.waterfee.meterReading.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;

import java.util.List;

/**
 * 抄表任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface WaterfeeReadingTaskMapper extends BaseMapperPlus<WaterfeeReadingTask, WaterfeeReadingTaskVo> {

    /**
     * 根据ID查询抄表任务
     *
     * @param taskId 任务ID
     * @return 抄表任务视图对象
     */
    WaterfeeReadingTaskVo selectTaskVoById(@Param("taskId") Long taskId);

    /**
     * 查询抄表任务列表
     *
     * @param queryWrapper 查询条件
     * @return 抄表任务视图对象列表
     */
    List<WaterfeeReadingTaskVo> selectTaskVoList(@Param(Constants.WRAPPER) Wrapper<WaterfeeReadingTask> queryWrapper);

    /**
     * 分页查询抄表任务列表
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @return 抄表任务视图对象分页列表
     */
    Page<WaterfeeReadingTaskVo> selectTaskVoPage(Page<WaterfeeReadingTask> page, @Param(Constants.WRAPPER) Wrapper<WaterfeeReadingTask> queryWrapper);

    /**
     * 根据ID查询抄表任务（直接SQL查询，避免字段不匹配问题）
     *
     * @param taskId 任务ID
     * @return 抄表任务对象
     */
    WaterfeeReadingTask selectTaskById(@Param("taskId") Long taskId);

    /**
     * 根据任务状态和循环标志查询抄表任务
     *
     * @param taskStatus 任务状态
     * @param isCycle    是否循环
     * @return 抄表任务列表
     */
    @Select("SELECT task_id, task_name, business_area_id, meter_book_id, reader_id, reader_name, reading_method, reading_cycle, reading_day, base_day, is_cycle, task_status, start_date, end_date, last_execute_time, next_execute_time, book_user_num, plan_reading_num, remark, tenant_id, create_time, update_time, del_flag FROM waterfee_meter_reading_task WHERE task_status = #{taskStatus} AND is_cycle = #{isCycle} AND del_flag = '0'")
    List<WaterfeeReadingTask> selectTasksByStatusAndCycle(@Param("taskStatus") String taskStatus, @Param("isCycle") String isCycle);
}
