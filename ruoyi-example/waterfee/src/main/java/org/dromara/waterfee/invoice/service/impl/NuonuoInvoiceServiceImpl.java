package org.dromara.waterfee.invoice.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNOpenSDK;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.system.api.RemoteConfigService;
import org.dromara.waterfee.app.domain.vo.AppUserBillConsumptionVo;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.bill.service.IPaymentDetailService;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.invoice.accessToken.NuonuoAccessToken;
import org.dromara.waterfee.invoice.domain.*;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.waterfee.invoice.enums.InvoiceGoodsEnum;
import org.dromara.waterfee.invoice.enums.NuonuoInvoiceStatusEnum;
import org.dromara.waterfee.invoice.mapper.InvoiceRecordMapper;
import org.dromara.waterfee.invoice.service.IInvoiceRecordService;
import org.dromara.waterfee.invoice.service.INuonuoTokenService;
import org.dromara.waterfee.invoice.service.NuonuoInvoiceService;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.service.IWaterfeePriceConfigService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class NuonuoInvoiceServiceImpl implements NuonuoInvoiceService {

    @Autowired
    private INuonuoTokenService nuonuoTokenService;

    @Autowired
    private IWaterfeeBillService waterfeeBillService;

    @Autowired
    private IInvoiceRecordService invoiceRecordService;

    @Autowired
    private InvoiceRecordMapper invoiceRecordMapper;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    @Autowired
    private IWaterfeePriceConfigService waterfeePriceConfigService;

    /** 填写应用的appKey */
    @Value("${invoice.nuonuo.client_id}")
    private String clientId;

    /** 填写应用的appSecret */
    @Value("${invoice.nuonuo.client_secret}")
    private String clientSecret;

    /** 请求地址 */
    @Value("${invoice.nuonuo.url}")
    private String url;

    /** 自来水商品税收分类编码  */
    public final String zlsGoodsCode = "1100301010000000000";

    /** 不征税自来水 商品税收分类编码 */
    public final String bzszlsGoodsCode = "6110000000000000000";


    /**
     * 发起开票请求
     *
     */
    public String requestInvoice(RequestInvoiceParam requestInvoiceParam) {
        //从参数配置获取开票参数
        String invoiceConfig = remoteConfigService.selectConfigByKey("waterfee.invoice.param");
        if(StringUtils.isBlank(invoiceConfig)) {
            throw new ServiceException("缺少必要参数，请检查系统参数配置");
        }
        JSONObject jsonConfig = JSONObject.parseObject(invoiceConfig);
        /** 企业税号 */
        String taxNum;
        /** 销方电话 */
        String salerTel;
        /** 销方地址 */
        String salerAddress;
        /** 开票员 */
        String clerk;
        if(jsonConfig.containsKey("taxNum")) {
            taxNum = jsonConfig.getString("taxNum");
        }else {
            throw new ServiceException("缺少企业税号参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("salerTel")) {
            salerTel = jsonConfig.getString("salerTel");
        }else {
            salerTel = "";
        }
        if(jsonConfig.containsKey("salerAddress")) {
            salerAddress = jsonConfig.getString("salerAddress");
        }else {
            salerAddress = "";
        }
        if(StringUtils.isNotBlank(requestInvoiceParam.getClerk())) {
            clerk = requestInvoiceParam.getClerk();
        }else {
            if(jsonConfig.containsKey("clerk")) {
                clerk = jsonConfig.getString("clerk");
            }else {
                throw new ServiceException("缺少开票员参数，请检查系统参数配置");
            }
        }
        if(requestInvoiceParam.getPushMode().equals("0") && StringUtils.isBlank(requestInvoiceParam.getEmail())) {
            throw new ServiceException("邮箱不能为空");
        }else if((requestInvoiceParam.getPushMode().equals("1") || requestInvoiceParam.getPushMode().equals("")) && StringUtils.isBlank(requestInvoiceParam.getBuyerPhone())) {
            throw new ServiceException("手机号不能为空");
        }else if(requestInvoiceParam.getPushMode().equals("2") && (StringUtils.isBlank(requestInvoiceParam.getEmail()) || StringUtils.isBlank(requestInvoiceParam.getBuyerPhone()))) {
            throw new ServiceException("邮箱和手机号不能为空");
        }
        if(requestInvoiceParam.getIsEnterprise().equals(true)) {
            if(StringUtils.isBlank(requestInvoiceParam.getBuyerTaxNumber())) {
                throw new ServiceException("购方企业税号不能为空");
            }
        }
        if(StringUtils.isBlank(requestInvoiceParam.getInvoiceType())) {
            throw new ServiceException("开票类型不能为空");
        }
        //获取订单时间
        WaterfeeBillVo waterfeeBillVo = waterfeeBillService.queryByBillNumber(requestInvoiceParam.getBillNumber());
        if(waterfeeBillVo == null) {
            throw new ServiceException("账单信息不存在");
        }
        Date invoiceDate = waterfeeBillVo.getCreateTime() == null ? new Date() : waterfeeBillVo.getCreateTime();
        // 使用MyBatis-Plus查询状态为process的发票记录
        LambdaQueryWrapper<InvoiceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvoiceRecord::getBillNumber, requestInvoiceParam.getBillNumber());
        queryWrapper.eq(InvoiceRecord::getInvoiceStatus, "success").or().eq(InvoiceRecord::getInvoiceStatus, "process");
        queryWrapper.eq(InvoiceRecord::getInvoiceType, requestInvoiceParam.getInvoiceType());
        List<InvoiceRecord> invoiceRecords = invoiceRecordMapper.selectList(queryWrapper);
        if(invoiceRecords.size() > 0) {
            throw new ServiceException("该订单发票开具中或已完成开具");
        }
        //创建发票记录
        InvoiceRecordBo invoiceRecordBo = new InvoiceRecordBo();
        invoiceRecordBo.setBillNumber(requestInvoiceParam.getBillNumber());
        invoiceRecordBo.setInvoiceType(requestInvoiceParam.getInvoiceType());
        invoiceRecordBo.setBuyerName(requestInvoiceParam.getBuyerName());
        invoiceRecordBo.setIsEnterprise(requestInvoiceParam.getIsEnterprise());
        invoiceRecordBo.setBuyerTaxNumber(requestInvoiceParam.getBuyerTaxNumber());
        invoiceRecordBo.setClerk(requestInvoiceParam.getClerk());
        invoiceRecordBo.setInvoiceLine(requestInvoiceParam.getInvoiceLine());
        invoiceRecordBo.setUserId(waterfeeBillVo.getCustomerId());
        //日期格式化
        LocalDateTime ldt = invoiceDate.toInstant()
            .atZone(ZoneId.of("Asia/Shanghai")) // 时区
            .toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String invoiceDateString = ldt.format(formatter);

        NNOpenSDK sdk = NNOpenSDK.getIntance();
        //请求方法
        String method = "nuonuo.OpeMplatform.requestBillingNew";
        String token = nuonuoTokenService.getValidToken();
        //请求参数
        JSONObject jsonObject = new JSONObject();
        JSONObject order = new JSONObject();
        //购方名称
        order.put("buyerName", requestInvoiceParam.getBuyerName());
        //购方税号
        order.put("buyerTaxNum", requestInvoiceParam.getBuyerTaxNumber());
        //销方税号
        order.put("salerTaxNum", taxNum);
        //销方电话
        if(StringUtils.isNotBlank(salerTel)) {
            order.put("salerTel", salerTel);
        }
        //销方地址
        if(StringUtils.isNotBlank(salerAddress)) {
            order.put("salerAddress", salerAddress);
        }
        InvoiceRecord invoiceRecord;

        if(requestInvoiceParam.getInvoiceType().equals("1")) {
            //内部订单编号
            order.put("orderNo", requestInvoiceParam.getBillNumber());
        }
        if(requestInvoiceParam.getInvoiceType().equals("2")) {
            if(requestInvoiceParam.getInvoiceId() == null) {
                throw new ServiceException("缺少需要冲红的蓝票的记录id");
            }
            invoiceRecord = invoiceRecordMapper.selectById(requestInvoiceParam.getInvoiceId());
            if(invoiceRecord == null) {
                throw new ServiceException("冲红的票据信息不存在");
            }
            if(!invoiceRecord.getInvoiceStatus().equals("success")) {
                throw new ServiceException("只能冲红已开票的蓝票");
            }
            if(!invoiceRecord.getInvoiceType().equals("1")) {
                throw new ServiceException("只能冲红蓝票");
            }
            //内部订单编号
            order.put("orderNo", invoiceRecord.getBillNumber());
            order.put("invoiceCode", invoiceRecord.getInvoiceCode());
            order.put("invoiceNum", invoiceRecord.getInvoiceNo());
        }
        //订单时间
        order.put("invoiceDate", invoiceDateString);
        //开票员
        order.put("clerk", clerk);
        order.put("pushMode", requestInvoiceParam.getPushMode());
        order.put("email", requestInvoiceParam.getEmail());
        order.put("buyerPhone", requestInvoiceParam.getBuyerPhone());
        //开票类型 1:蓝票;2:红票
        order.put("invoiceType", requestInvoiceParam.getInvoiceType());
        //发票种类 0普通发票（电票），1数电专票(电子)
        if(StringUtils.isNotBlank(requestInvoiceParam.getInvoiceLine())) {
            if(requestInvoiceParam.getInvoiceLine().equals("0")) {
                order.put("invoiceLine", "pc");
            }else if(requestInvoiceParam.getInvoiceLine().equals("1")) {
                order.put("invoiceLine", "bs");
            }
        }
        JSONArray invoiceDetailList = getInvoiceDetailList(waterfeeBillVo, requestInvoiceParam.getInvoiceType());
//        JSONObject invoiceDetail = new JSONObject();
//        //发票商品信息
//        invoiceDetail.put("goodsName", "阶梯一水费");
//        invoiceDetail.put("goodsCode", zlsGoodsCode);
//        //是否含税 0:不含税,1:含税
//        invoiceDetail.put("withTaxFlag", "1");
//        //单价（精确到小数点后8位）
//        invoiceDetail.put("price", "1.00000000");
//        //数量（精确到小数点后8位）
//        invoiceDetail.put("num", "1.00000000");
//        //单位
//        invoiceDetail.put("unit", "吨");
//        //税额（精确到小数点后2位）
//        invoiceDetail.put("tax", "0.03");
//        //税率
//        invoiceDetail.put("taxRate", "0.03");
//        //不含税金额（精确到小数点后2位）
//        invoiceDetail.put("taxExcludedAmount", "0.97");
//        //含税金额（精确到小数点后2位）
//        invoiceDetail.put("taxIncludedAmount", "1.00");
//        invoiceDetailList.add(invoiceDetail);
        order.put("invoiceDetail", invoiceDetailList);
        jsonObject.put("order", order);
        String senid = UUID.randomUUID().toString().replace("-", ""); // 唯一标识，32位随机码，无需修改，保持
        String content = JSONObject.toJSONString(jsonObject);
        String result = sdk.sendPostSyncRequest(url, senid, clientId, clientSecret, token, taxNum, method, content);
        log.info("url:{}, senid:{}, clientId:{}, clientSecret:{}, token:{}, taxNum:{}, method:{}, content:{}",url, senid, clientId, clientSecret, token, taxNum, method, content);
        log.info("nuonuoInvoiceServiceImpl.requestInvoice result:{}",result);
        JSONObject resultJson = JSONObject.parseObject(result);
        String code = resultJson.getString("code");
        if(!code.equals("E0000")){
            String errorMsg = resultJson.getString("describe");
            throw new ServiceException(errorMsg+"，开票失败，请稍后重试");
        }else {
            JSONObject resultData = resultJson.getJSONObject("result");
            //获取发票流水号
            String invoiceSerialNum = resultData.getString("invoiceSerialNum");
            invoiceRecordBo.setSerialNo(invoiceSerialNum);
            invoiceRecordBo.setInvoiceStatus("process");
        }
        invoiceRecordBo.setCreateTime(DateTime.now().toDate());
        invoiceRecordService.insertByBo(invoiceRecordBo);
        return result;
    }

    public R webRequestInvoice(RequestInvoiceParam requestInvoiceParam) {
        String result = this.requestInvoice(requestInvoiceParam);
        JSONObject resultJson = JSONObject.parseObject(result);
        if("E0000".equals(resultJson.getString("code"))) {
            return R.ok(resultJson.getString("describe"));
        }else {
            return R.fail(resultJson.getString("describe"));
        }
    }

    /**
     * 查询发票详情
     * @param queryInvoiceParam
     * @return
     */
    public String queryInvoiceStatus(QueryInvoiceParam queryInvoiceParam) {
        //从参数配置获取开票参数
        String invoiceConfig = remoteConfigService.selectConfigByKey("waterfee.invoice.param");
        if(StringUtils.isBlank(invoiceConfig)) {
            throw new ServiceException("缺少必要参数，请检查系统参数配置");
        }
        JSONObject jsonConfig = JSONObject.parseObject(invoiceConfig);
        /** 企业税号 */
        String taxNum;
        /** 销方电话 */
        String salerTel;
        /** 销方地址 */
        String salerAddress;
        /** 开票员 */
        String clerk;
        if(jsonConfig.containsKey("taxNum")) {
            taxNum = jsonConfig.getString("taxNum");
        }else {
            throw new ServiceException("缺少企业税号参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("salerTel")) {
            salerTel = jsonConfig.getString("salerTel");
        }else {
            throw new ServiceException("缺少销方电话参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("salerAddress")) {
            salerAddress = jsonConfig.getString("salerAddress");
        }else {
            throw new ServiceException("缺少销方地址参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("clerk")) {
            clerk = jsonConfig.getString("clerk");
        }else {
            throw new ServiceException("缺少开票员参数，请检查系统参数配置");
        }

        NNOpenSDK sdk = NNOpenSDK.getIntance();
        //请求方法
        String method = "nuonuo.OpeMplatform.queryInvoiceResult";
        String token = nuonuoTokenService.getValidToken();
        JSONObject jsonObject = new JSONObject();
        if(queryInvoiceParam.getSerialNos() != null && queryInvoiceParam.getSerialNos().size() > 0) {
            jsonObject.put("serialNos", queryInvoiceParam.getSerialNos());
        }else if(queryInvoiceParam.getOrderNos() != null && queryInvoiceParam.getOrderNos().size() > 0) {
            jsonObject.put("orderNos", queryInvoiceParam.getOrderNos());
        }else {
            log.error("查询发票详情失败，发票流水号和订单编号不能同时为空");
            throw new ServiceException("查询发票详情失败，发票流水号和订单编号不能同时为空");
        }
        if(StringUtils.isNotBlank(queryInvoiceParam.getIsOfferInvoiceDetail())) {
            jsonObject.put("isOfferInvoiceDetail", queryInvoiceParam.getIsOfferInvoiceDetail());
        }
        if(StringUtils.isNotBlank(queryInvoiceParam.getIsOfferBlueDetailIndex())) {
            jsonObject.put("isOfferBlueDetailIndex", queryInvoiceParam.getIsOfferBlueDetailIndex());
        }
        String content = JSONObject.toJSONString(jsonObject);
        String senid = UUID.randomUUID().toString().replace("-", ""); // 唯一标识，32位随机码，无需修改，保持
        String result = sdk.sendPostSyncRequest(url, senid, clientId, clientSecret, token, taxNum, method, content);
//        JSONObject resultJson = JSONObject.parseObject(result);
//        String code = resultJson.getString("code");
//        if(code.equals("E0000")) {
//            JSONObject resultData = resultJson.getJSONObject("result");
//            String status = resultData.getString("status");
//            if(status.equals(NuonuoInvoiceStatusEnum.INVOICED_COMPLETED.getCode())) {
//            }
//        }else {
//            String errorMsg = resultJson.getString("describe");
//            log.error("查询发票详情失败，错误信息：{}",errorMsg);
//            throw new ServiceException(errorMsg+"，查询发票详情失败，请稍后重试");
//        }
        return result;
    }

    /**
     * 发票作废
     * @param invoiceCancellationParam
     * @return
     */
    public String invoiceCancellation(InvoiceCancellationParam invoiceCancellationParam) {
        //从参数配置获取开票参数
        String invoiceConfig = remoteConfigService.selectConfigByKey("waterfee.invoice.param");
        if(StringUtils.isBlank(invoiceConfig)) {
            throw new ServiceException("缺少必要参数，请检查系统参数配置");
        }
        if(StringUtils.isBlank(invoiceCancellationParam.getInvoiceCode())) {
            throw new ServiceException("发票代码不能为空");
        }
        if(StringUtils.isBlank(invoiceCancellationParam.getInvoiceNo())) {
            throw new ServiceException("发票号码不能为空");
        }
        if(StringUtils.isBlank(invoiceCancellationParam.getSerialNo())) {
            throw new ServiceException("发票流水号不能为空");
        }
        // 使用MyBatis-Plus查询状态为发票记录
        LambdaQueryWrapper<InvoiceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvoiceRecord::getSerialNo, invoiceCancellationParam.getSerialNo());
        queryWrapper.eq(InvoiceRecord::getInvoiceCode, invoiceCancellationParam.getInvoiceCode());
        queryWrapper.eq(InvoiceRecord::getInvoiceNo, invoiceCancellationParam.getInvoiceNo());
        InvoiceRecord invoiceRecord = invoiceRecordMapper.selectOne(queryWrapper);
        if(invoiceRecord == null) {
            throw new ServiceException("发票记录不存在");
        }
        if(invoiceRecord.getInvoiceStatus().equals("process")) {
            throw new ServiceException("发票开具中，请等待开票结果");
        }
        JSONObject jsonConfig = JSONObject.parseObject(invoiceConfig);
        /** 企业税号 */
        String taxNum;
        /** 销方电话 */
        String salerTel;
        /** 销方地址 */
        String salerAddress;
        /** 开票员 */
        String clerk;
        if(jsonConfig.containsKey("taxNum")) {
            taxNum = jsonConfig.getString("taxNum");
        }else {
            throw new ServiceException("缺少企业税号参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("salerTel")) {
            salerTel = jsonConfig.getString("salerTel");
        }else {
            throw new ServiceException("缺少销方电话参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("salerAddress")) {
            salerAddress = jsonConfig.getString("salerAddress");
        }else {
            throw new ServiceException("缺少销方地址参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("clerk")) {
            clerk = jsonConfig.getString("clerk");
        }else {
            throw new ServiceException("缺少开票员参数，请检查系统参数配置");
        }
        NNOpenSDK sdk = NNOpenSDK.getIntance();
        String method = "nuonuo.electronInvoice.invoiceCancellation";
        String token = nuonuoTokenService.getValidToken();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("invoiceCode", invoiceCancellationParam.getInvoiceCode());
        jsonObject.put("invoiceNo", invoiceCancellationParam.getInvoiceNo());
        jsonObject.put("serialNo", invoiceCancellationParam.getSerialNo());
        String content = JSONObject.toJSONString(jsonObject);
        String senid = UUID.randomUUID().toString().replace("-", "");
        String result = sdk.sendPostSyncRequest(url, senid, clientId, clientSecret, token, taxNum, method, content);
        log.info("发票作废结果：{}",result);
        JSONObject resultJson = JSONObject.parseObject(result);
        String code = resultJson.getString("code");
        if(code.equals("E0000")) {
            //更新发票状态
            invoiceRecord.setInvoiceStatus("cancel");
            invoiceRecordMapper.updateById(invoiceRecord);
        }
        return result;
    }

    public R webInvoiceCancellation(Long invoiceId) {
        InvoiceRecord invoiceRecord = invoiceRecordMapper.selectById(invoiceId);
        if(invoiceRecord == null) {
            throw new ServiceException("发票记录不存在");
        }
        if(invoiceRecord.getInvoiceStatus().equals("process")) {
            throw new ServiceException("发票开具中，请等待开票结果");
        }
        InvoiceCancellationParam invoiceCancellationParam = new InvoiceCancellationParam();
        invoiceCancellationParam.setInvoiceCode(invoiceRecord.getInvoiceCode());
        invoiceCancellationParam.setInvoiceNo(invoiceRecord.getInvoiceNo());
        invoiceCancellationParam.setSerialNo(invoiceRecord.getSerialNo());
        String result = this.invoiceCancellation(invoiceCancellationParam);
        JSONObject resultJson = JSONObject.parseObject(result);
        if("E0000".equals(resultJson.getString("code"))) {
            return R.ok(resultJson.getString("describe"));
        }else {
            return R.fail(resultJson.getString("describe"));
        }
    }

    /**
     * 获取发票用水信息
//     * @param bill
     * @return
     */
//    JSONArray getWaterfeeInvoiceDetailList(WaterfeeBillVo bill) {
//        // 获取账单对应的价格配置ID和用户ID
//        Long priceConfigId = bill.getPricePlanId();
//        Long userId = bill.getCustomerId();
//    }

    @Override
    public R refreshInvoiceStatusBySerialNo(String serialNo) {
        try {
            // 查询指定serialNo的发票记录，且状态为process
            LambdaQueryWrapper<InvoiceRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InvoiceRecord::getSerialNo, serialNo);
//            queryWrapper.eq(InvoiceRecord::getInvoiceStatus, "process");
            InvoiceRecord record = invoiceRecordMapper.selectOne(queryWrapper);
            if (record == null) {
                return R.fail("未找到处于开票中状态的发票记录，或发票已处理完成");
            }
            // 构建查询参数
            QueryInvoiceParam queryParam = new QueryInvoiceParam();
            queryParam.setSerialNos(Collections.singletonList(serialNo));
            // 查询发票状态
            String result = this.queryInvoiceStatus(queryParam);
            log.info("手动刷新发票状态结果: {}", result);
            JSONObject resultJson = JSONObject.parseObject(result);
            if ("E0000".equals(resultJson.getString("code"))) {
                JSONArray invoices = resultJson.getJSONArray("result");
                if (invoices == null || invoices.isEmpty()) {
                    return R.fail("未查询到发票状态信息");
                }
                JSONObject invoice = invoices.getJSONObject(0);
                String status = invoice.getString("status");
                InvoiceRecordBo updateBo = new InvoiceRecordBo();
                updateBo.setInvoiceId(record.getInvoiceId());
                if (status.equals(NuonuoInvoiceStatusEnum.INVOICED_COMPLETED.getCode())) {
                    updateBo.setInvoiceStatus("success");
                } else if (status.equals(NuonuoInvoiceStatusEnum.INVOICE_FAILED.getCode()) || status.equals(NuonuoInvoiceStatusEnum.SIGN_FAILED_AFTER_SUCCESS.getCode())) {
                    updateBo.setInvoiceStatus("failure");
                } else if (status.equals(NuonuoInvoiceStatusEnum.INVOICE_VOIDED.getCode())) {
                    updateBo.setInvoiceStatus("cancel");
                } else {
                    return R.ok("发票状态未变更，无需更新");
                }
                if (invoice.containsKey("pdfUrl")) {
                    updateBo.setPdfUrl(invoice.getString("pdfUrl"));
                }
                Long time = invoice.getLongValue("invoiceTime");
                updateBo.setInvoiceTime(new Date(time));
                updateBo.setInvoiceCode(invoice.getString("invoiceCode"));
                updateBo.setInvoiceNo(invoice.getString("invoiceNo"));
                updateBo.setInvoiceKind(invoice.getString("invoiceKind"));
                updateBo.setOrderAmount(invoice.getBigDecimal("orderAmount"));
                updateBo.setExTaxAmount(invoice.getBigDecimal("exTaxAmount"));
                updateBo.setTaxAmount(invoice.getBigDecimal("taxAmount"));
                invoiceRecordService.updateByBo(updateBo);
                return R.ok("发票状态已更新: " + updateBo.getInvoiceStatus());
            } else {
                return R.fail("查询发票状态失败: " + resultJson.getString("describe"));
            }
        } catch (Exception e) {
            log.error("手动刷新发票状态异常", e);
            return R.fail("手动刷新发票状态异常: " + e.getMessage());
        }
    }

    private JSONArray getInvoiceDetailList(WaterfeeBillVo bill, String invoiceType) {
        JSONObject waterUseDetail;
        try {
            waterUseDetail = JSONObject.parseObject(bill.getDetailJson());
        }catch (Exception e) {
            throw new ServiceException("账单详情json解析失败");
        }
        WaterfeePriceConfigVo waterfeePriceConfigVo = waterfeePriceConfigService.queryById(bill.getPricePlanId());
        if(waterfeePriceConfigVo == null) {
            throw new ServiceException("获取价格配置失败");
        }
        //是否为个人用户
        Boolean isIndividual;
        if(waterfeePriceConfigVo.getWaterUseType().equals("1")) {
            isIndividual = true;
        }else {
            isIndividual = false;
        }
        //是否需要污水处理费
        Boolean ifNeedSewageTreatmentFee = true;
        //污水处理费 默认为0 从bill的detailJson中获取
        BigDecimal sewageTreatmentFee = new BigDecimal(0);
        //不征增值税自来水价格（水资源税）
        BigDecimal waterSourceFee = new BigDecimal(0);
        JSONArray invoiceDetailList = new JSONArray();
        if(waterUseDetail != null) {
            if(waterUseDetail.containsKey("tierDetails")) {
                JSONArray tierDetails = waterUseDetail.getJSONArray("tierDetails");
                for(Object tierDetailObj : tierDetails) {
                    JSONObject invoiceDetail = new JSONObject();
                    JSONObject tierDetail = (JSONObject) tierDetailObj;
                    //判断是否为单位户 区分明细名称
                    if(isIndividual) {
                        if(tierDetail.get("tierNumber").equals("1")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.LEVEL_ONE.getGoodsName());
                            //判断是否需要支付污水处理费
                            if(tierDetail.get("sewageTreatmentFee").equals("0")) {
                                ifNeedSewageTreatmentFee = false;
                            }else {
                                ifNeedSewageTreatmentFee = true;
                                sewageTreatmentFee = sewageTreatmentFee.add(new BigDecimal(tierDetail.getString("sewageTreatmentFee")));
                            }
                            //水资源税单价
                            if(!tierDetail.get("waterResourceTax").equals("0")) {
                                waterSourceFee = waterSourceFee.add(new BigDecimal(tierDetail.getString("waterResourceTax")));
                            }
                        }else if(tierDetail.get("tierNumber").equals("2")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.LEVEL_TWO.getGoodsName());
                        }else if(tierDetail.get("tierNumber").equals("3")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.LEVEL_THREE.getGoodsName());
                        }
                    }else {
                        if(tierDetail.get("tierNumber").equals("1")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.QUOTA_ONE.getGoodsName());
                            //判断是否需要支付污水处理费
                            if(tierDetail.get("sewageTreatmentFee").equals("0")) {
                                ifNeedSewageTreatmentFee = false;
                            }else {
                                ifNeedSewageTreatmentFee = true;
                                sewageTreatmentFee = sewageTreatmentFee.add(new BigDecimal(tierDetail.getString("sewageTreatmentFee")));
                            }
                            //水资源税单价
                            if(!tierDetail.get("waterResourceTax").equals("0")) {
                                waterSourceFee = waterSourceFee.add(new BigDecimal(tierDetail.getString("waterResourceTax")));
                            }
                        }else if(tierDetail.get("tierNumber").equals("2")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.QUOTA_TWO.getGoodsName());
                        }else if(tierDetail.get("tierNumber").equals("3")) {
                            invoiceDetail.put("goodsName", InvoiceGoodsEnum.QUOTA_THREE.getGoodsName());
                        }
                    }
                    invoiceDetail.put("goodsCode", zlsGoodsCode);
                    //单价是否含税 0:不含税,1:含税
                    invoiceDetail.put("withTaxFlag", "1");
                    //单价（精确到小数点后8位）
                    invoiceDetail.put("price", tierDetail.getString("price"));
                    //数量单位
                    invoiceDetail.put("unit", "吨");
                    //税率
                    invoiceDetail.put("taxRate", "0.03");
                    //计算含税总价
                    BigDecimal price = tierDetail.getBigDecimal("price");
                    BigDecimal amount = tierDetail.getBigDecimal("amount");
                    BigDecimal taxIncludedAmount = price.multiply(amount);
                    if(invoiceType.equals("2")) {
                        //冲红 数量金额须为负
                        //数量（精确到小数点后8位）
                        invoiceDetail.put("num", "-" + tierDetail.getString("amount"));
                        //含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxIncludedAmount", "-" + taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //不含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxExcludedAmount", "-" + taxIncludedAmount.divide(new BigDecimal("1.03"), 2, BigDecimal.ROUND_HALF_UP));
                        //税额（精确到小数点后2位）
                        invoiceDetail.put("tax", "-" + taxIncludedAmount.subtract(taxIncludedAmount.divide(new BigDecimal("1.03")).setScale(2, BigDecimal.ROUND_HALF_UP)));
                    }else {
                        //蓝票
                        //数量（精确到小数点后8位）
                        invoiceDetail.put("num", tierDetail.getString("amount"));
                        //含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxIncludedAmount", taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //不含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxExcludedAmount", taxIncludedAmount.divide(new BigDecimal("1.03"), 2, BigDecimal.ROUND_HALF_UP));
                        //税额（精确到小数点后2位）
                        invoiceDetail.put("tax", taxIncludedAmount.subtract(taxIncludedAmount.divide(new BigDecimal("1.03")).setScale(2, BigDecimal.ROUND_HALF_UP)));
                    }
                    invoiceDetailList.add(invoiceDetail);
                }
                //污水处理费
                if(ifNeedSewageTreatmentFee) {
                    JSONObject invoiceDetail = new JSONObject();
                    invoiceDetail.put("goodsName", InvoiceGoodsEnum.SEWAGE.getGoodsName());
                    invoiceDetail.put("goodsCode", zlsGoodsCode);
                    //单价是否含税 0:不含税,1:含税
                    invoiceDetail.put("withTaxFlag", "1");
                    //单价（精确到小数点后8位）
                    invoiceDetail.put("price", sewageTreatmentFee);
                    //数量单位
                    invoiceDetail.put("unit", "吨");
                    //税率
                    invoiceDetail.put("taxRate", "0");
                    //优惠政策标识 03: 免税 04: 不征税
                    invoiceDetail.put("favouredPolicyFlag", "03");
                    //计算含税总价
                    BigDecimal amount = waterUseDetail.getBigDecimal("amount");
                    BigDecimal taxIncludedAmount = sewageTreatmentFee.multiply(amount);
                    if(invoiceType.equals("2")) {
                        //冲红 数量金额须为负
                        //数量（精确到小数点后8位）
                        invoiceDetail.put("num", "-" + waterUseDetail.getString("totalAmount"));
                        //含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxIncludedAmount", "-" + taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //不含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxExcludedAmount", "-" + taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //税额（精确到小数点后2位）
                        invoiceDetail.put("tax", "0");
                    }else {
                        //蓝票
                        //数量（精确到小数点后8位）
                        invoiceDetail.put("num", waterUseDetail.getString("totalAmount"));
                        //含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxIncludedAmount", taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //不含税金额（精确到小数点后2位）
                        invoiceDetail.put("taxExcludedAmount", taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        //税额（精确到小数点后2位）
                        invoiceDetail.put("tax", "0");
                    }
                    invoiceDetailList.add(invoiceDetail);
                }
                //不征增值税自来水
                JSONObject invoiceDetail = new JSONObject();
                invoiceDetail.put("goodsName", InvoiceGoodsEnum.NO_TAX.getGoodsName());
                invoiceDetail.put("goodsCode", bzszlsGoodsCode);
                //单价是否含税 0:不含税,1:含税
                invoiceDetail.put("withTaxFlag", "1");
                //单价（精确到小数点后8位）
                invoiceDetail.put("price", waterSourceFee);
                //数量单位
                invoiceDetail.put("unit", "吨");
                //税率
                invoiceDetail.put("taxRate", "0");
                //优惠政策标识 03:免税 04: 不征税
                invoiceDetail.put("favouredPolicyFlag", "04");
                //计算含税总价
                BigDecimal amount = waterUseDetail.getBigDecimal("amount");
                BigDecimal taxIncludedAmount = waterSourceFee.multiply(amount);
                if(invoiceType.equals("2")) {
                    //冲红 数量金额须为负
                    //数量（精确到小数点后8位）
                    invoiceDetail.put("num", "-" + amount);
                    //含税金额（精确到小数点后2位）
                    invoiceDetail.put("taxIncludedAmount", "-" + taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                    //不含税金额（精确到小数点后2位）
                    invoiceDetail.put("taxExcludedAmount", "-" + taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                    //税额（精确到小数点后2位）
                    invoiceDetail.put("tax", "0");
                }else {
                    //蓝票
                    //数量（精确到小数点后8位）
                    invoiceDetail.put("num", amount);
                    //含税金额（精确到小数点后2位）
                    invoiceDetail.put("taxIncludedAmount", taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                    //不含税金额（精确到小数点后2位）
                    invoiceDetail.put("taxExcludedAmount", taxIncludedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                    //税额（精确到小数点后2位）
                    invoiceDetail.put("tax", "0");
                }
                invoiceDetailList.add(invoiceDetail);
            }
        }
        return invoiceDetailList;
//        //发票商品信息
//        invoiceDetail.put("goodsName", "阶梯一水费");
//        invoiceDetail.put("goodsCode", zlsGoodsCode);
//        //是否含税 0:不含税,1:含税
//        invoiceDetail.put("withTaxFlag", "1");
//        //单价（精确到小数点后8位）
//        invoiceDetail.put("price", "1.00000000");
//        //数量（精确到小数点后8位）
//        invoiceDetail.put("num", "1.00000000");
//        //单位
//        invoiceDetail.put("unit", "吨");
//        //税额（精确到小数点后2位）
//        invoiceDetail.put("tax", "0.03");
//        //税率
//        invoiceDetail.put("taxRate", "0.03");
//        //不含税金额（精确到小数点后2位）
//        invoiceDetail.put("taxExcludedAmount", "0.97");
//        //含税金额（精确到小数点后2位）
//        invoiceDetail.put("taxIncludedAmount", "1.00");
//        invoiceDetailList.add(invoiceDetail);
    }

    /**
     * 快速红冲
     * @param quickRedInvoiceParam
     * @return
     */
    public R quickRedInvoice(QuickRedInvoiceParam quickRedInvoiceParam) {
        RequestInvoiceParam requestInvoiceParam = new RequestInvoiceParam();
        BeanUtils.copyProperties(quickRedInvoiceParam, requestInvoiceParam);
        InvoiceRecord invoiceRecord = invoiceRecordMapper.selectById(quickRedInvoiceParam.getInvoiceId());
        if(invoiceRecord == null) {
            throw new ServiceException("发票记录不存在");
        }
        requestInvoiceParam.setBillNumber(invoiceRecord.getBillNumber());
        //指定开具红票
        requestInvoiceParam.setInvoiceType("2");
        requestInvoiceParam.setInvoiceLine(invoiceRecord.getInvoiceLine());
        requestInvoiceParam.setClerk(invoiceRecord.getClerk());
        requestInvoiceParam.setBuyerTaxNumber(invoiceRecord.getBuyerTaxNumber());
        requestInvoiceParam.setIsEnterprise(invoiceRecord.getIsEnterprise());
        return this.webRequestInvoice(requestInvoiceParam);
    }

}
