package org.dromara.waterfee.user.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBasicInfoChangeRecordBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserBasicInfoChangeRecordVo;
import org.dromara.waterfee.user.domain.WaterfeeUserBasicInfoChangeRecord;
import org.dromara.waterfee.user.mapper.WaterfeeUserBasicInfoChangeRecordMapper;
import org.dromara.waterfee.user.service.IWaterfeeUserBasicInfoChangeRecordService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用水用户基础信息变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class WaterfeeUserBasicInfoChangeRecordServiceImpl implements IWaterfeeUserBasicInfoChangeRecordService {

    private final WaterfeeUserBasicInfoChangeRecordMapper baseMapper;

    /**
     * 查询用水用户基础信息变更记录
     *
     * @param basicInfoChangeId 主键
     * @return 用水用户基础信息变更记录
     */
    @Override
    public WaterfeeUserBasicInfoChangeRecord queryById(Long basicInfoChangeId){
        return baseMapper.selectById(basicInfoChangeId);
    }

    /**
     * 分页查询用水用户基础信息变更记录列表
     *
     * @param recordQo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户基础信息变更记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeUserBasicInfoChangeRecordVo> queryPageList(RecordQo recordQo, PageQuery pageQuery) {
        Page<WaterfeeUserBasicInfoChangeRecordVo> result = baseMapper.queryList(pageQuery.build(), recordQo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用水用户基础信息变更记录列表
     *
     * @param bo 查询条件
     * @return 用水用户基础信息变更记录列表
     */
    @Override
    public List<WaterfeeUserBasicInfoChangeRecordVo> queryList(WaterfeeUserBasicInfoChangeRecordBo bo) {
        LambdaQueryWrapper<WaterfeeUserBasicInfoChangeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeUserBasicInfoChangeRecord> buildQueryWrapper(WaterfeeUserBasicInfoChangeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeUserBasicInfoChangeRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeUserBasicInfoChangeRecord::getCreateTime);
        lqw.eq(bo.getCreateTime() != null, WaterfeeUserBasicInfoChangeRecord::getCreateTime, bo.getCreateTime());
        return lqw;
    }

    /**
     * 新增用水用户基础信息变更记录
     *
     * @param bo 用水用户基础信息变更记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeUserBasicInfoChangeRecordBo bo) {
        WaterfeeUserBasicInfoChangeRecord add = MapstructUtils.convert(bo, WaterfeeUserBasicInfoChangeRecord.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBasicInfoChangeId(add.getBasicInfoChangeId());
        }
        return flag;
    }

    /**
     * 修改用水用户基础信息变更记录
     *
     * @param bo 用水用户基础信息变更记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeUserBasicInfoChangeRecordBo bo) {
        WaterfeeUserBasicInfoChangeRecord update = MapstructUtils.convert(bo, WaterfeeUserBasicInfoChangeRecord.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeUserBasicInfoChangeRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用水用户基础信息变更记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
