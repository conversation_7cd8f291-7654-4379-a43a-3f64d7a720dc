package org.dromara.waterfee.bill.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.waterfee.statisticalReport.domain.MechanicalWatchArrearsDetailsVO;

import java.io.Serializable;
import java.util.List;

/**
 * 账单管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface WaterfeeBillMapper extends BaseMapperPlus<WaterfeeBill, WaterfeeBillVo> {

    /**
     * 根据ID查询账单详情，包含用户信息
     *
     * @param billId 账单ID
     * @return 账单详情VO，包含用户信息
     */
    WaterfeeBillVo selectVoById(@Param("billId") Serializable billId);

    List<WaterfeeBillVo> selectVoListByUserId(@Param("userId") Long userId, @Param("status") String status, @Param("month") String month);

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeBillVo>> P selectVoPage(IPage<WaterfeeBill> page, Wrapper<WaterfeeBill> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
     * 查询账单列表，包含用户信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 账单列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    <P extends IPage<WaterfeeBillVo>> P selectBillVoPage(@Param("page") IPage<WaterfeeBill> page, @Param("ew") Wrapper<WaterfeeBill> wrapper);

    /**
     * 查询账单列表，包含用户信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 账单列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    <P extends IPage<WaterfeeBillVo>> P selectBillVoPageDetail(@Param("page") IPage<WaterfeeBill> page, @Param("ew") Wrapper<WaterfeeBill> wrapper);

    /**
     * 查询按表册分组的账单汇总信息
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 表册账单汇总信息分页列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<MeterBookBillSummaryVo> selectMeterBookBillSummaryPage(@Param("page") IPage<?> page, @Param("ew") Wrapper<?> wrapper);

    /**
     * 机械表欠费明细表（搜索条件是区册）
     */
    List<MechanicalWatchArrearsDetailsVO> getMechanicalWatchArrearsDetails(@Param("meterBookId") Long meterBookId);
}
