package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 实收账冲正业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WaterfeeReversalBo extends BaseEntity {

    /**
     * 收费明细ID
     */
    @NotBlank(message = "收费明细ID不能为空", groups = {AddGroup.class})
    private String paymentDetailId;

    /**
     * 冲正原因
     */
    @NotBlank(message = "冲正原因不能为空", groups = {AddGroup.class})
    private String reversalReason;

    /**
     * 冲正类型：MANUAL-手动冲正，SYSTEM-系统冲正
     */
    private String reversalType = "MANUAL";

    /**
     * 是否强制冲正（跳过部分校验）
     */
    private Boolean forceReversal = false;
}
