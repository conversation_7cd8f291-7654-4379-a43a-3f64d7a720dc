package org.dromara.waterfee.wechat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 草稿类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum DraftTypeEnum {

    /**
     * 停水通知
     */
    WATER_OUTAGE("WATER_OUTAGE", "停水通知"),

    /**
     * 供水通知
     */
    WATER_SUPPLY("WATER_SUPPLY", "供水通知");

    /**
     * 类型码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型码获取类型名称
     *
     * @param code 类型码
     * @return 类型名称
     */
    public static String getNameByCode(String code) {
        for (DraftTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return code;
    }

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 枚举
     */
    public static DraftTypeEnum getByCode(String code) {
        for (DraftTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取所有类型的代码和名称
     *
     * @return 类型列表
     */
    public static java.util.List<java.util.Map<String, String>> getAllTypes() {
        java.util.List<java.util.Map<String, String>> types = new java.util.ArrayList<>();
        for (DraftTypeEnum type : values()) {
            java.util.Map<String, String> typeMap = new java.util.HashMap<>();
            typeMap.put("code", type.getCode());
            typeMap.put("name", type.getName());
            types.add(typeMap);
        }
        return types;
    }
}
