package org.dromara.waterfee.priceManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 违约金配置对象 waterfee_liquidated_damages_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_liquidated_damages_configs")
public class WaterfeeLiquidatedDamagesConfigs extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 违约金名称
     */
    private String name;

    /**
     * 计算方式 (e.g., daily_rate, fixed_amount)
     */
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    private Long fixedAmount;

    /**
     * 利率(%), null if not applicable
     */
    private Long interestRatePercent;

    /**
     * 收取方式 (e.g., term, next_month)
     */
    private String collectionMethod;

    /**
     * 开始日期 (if applicable)
     */
    private Date startDate;

    /**
     * 是否大于本金 (True/False)
     */
    private Long canExceedPrincipal;

    /**
     * 免除违约金启用状态
     */
    private Long waiverEnabled;

    /**
     * 免除截止时间 (if waiver_enabled is true)
     */
    private Date waiverTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
