package org.dromara.waterfee.app.domain.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * App用户详细账单查询视图对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
public class AppUserBillDetailVo implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 用户基本信息
   */
  private UserInfoVo userInfo;

  /**
   * 用水信息
   */
  private WaterConsumptionInfoVo waterConsumptionInfo;

  /**
   * 阶梯水价信息列表
   */
  private List<WaterPriceTierInfoVo> waterPriceTierInfoList;

  /**
   * 水表读数时间信息
   */
  private MeterReadingTimeInfoVo meterReadingTimeInfo;
}