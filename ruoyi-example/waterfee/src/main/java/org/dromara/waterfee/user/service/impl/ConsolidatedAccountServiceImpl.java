package org.dromara.waterfee.user.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountItemBo;
import org.dromara.waterfee.user.mapper.ConsolidatedAccountItemMapper;
import org.dromara.waterfee.user.service.IConsolidatedAccountItemService;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountBo;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountVo;
import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import org.dromara.waterfee.user.mapper.ConsolidatedAccountMapper;
import org.dromara.waterfee.user.service.IConsolidatedAccountService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 合收户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class ConsolidatedAccountServiceImpl implements IConsolidatedAccountService {

    private final ConsolidatedAccountMapper baseMapper;

    private final IConsolidatedAccountItemService consolidatedAccountItemService;

    /**
     * 查询合收户管理
     *
     * @param consolidatedAccountId 主键
     * @return 合收户管理
     */
    @Override
    public ConsolidatedAccount queryById(Long consolidatedAccountId){
        return baseMapper.selectById(consolidatedAccountId);
    }

    @Override
    public ConsolidatedAccountVo getDetailById(Long consolidatedAccountId){
        ConsolidatedAccountVo vo = baseMapper.selectVoById(consolidatedAccountId);
        if(vo != null) {
            List<WaterfeeUser> userList = baseMapper.selectUserListByConsolidatedAccountId(consolidatedAccountId);
            vo.setUserList(userList);
            return vo;
        }else {
            return null;
        }
    }



    /**
     * 分页查询合收户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合收户管理分页列表
     */
    @Override
    public TableDataInfo<ConsolidatedAccountVo> queryPageList(ConsolidatedAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ConsolidatedAccount> lqw = buildQueryWrapper(bo);
        Page<ConsolidatedAccountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的合收户管理列表
     *
     * @param bo 查询条件
     * @return 合收户管理列表
     */
    @Override
    public List<ConsolidatedAccountVo> queryList(ConsolidatedAccountBo bo) {
        LambdaQueryWrapper<ConsolidatedAccount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ConsolidatedAccount> buildQueryWrapper(ConsolidatedAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ConsolidatedAccount> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ConsolidatedAccount::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getConsolidatedAccountNo()), ConsolidatedAccount::getConsolidatedAccountNo, bo.getConsolidatedAccountNo());
        lqw.like(StringUtils.isNotBlank(bo.getConsolidatedAccountName()), ConsolidatedAccount::getConsolidatedAccountName, bo.getConsolidatedAccountName());
        lqw.like(StringUtils.isNotBlank(bo.getOwnerName()), ConsolidatedAccount::getOwnerName, bo.getOwnerName());
        lqw.eq(StringUtils.isNotBlank(bo.getOwnerPhone()), ConsolidatedAccount::getOwnerPhone, bo.getOwnerPhone());
        return lqw;
    }

    /**
     * 新增合收户管理
     *
     * @param bo 合收户管理
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ConsolidatedAccountBo bo) {
        ConsolidatedAccount add = MapstructUtils.convert(bo, ConsolidatedAccount.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setConsolidatedAccountId(add.getConsolidatedAccountId());

            // 处理合收户关系
            if (bo.getUserList() != null && !bo.getUserList().isEmpty()) {
                List<ConsolidatedAccountItemBo> itemBoList = new ArrayList<>();
                for (WaterfeeUser user : bo.getUserList()) {
                    ConsolidatedAccountItemBo itemBo = new ConsolidatedAccountItemBo();
                    itemBo.setConsolidatedAccountId(add.getConsolidatedAccountId());
                    itemBo.setUserId(user.getUserId());
                    itemBoList.add(itemBo);
                }
                consolidatedAccountItemService.insertByBoList(itemBoList);
            }
        }
        return flag;
    }

    /**
     * 修改合收户管理
     *
     * @param bo 合收户管理
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ConsolidatedAccountBo bo) {
        ConsolidatedAccount update = MapstructUtils.convert(bo, ConsolidatedAccount.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        boolean result = baseMapper.updateById(update) > 0;

        if (result) {
            // 先删除原有的合收户关系
            List<Long> ids = new ArrayList<>();
            ids.add(bo.getConsolidatedAccountId());
            consolidatedAccountItemService.deleteByConsolidatedAccountIds(ids);

            // 再添加新的合收户关系
            if (bo.getUserList() != null && !bo.getUserList().isEmpty()) {
                List<ConsolidatedAccountItemBo> itemBoList = new ArrayList<>();
                for (WaterfeeUser user : bo.getUserList()) {
                    ConsolidatedAccountItemBo itemBo = new ConsolidatedAccountItemBo();
                    itemBo.setConsolidatedAccountId(bo.getConsolidatedAccountId());
                    itemBo.setUserId(user.getUserId());
                    itemBoList.add(itemBo);
                }
                consolidatedAccountItemService.insertByBoList(itemBoList);
            }
        }

        return result;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ConsolidatedAccount entity){
        // 校验合收户编号是否重复
        if (StringUtils.isNotEmpty(entity.getConsolidatedAccountNo())) {
            LambdaQueryWrapper<ConsolidatedAccount> lqw = Wrappers.lambdaQuery();
            lqw.eq(ConsolidatedAccount::getConsolidatedAccountNo, entity.getConsolidatedAccountNo());
            
            // 如果是编辑操作，需要排除当前记录
            if (entity.getConsolidatedAccountId() != null) {
                lqw.ne(ConsolidatedAccount::getConsolidatedAccountId, entity.getConsolidatedAccountId());
            }
            
            long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new RuntimeException("合收户编号已存在，请更换其他编号");
            }
        }
    }

    /**
     * 校验并批量删除合收户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        consolidatedAccountItemService.deleteByConsolidatedAccountIds(ids);
        return baseMapper.deleteByIds(ids) > 0;
    }
}
