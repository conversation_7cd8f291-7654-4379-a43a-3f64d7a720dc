package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.statisticalReport.domain.UserTransferDetailsVO;
import org.dromara.waterfee.user.domain.WaterfeeUserTransferOwnershipRecord;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserTransferOwnershipRecordVo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserTransferOwnershipRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用水用户过户记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface WaterfeeUserTransferOwnershipRecordMapper extends BaseMapperPlus<WaterfeeUserTransferOwnershipRecord, WaterfeeUserTransferOwnershipRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeUserTransferOwnershipRecordVo>> P selectVoPage(IPage<WaterfeeUserTransferOwnershipRecord> page, Wrapper<WaterfeeUserTransferOwnershipRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询用水用户过户记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeUserTransferOwnershipRecordVo> queryList(@Param("page") Page<WaterfeeUserTransferOwnershipRecord> page, @Param("query") RecordQo query);

    /**
     * 报表统计 - 用户过户明细表
     * @return
     */
    List<UserTransferDetailsVO> getUserTransferDetails();
}
