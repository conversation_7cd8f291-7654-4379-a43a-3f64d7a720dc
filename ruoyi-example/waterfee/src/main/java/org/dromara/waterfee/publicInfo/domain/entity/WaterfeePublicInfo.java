package org.dromara.waterfee.publicInfo.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;

import java.util.Date;

/**
 * 公共信息对象 waterfee_public_info
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@TableName("waterfee_public_info")
public class WaterfeePublicInfo extends TenantEntity {

    /**
     * 信息ID
     */
    @TableId(type = IdType.AUTO)
    private Long infoId;

    /**
     * 信息标题
     */
    private String title;

    /**
     * 信息内容
     */
    private String content;

    /**
     * 信息类型（1-停水通知 2-供水公告 3-业务常识 4-水费标准 5-营商环境 6-公司简介）
     */
    private String infoType;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 截止时间（针对停水通知有效）
     */
    private Date endTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 点击量
     */
    private Long viewCount;

    /**
     * 状态（0-未发布 1-已发布）
     */
    private String status;

    /**
     * 关联的微信草稿ID
     */
    private Long wechatDraftId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;
}
