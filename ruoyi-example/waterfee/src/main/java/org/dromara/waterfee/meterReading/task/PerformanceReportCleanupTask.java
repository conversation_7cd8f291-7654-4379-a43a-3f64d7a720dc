package org.dromara.waterfee.meterReading.task;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPOutputStream;

/**
 * 性能报告文件清理定时任务
 * 负责清理过期的性能报告文件，压缩旧文件
 */
@Slf4j
@Component
public class PerformanceReportCleanupTask {

    private static final String PERFORMANCE_REPORTS_DIR = "logs/performance-reports";
    private static final int RETENTION_DAYS = 30; // 文件保留天数
    private static final int COMPRESSION_AFTER_DAYS = 7; // 压缩文件的天数阈值

    /**
     * 每天凌晨2点执行文件清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupPerformanceReports() {
        log.info("开始执行性能报告文件清理任务...");

        try {
            Path reportsDir = Paths.get(PERFORMANCE_REPORTS_DIR);
            if (!Files.exists(reportsDir)) {
                log.info("性能报告目录不存在，跳过清理任务");
                return;
            }

            AtomicInteger deletedCount = new AtomicInteger(0);
            AtomicInteger compressedCount = new AtomicInteger(0);
            AtomicInteger totalCount = new AtomicInteger(0);

            // 遍历所有子目录和文件
            Files.walkFileTree(reportsDir, new SimpleFileVisitor<Path>() {
                @NotNull
                @Override
                public FileVisitResult visitFile(@NotNull Path file, @NotNull BasicFileAttributes attrs) throws IOException {
                    totalCount.incrementAndGet();

                    // 获取文件的最后修改时间
                    LocalDateTime fileTime = LocalDateTime.ofInstant(
                        attrs.lastModifiedTime().toInstant(), ZoneId.systemDefault());
                    LocalDateTime now = LocalDateTime.now();

                    long daysDiff = java.time.Duration.between(fileTime, now).toDays();

                    // 删除超过保留期的文件
                    if (daysDiff > RETENTION_DAYS) {
                        Files.delete(file);
                        deletedCount.incrementAndGet();
                        log.debug("删除过期文件: {}", file.getFileName());
                    }
                    // 压缩超过压缩阈值的文件
                    else if (daysDiff > COMPRESSION_AFTER_DAYS && !file.toString().endsWith(".gz")) {
                        compressFile(file);
                        compressedCount.incrementAndGet();
                        log.debug("压缩文件: {}", file.getFileName());
                    }

                    return FileVisitResult.CONTINUE;
                }

                @NotNull
                @Override
                public FileVisitResult visitFileFailed(@NotNull Path file, @NotNull IOException exc) throws IOException {
                    log.warn("访问文件失败: {}, 错误: {}", file, exc.getMessage());
                    return FileVisitResult.CONTINUE;
                }
            });

            log.info("性能报告文件清理完成 - 总文件数: {}, 删除: {}, 压缩: {}",
                totalCount.get(), deletedCount.get(), compressedCount.get());

        } catch (IOException e) {
            log.error("执行性能报告文件清理任务失败", e);
        }
    }

    /**
     * 压缩文件
     */
    private void compressFile(Path originalFile) throws IOException {
        Path compressedFile = Paths.get(originalFile.toString() + ".gz");

        try (GZIPOutputStream gzipOut = new GZIPOutputStream(Files.newOutputStream(compressedFile))) {
            Files.copy(originalFile, gzipOut);
        }

        // 删除原文件
        Files.delete(originalFile);
    }

    /**
     * 手动触发清理任务（用于测试）
     */
    public void manualCleanup() {
        log.info("手动触发性能报告文件清理任务");
        cleanupPerformanceReports();
    }

    /**
     * 获取目录统计信息
     */
    public DirectoryStats getDirectoryStats() {
        DirectoryStats stats = new DirectoryStats();

        try {
            Path reportsDir = Paths.get(PERFORMANCE_REPORTS_DIR);
            if (!Files.exists(reportsDir)) {
                return stats;
            }

            Files.walkFileTree(reportsDir, new SimpleFileVisitor<Path>() {
                @NotNull
                @Override
                public FileVisitResult visitFile(@NotNull Path file, @NotNull BasicFileAttributes attrs) throws IOException {
                    stats.totalFiles++;
                    stats.totalSize += attrs.size();

                    if (file.toString().endsWith(".gz")) {
                        stats.compressedFiles++;
                    }

                    // 计算文件年龄
                    LocalDateTime fileTime = LocalDateTime.ofInstant(
                        attrs.lastModifiedTime().toInstant(), ZoneId.systemDefault());
                    long daysDiff = java.time.Duration.between(fileTime, LocalDateTime.now()).toDays();

                    if (daysDiff > RETENTION_DAYS) {
                        stats.expiredFiles++;
                    } else if (daysDiff > COMPRESSION_AFTER_DAYS) {
                        stats.oldFiles++;
                    }

                    return FileVisitResult.CONTINUE;
                }
            });

        } catch (IOException e) {
            log.error("获取目录统计信息失败", e);
        }

        return stats;
    }

    /**
     * 目录统计信息
     */
    @Getter
    public static class DirectoryStats {
        private int totalFiles = 0;
        private long totalSize = 0;
        private int compressedFiles = 0;
        private int expiredFiles = 0;
        private int oldFiles = 0;

        public String getTotalSizeFormatted() {
            if (totalSize < 1024) return totalSize + " B";
            if (totalSize < 1024 * 1024) return String.format("%.1f KB", totalSize / 1024.0);
            if (totalSize < 1024 * 1024 * 1024) return String.format("%.1f MB", totalSize / (1024.0 * 1024));
            return String.format("%.1f GB", totalSize / (1024.0 * 1024 * 1024));
        }
    }
}
