package org.dromara.waterfee.counter.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 缴费明细视图对象
 *
 * <AUTHOR> Li
 * @date 2025-05-07
 */
@Data
public class WaterfeePaymentDetailVo {

    /**
     * 缴费ID
     */
    private String paymentId;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账单月份
     */
    private String billMonth;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 缴费金额
     */
    private BigDecimal paymentAmount;

    /**
     * 缴费方式
     */
    private String paymentMethod;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 缴费时间
     */
    private Date paymentTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;
}
