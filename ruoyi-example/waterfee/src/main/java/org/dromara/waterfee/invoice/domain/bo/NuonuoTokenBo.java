package org.dromara.waterfee.invoice.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 诺诺发票平台访问令牌业务对象
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NuonuoTokenBo extends BaseEntity {

    /**
     * 令牌ID
     */
    private Long tokenId;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 过期时间点
     */
    private Date expireTime;

    /**
     * 是否有效（0有效 1无效）
     */
    private int isValid;

}
