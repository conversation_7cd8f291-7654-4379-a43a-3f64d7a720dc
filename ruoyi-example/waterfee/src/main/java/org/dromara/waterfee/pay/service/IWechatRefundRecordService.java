package org.dromara.waterfee.pay.service;

import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.waterfee.pay.domain.vo.WechatRefundRecordVo;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 微信退款记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IWechatRefundRecordService {

    /**
     * 查询微信退款记录
     *
     * @param refundId 主键
     * @return 微信退款记录
     */
    WechatRefundRecord queryById(String refundId);

    /**
     * 分页查询微信退款记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 微信退款记录分页列表
     */
    TableDataInfo<WechatRefundRecordVo> queryPageList(WechatRefundRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的微信退款记录列表
     *
     * @param bo 查询条件
     * @return 微信退款记录列表
     */
    List<WechatRefundRecordVo> queryList(WechatRefundRecordBo bo);

    /**
     * 新增微信退款记录
     *
     * @param bo 微信退款记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WechatRefundRecordBo bo);

    /**
     * 修改微信退款记录
     *
     * @param bo 微信退款记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WechatRefundRecordBo bo);

    /**
     * 校验并批量删除微信退款记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
