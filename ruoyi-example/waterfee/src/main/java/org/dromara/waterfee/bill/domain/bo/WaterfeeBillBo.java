package org.dromara.waterfee.bill.domain.bo;

import org.dromara.waterfee.bill.domain.WaterfeeBill;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单管理业务对象 waterfee_bills
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeBill.class, reverseConvertGenerate = false)
public class WaterfeeBillBo extends BaseEntity {

    /**
     * 账单唯一ID
     */
    @NotNull(message = "账单唯一ID不能为空", groups = {EditGroup.class})
    private Long billId;

    /**
     * 账单编号 (业务)
     */
    @NotBlank(message = "账单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String billNumber;

    /**
     * 关联客户ID
     */
    @NotNull(message = "关联客户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long customerId;

    /**
     * 关联水表ID
     */
    @NotNull(message = "关联水表ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long meterId;

    /**
     * 表册ID
     */
    @NotNull(message = "表册ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long meterBookId;

    /**
     * 价格方案ID
     */
    @NotNull(message = "价格方案ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long pricePlanId;

    /**
     * 计费周期开始日期
     */
    @NotNull(message = "计费周期开始日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date billingPeriodStart;

    /**
     * 计费周期结束日期
     */
    @NotNull(message = "计费周期结束日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date billingPeriodEnd;

    /**
     * 账单发行日期
     */
    private Date billingIssueDate;

    /**
     * 账单到期日期
     */
    private Date billingDueDate;

    /**
     * 上期结算读数ID
     */
    private Long previousReadingId;

    /**
     * 本期结算读数ID
     */
    @NotNull(message = "本期结算读数ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long currentReadingId;

    /**
     * 上期读数值
     */
    private BigDecimal previousReadingValue;

    /**
     * 本期读数值
     */
    @NotNull(message = "本期读数值不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal currentReadingValue;

    /**
     * 本期消费量
     */
    @NotNull(message = "本期消费量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal consumptionVolume;

    /**
     * 消费量单位
     */
    private String consumptionUnit;

    /**
     * 标准水费 (标准用量费用)
     */
    private BigDecimal baseChargeAmount;

    /**
     * 调整金额 (正数为增加, 负数为减免)
     */
    private BigDecimal adjustmentsAmount;

    /**
     * 账单总金额 (含调整)
     */
    @NotNull(message = "账单总金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal totalAmount;

    /**
     * 已支付金额
     */
    private BigDecimal amountPaid;

    /**
     * 应付余额 (total_amount - amount_paid)
     */
    @NotNull(message = "应付余额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal balanceDue;

    /**
     * 账单状态
     */
    private String billStatus;

    /**
     * 账单月份(格式：YYYYMM)
     */
    @NotBlank(message = "账单月份不能为空", groups = {AddGroup.class, EditGroup.class})
    private String billMonth;

    /**
     * 账单备注
     */
    private String notes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用于用量调整的字段
     */
    private BigDecimal adjustmentVolume;

    /**
     * 用量调整原因
     */
    private String adjustmentReason;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用水地址
     */
    private String address;

    /**
     * 详情JSON
     */
    private String detailJson;

    /**
     * 水资源税
     */
    private BigDecimal waterResourceTax;

    /**
     * 污水处理费
     */
    private BigDecimal sewageTreatmentFee;

    /**
     * 仅水费，不含其他
     */
    private BigDecimal waterBillOnly;
}
