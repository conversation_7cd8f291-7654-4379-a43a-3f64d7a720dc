package org.dromara.waterfee.complaint.service;

import org.dromara.waterfee.complaint.domain.ComplaintSuggestion;
import org.dromara.waterfee.complaint.domain.vo.ComplaintSuggestionVo;
import org.dromara.waterfee.complaint.domain.bo.ComplaintSuggestionBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 投诉建议Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IComplaintSuggestionService {

    /**
     * 查询投诉建议
     *
     * @param complaintSuggestionId 主键
     * @return 投诉建议
     */
    ComplaintSuggestion queryById(Long complaintSuggestionId);

    /**
     * 分页查询投诉建议列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 投诉建议分页列表
     */
    TableDataInfo<ComplaintSuggestionVo> queryPageList(ComplaintSuggestionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的投诉建议列表
     *
     * @param bo 查询条件
     * @return 投诉建议列表
     */
    List<ComplaintSuggestionVo> queryList(ComplaintSuggestionBo bo);

    /**
     * 新增投诉建议
     *
     * @param bo 投诉建议
     * @return 是否新增成功
     */
    Boolean insertByBo(ComplaintSuggestionBo bo);

    /**
     * 修改投诉建议
     *
     * @param bo 投诉建议
     * @return 是否修改成功
     */
    Boolean updateByBo(ComplaintSuggestionBo bo);

    /**
     * 校验并批量删除投诉建议信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
