package org.dromara.waterfee.invoice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.waterfee.invoice.domain.vo.InvoiceRecordVo;
import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import org.dromara.waterfee.invoice.mapper.InvoiceRecordMapper;
import org.dromara.waterfee.invoice.service.IInvoiceRecordService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 发票记录信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class InvoiceRecordServiceImpl implements IInvoiceRecordService {

    private final InvoiceRecordMapper baseMapper;

    /**
     * 查询发票记录信息
     *
     * @param invoiceId 主键
     * @return 发票记录信息
     */
    @Override
    public InvoiceRecord queryById(Long invoiceId){
        return baseMapper.selectById(invoiceId);
    }

    /**
     * 分页查询发票记录信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 发票记录信息分页列表
     */
    @Override
    public TableDataInfo<InvoiceRecordVo> queryPageList(InvoiceRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InvoiceRecord> lqw = buildQueryWrapper(bo);
        Page<InvoiceRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的发票记录信息列表
     *
     * @param bo 查询条件
     * @return 发票记录信息列表
     */
    @Override
    public List<InvoiceRecordVo> queryList(InvoiceRecordBo bo) {
        LambdaQueryWrapper<InvoiceRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InvoiceRecord> buildQueryWrapper(InvoiceRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InvoiceRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(InvoiceRecord::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getBillNumber()), InvoiceRecord::getBillNumber, bo.getBillNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNo()), InvoiceRecord::getSerialNo, bo.getSerialNo());
        lqw.eq(StringUtils.isNotBlank(bo.getBuyerName()), InvoiceRecord::getBuyerName, bo.getBuyerName());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceKind()), InvoiceRecord::getInvoiceKind, bo.getInvoiceKind());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceStatus()), InvoiceRecord::getInvoiceStatus, bo.getInvoiceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getFailCause()), InvoiceRecord::getFailCause, bo.getFailCause());
        lqw.eq(StringUtils.isNotBlank(bo.getPdfUrl()), InvoiceRecord::getPdfUrl, bo.getPdfUrl());
        lqw.eq(bo.getInvoiceTime() != null, InvoiceRecord::getInvoiceTime, bo.getInvoiceTime());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceCode()), InvoiceRecord::getInvoiceCode, bo.getInvoiceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNo()), InvoiceRecord::getInvoiceNo, bo.getInvoiceNo());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), InvoiceRecord::getInvoiceType, bo.getInvoiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getClerk()), InvoiceRecord::getClerk, bo.getClerk());
        lqw.eq(ObjectUtil.isNotNull(bo.getUserId()), InvoiceRecord::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增发票记录信息
     *
     * @param bo 发票记录信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InvoiceRecordBo bo) {
        InvoiceRecord add = MapstructUtils.convert(bo, InvoiceRecord.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setInvoiceId(add.getInvoiceId());
        }
        return flag;
    }

    /**
     * 修改发票记录信息
     *
     * @param bo 发票记录信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InvoiceRecordBo bo) {
        InvoiceRecord update = MapstructUtils.convert(bo, InvoiceRecord.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InvoiceRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除发票记录信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
