package org.dromara.waterfee.priceManage.mapper;

import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 阶梯价格配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface WaterfeePriceTierMapper extends BaseMapper<WaterfeePriceTier> {

    /**
     * 根据价格配置ID查询阶梯价格列表
     *
     * @param priceConfigId 价格配置ID
     * @return 阶梯价格列表
     */
    List<WaterfeePriceTier> selectTiersByPriceConfigId(Long priceConfigId);
}
