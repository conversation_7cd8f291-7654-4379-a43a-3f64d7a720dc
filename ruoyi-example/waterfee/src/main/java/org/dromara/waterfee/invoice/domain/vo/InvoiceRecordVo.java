package org.dromara.waterfee.invoice.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 发票记录信息视图对象 invoice_record
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InvoiceRecord.class)
public class InvoiceRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID
     */
    @ExcelProperty(value = "发票ID")
    private Long invoiceId;

    /**
     * 关联用户id
     */
    private Long userId;

    /**
     * 关联账单编号
     */
    @ExcelProperty(value = "关联账单编号")
    private String billNumber;

    /**
     * 发票平台请求流水号
     */
    @ExcelProperty(value = "发票平台请求流水号")
    private String serialNo;

    /**
     * 开票状态(process, success, failure)
     */
    @ExcelProperty(value = "开票状态(process, success, failure, cancel)")
    private String invoiceStatus;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String failCause;

    /**
     * 发票pdf地址
     */
    @ExcelProperty(value = "发票pdf地址")
    private String pdfUrl;


    /**
     * 发票ofd地址
     */
    private String ofd_url;

    /**
     * 发票xml地址
     */
    private String xml_url;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方税号 企业必填 个人可为空
     */
    private String buyerTaxNumber;

    /**
     * 是否企业 必填
     */
    private Boolean isEnterprise;

    /**
     * 开票员
     */
    private String clerk;

    /**
     * 开票时间
     */
    @ExcelProperty(value = "开票时间")
    private Date invoiceTime;

    /**
     * 发票代码
     */
    @ExcelProperty(value = "发票代码")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @ExcelProperty(value = "发票号码")
    private String invoiceNo;

    /**
     * 发票类型 1:蓝票;2:红票
     */
    private String invoiceType;

    /**
     * 发票种类
     */
    private String invoiceKind;

    /** 发票种类标识 0普通发票（电票），1数电专票(电子) */
    private String invoiceLine;

    /**
     * 价税合计
     */
    private BigDecimal orderAmount;

    /**
     * 不含税金额
     */
    private BigDecimal exTaxAmount;

    /**
     * 含税金额
     */
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
