package org.dromara.waterfee.invoice.service;

import org.dromara.common.core.domain.R;
import org.dromara.waterfee.invoice.domain.InvoiceCancellationParam;
import org.dromara.waterfee.invoice.domain.QueryInvoiceParam;
import org.dromara.waterfee.invoice.domain.QuickRedInvoiceParam;
import org.dromara.waterfee.invoice.domain.RequestInvoiceParam;

public interface NuonuoInvoiceService {

    String requestInvoice(RequestInvoiceParam requestInvoiceParam);

    R webRequestInvoice(RequestInvoiceParam requestInvoiceParam);

    String queryInvoiceStatus(QueryInvoiceParam queryInvoiceParam);

    String invoiceCancellation(InvoiceCancellationParam invoiceCancellationParam);

    R webInvoiceCancellation(Long invoiceId);

    /**
     * 根据发票流水号手动刷新发票状态
     * @param serialNo 发票流水号
     * @return 结果
     */
    R refreshInvoiceStatusBySerialNo(String serialNo);

    /**
     * 快速红冲
     * @param quickRedInvoiceParam
     * @return
     */
    R quickRedInvoice(QuickRedInvoiceParam quickRedInvoiceParam);

}
