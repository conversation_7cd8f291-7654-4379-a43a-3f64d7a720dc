package org.dromara.waterfee.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.waterfee.pay.domain.vo.WechatRefundRecordVo;
import org.dromara.waterfee.pay.mapper.WechatRefundRecordMapper;
import org.dromara.waterfee.pay.service.IWechatRefundRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 微信退款记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RequiredArgsConstructor
@Service
public class WechatRefundRecordServiceImpl implements IWechatRefundRecordService {

    private final WechatRefundRecordMapper baseMapper;

    /**
     * 查询微信退款记录
     *
     * @param refundId 主键
     * @return 微信退款记录
     */
    @Override
    public WechatRefundRecord queryById(String refundId){
        return baseMapper.selectById(refundId);
    }

    /**
     * 分页查询微信退款记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 微信退款记录分页列表
     */
    @Override
    public TableDataInfo<WechatRefundRecordVo> queryPageList(WechatRefundRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WechatRefundRecord> lqw = buildQueryWrapper(bo);
        Page<WechatRefundRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的微信退款记录列表
     *
     * @param bo 查询条件
     * @return 微信退款记录列表
     */
    @Override
    public List<WechatRefundRecordVo> queryList(WechatRefundRecordBo bo) {
        LambdaQueryWrapper<WechatRefundRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WechatRefundRecord> buildQueryWrapper(WechatRefundRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WechatRefundRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WechatRefundRecord::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentDetailId()), WechatRefundRecord::getPaymentDetailId, bo.getPaymentDetailId());
        lqw.eq(StringUtils.isNotBlank(bo.getWechatRefundId()), WechatRefundRecord::getWechatRefundId, bo.getWechatRefundId());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundStatus()), WechatRefundRecord::getRefundStatus, bo.getRefundStatus());
        lqw.eq(bo.getSuccessTime() != null, WechatRefundRecord::getSuccessTime, bo.getSuccessTime());
        lqw.eq(StringUtils.isNotBlank(bo.getUserReceivedAccount()), WechatRefundRecord::getUserReceivedAccount, bo.getUserReceivedAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionId()), WechatRefundRecord::getTransactionId, bo.getTransactionId());
        lqw.eq(bo.getPaymentAmount() != null, WechatRefundRecord::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(bo.getPaymentRefund() != null, WechatRefundRecord::getPaymentRefund, bo.getPaymentRefund());
        lqw.eq(bo.getPayerAmount() != null, WechatRefundRecord::getPayerAmount, bo.getPayerAmount());
        lqw.eq(bo.getPayerRefund() != null, WechatRefundRecord::getPayerRefund, bo.getPayerRefund());
        return lqw;
    }

    /**
     * 新增微信退款记录
     *
     * @param bo 微信退款记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WechatRefundRecordBo bo) {
        WechatRefundRecord add = MapstructUtils.convert(bo, WechatRefundRecord.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRefundId(add.getRefundId());
        }
        return flag;
    }

    /**
     * 修改微信退款记录
     *
     * @param bo 微信退款记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WechatRefundRecordBo bo) {
        WechatRefundRecord update = MapstructUtils.convert(bo, WechatRefundRecord.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WechatRefundRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除微信退款记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
