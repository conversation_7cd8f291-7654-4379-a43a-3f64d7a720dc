package org.dromara.waterfee.priceManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.community.domain.Community;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeePriceConfigBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceConfigMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceTierMapper;
import org.dromara.waterfee.priceManage.service.IWaterfeePriceConfigService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 阶梯价格配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class WaterfeePriceConfigServiceImpl implements IWaterfeePriceConfigService {

    private final WaterfeePriceConfigMapper baseMapper;
    private final WaterfeePriceTierMapper tierMapper;

    /**
     * 查询阶梯价格配置
     *
     * @param id 主键
     * @return 阶梯价格配置
     */
    @Override
    public WaterfeePriceConfigVo queryById(Long id) {
        WaterfeePriceConfig config = baseMapper.selectById(id);
        WaterfeePriceConfigVo configVo = MapstructUtils.convert(config, WaterfeePriceConfigVo.class);
        if (configVo != null) {
            LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
            tierQuery.eq(WaterfeePriceTier::getPriceConfigId, id);
            List<WaterfeePriceTier> priceTiers = tierMapper.selectList(tierQuery);
            configVo.setPriceTiers(priceTiers);
        }
        return configVo;
    }

    /**
     * 分页查询阶梯价格配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 阶梯价格配置分页列表
     */
    @Override
    public TableDataInfo<WaterfeePriceConfigVo> queryPageList(WaterfeePriceConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = buildQueryWrapper(bo);
        Page<WaterfeePriceConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 设置关联的阶梯价格数据
        result.getRecords().forEach(configVo -> {
            LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
            tierQuery.eq(WaterfeePriceTier::getPriceConfigId, configVo.getId());
            List<WaterfeePriceTier> tiers = tierMapper.selectList(tierQuery);
            configVo.setPriceTiers(tiers);
        });

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的阶梯价格配置列表
     *
     * @param bo 查询条件
     * @return 阶梯价格配置列表
     */
    @Override
    public List<WaterfeePriceConfigVo> queryList(WaterfeePriceConfigBo bo) {
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeePriceConfig> buildQueryWrapper(WaterfeePriceConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeePriceConfig::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), WaterfeePriceConfig::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getWaterUseType()), WaterfeePriceConfig::getWaterUseType, bo.getWaterUseType());
        lqw.eq(StringUtils.isNotBlank(bo.getCalculationMethod()), WaterfeePriceConfig::getCalculationMethod, bo.getCalculationMethod());
        lqw.eq(bo.getIsPopulation() != null, WaterfeePriceConfig::getIsPopulation, bo.getIsPopulation());
        lqw.eq(bo.getPopulationCount() != null, WaterfeePriceConfig::getPopulationCount, bo.getPopulationCount());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), WaterfeePriceConfig::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增阶梯价格配置
     *
     * @param bo 阶梯价格配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeePriceConfigBo bo) {
        WaterfeePriceConfig add = MapstructUtils.convert(bo, WaterfeePriceConfig.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag && bo.getPriceTiers() != null) {
            Long configId = add.getId();
            for (WaterfeePriceTier waterfeePriceTier : bo.getPriceTiers()) {
                waterfeePriceTier.setPriceConfigId(configId);
                tierMapper.insert(waterfeePriceTier);
            }
        }
        return flag;
    }

    /**
     * 修改阶梯价格配置
     *
     * @param bo 阶梯价格配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeePriceConfigBo bo) {
        WaterfeePriceConfig update = MapstructUtils.convert(bo, WaterfeePriceConfig.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        Long configId = bo.getId();
        LambdaQueryWrapper<WaterfeePriceTier> tierQuery = Wrappers.lambdaQuery();
        tierQuery.eq(WaterfeePriceTier::getPriceConfigId, configId);
        tierMapper.delete(tierQuery);
        if (bo.getPriceTiers() != null) {
            for (WaterfeePriceTier waterfeePriceTier : bo.getPriceTiers()) {
                waterfeePriceTier.setPriceConfigId(configId);
                tierMapper.insert(waterfeePriceTier);
            }
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeePriceConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除阶梯价格配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取价格配置下拉选择框数据
     *
     * @return ID和名称的Map集合，key为id，value为name
     */
    public Map<String, String> getSelectPriceConfigMap() {
        // 查询所有可用的阶梯价格
        LambdaQueryWrapper<WaterfeePriceConfig> lqw = Wrappers.lambdaQuery();
        // 如果有状态字段，可以添加状态过滤条件
        lqw.orderByAsc(WaterfeePriceConfig::getId); // 按照ID排序

        List<WaterfeePriceConfig> waterfeePriceConfigList = baseMapper.selectList(lqw);

        // 转换为Map<String, String>格式，key为社区ID，value为社区名称
        Map<String, String> waterfeePriceConfigMap = new HashMap<>(waterfeePriceConfigList.size());
        for (WaterfeePriceConfig waterfPriceConfig : waterfeePriceConfigList) {
            waterfeePriceConfigMap.put(waterfPriceConfig.getId().toString(), waterfPriceConfig.getName());
        }

        return waterfeePriceConfigMap;
    }
}
