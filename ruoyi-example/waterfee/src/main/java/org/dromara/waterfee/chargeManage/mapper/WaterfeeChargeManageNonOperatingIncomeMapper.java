package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageNonOperatingIncomeVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageNonOperatingIncomeBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 营业外收入记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageNonOperatingIncomeMapper extends BaseMapperPlus<WaterfeeChargeManageNonOperatingIncome, WaterfeeChargeManageNonOperatingIncomeVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageNonOperatingIncomeVo>> P selectVoPage(IPage<WaterfeeChargeManageNonOperatingIncome> page, Wrapper<WaterfeeChargeManageNonOperatingIncome> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询营业外收入记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageNonOperatingIncomeVo> queryList(@Param("page") Page<WaterfeeChargeManageNonOperatingIncome> page, @Param("query") WaterfeeChargeManageNonOperatingIncomeBo query);

}
