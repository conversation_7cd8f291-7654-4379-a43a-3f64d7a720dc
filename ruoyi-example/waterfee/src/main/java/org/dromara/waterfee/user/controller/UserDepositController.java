package org.dromara.waterfee.user.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 用户预存款管理控制器
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/user-deposit")
public class UserDepositController extends BaseController {

    private final IWaterfeeUserService waterfeeUserService;

    /**
     * 获取用户余额信息
     */
    @GetMapping("/balance")
    public R<BigDecimal> getUserBalance(@RequestParam Long userId) {
        BigDecimal balance = waterfeeUserService.getUserBalance(userId);
        return R.ok(balance);
    }
}