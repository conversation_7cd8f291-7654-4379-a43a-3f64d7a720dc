package org.dromara.waterfee.user.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.annotation.ExcelRequired;
import org.dromara.common.excel.annotation.ExcelServiceFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.excel.convert.ExcelServiceConvert;

import java.util.Date;

/**
 * 用水用户导入对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class WaterfeeUserImportVo {

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 营业区域id
     */
    @ExcelRequired
    @ExcelProperty(value = "营业区域", converter = ExcelServiceConvert.class)
    @ExcelServiceFormat(serviceClass = "org.dromara.waterfee.area.service.IWaterfeeAreaService", methodName = "getSelectAreaMap", mapMethodName = "getSelectAreaMap")
    @NotBlank(message = "营业区域不能为空")
    private Long areaId;

    /**
     * 小区id
     */
    @ExcelRequired
    @ExcelProperty(value = "小区", converter = ExcelServiceConvert.class)
    @ExcelServiceFormat(serviceClass = "org.dromara.waterfee.community.service.ICommunityService", methodName = "getSelectCommunityMap", mapMethodName = "getSelectCommunityMap")
    @NotBlank(message = "小区不能为空")
    private Long communityId;

    /**
     * 单元房号
     */
    @ExcelRequired
    @ExcelProperty(value = "单元房号")
    @NotBlank(message = "单元房号不能为空")
    private String unitRoomNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelRequired
    @ExcelProperty(value = "客户性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_customer_nature")
    @NotBlank(message = "客户性质不能为空")
    private String customerNature;

    /**
     * 用水性质（字典waterfee_user_use_water_nature）
     */
    @ExcelRequired
    @ExcelProperty(value = "用水性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_use_water_nature")
    @NotBlank(message = "用水性质不能为空")
    private String useWaterNature;

    /**
     * 用水人数
     */
    @ExcelRequired
    @ExcelProperty(value = "用水人数")
    @NotNull(message = "用水人数不能为空")
    private Long useWaterNumber;

    /**
     * 用水户名称
     */
    @ExcelRequired
    @ExcelProperty(value = "用户姓名")
    @NotBlank(message = "用户姓名不能为空")
    @Size(min = 2, max = 20, message = "用户姓名长度必须介于2和20之间")
    private String userName;

    /**
     * 用户手机号
     */
    @ExcelRequired
    @ExcelProperty(value = "手机号码")
    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    @NotBlank(message = "手机号码不能为空")
    private String phoneNumber;

    /**
     * 证件类型
     */
    @ExcelRequired
    @ExcelProperty(value = "证件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_certificate_type")
    @NotBlank(message = "证件类型不能为空")
    private String certificateType;

    /**
     * 证件号码
     */
    @ExcelRequired
    @ExcelProperty(value = "证件号码")
    @NotBlank(message = "证件号码不能为空")
    private String certificateNumber;

    /**
     * 用户状态（字典waterfee_user_user_status）
     */
    @ExcelRequired
    @ExcelProperty(value = "用户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_user_status")
    @NotBlank(message = "用户状态不能为空")
    private String status;

    /**
     * 用水地址
     */
    @ExcelRequired
    @ExcelProperty(value = "用户地址")
    @Size(max = 100, message = "用户地址长度不能超过100个字符")
    @NotBlank(message = "用户地址不能为空")
    private String address;

    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "电子邮箱")
    private String email;

    /**
     * 纳税人识别号
     */
    @ExcelRequired
    @ExcelProperty(value = "纳税人识别号")
    @NotBlank(message = "纳税人识别号不能为空")
    private String taxpayerIdentificationNumber;

    /**
     * 供水日期
     */
    @ExcelRequired
    @ExcelProperty(value = "供水日期")
    @NotNull(message = "供水日期不能为空")
    private Date supplyDate;

    /**
     * 开票名称
     */
    @ExcelProperty(value = "开票名称")
    private String invoiceName;

    /**
     * 发票类型（字典waterfee_user_invoice_type）
     */
    @ExcelRequired
    @ExcelProperty(value = "发票类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_user_invoice_type")
    @NotBlank(message = "发票类型不能为空")
    private String invoiceType;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    @ExcelRequired
//    @ExcelProperty(value = "价格-用水性质", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_use_water_nature")
//    @NotBlank(message = "价格-用水性质不能为空")
//    private String priceUseWaterNature;

    /**
     * 计费方式（字典waterfee_user_billing_method）
     */
    @ExcelRequired
    @ExcelProperty(value = "计费方式", converter = ExcelServiceConvert.class)
    @ExcelServiceFormat(serviceClass = "org.dromara.waterfee.priceManage.service.IWaterfeePriceConfigService", methodName = "getSelectPriceConfigMap", mapMethodName = "getSelectPriceConfigMap")
    @NotBlank(message = "计费方式不能为空")
    private String billingMethod;

//    /**
//     * 是否有违约金（字典yes_no）
//     */
//    @ExcelProperty(value = "是否有违约金", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String ifPenalty;
//
//    /**
//     * 违约金类型（字典waterfee_user_penalty_type）
//     */
//    @ExcelProperty(value = "违约金类型", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_penalty_type")
//    private String penaltyType;
//
//    /**
//     * 是否有附加费（字典yes_no）
//     */
//    @ExcelProperty(value = "是否有附加费", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "yes_no")
//    private String ifExtraCharge;
//
//    /**
//     * 附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
//     */
//    @ExcelProperty(value = "附加费内容", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "waterfee_user_extra_charge_type")
//    private String extraChargeType;

    /**
     * 审核状态（字典audit_status）
     */
//    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "audit_status")
//    private String auditStatus;

}
