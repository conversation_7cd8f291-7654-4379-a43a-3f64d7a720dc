package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdConfigVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdConfigBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageWithholdConfigService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 代扣配置信息
 * 前端访问路由地址为:/waterfee/chargeManageWithholdConfig
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageWithholdConfig")
public class WaterfeeChargeManageWithholdConfigController extends BaseController {

    private final IWaterfeeChargeManageWithholdConfigService waterfeeChargeManageWithholdConfigService;

    /**
     * 查询代扣配置信息列表
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageWithholdConfigVo> list(WaterfeeChargeManageWithholdConfigBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageWithholdConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出代扣配置信息列表
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:export")
    @Log(title = "代扣配置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageWithholdConfigBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageWithholdConfigVo> list = waterfeeChargeManageWithholdConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "代扣配置信息", WaterfeeChargeManageWithholdConfigVo.class, response);
    }

    /**
     * 获取代扣配置信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageWithholdConfig> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageWithholdConfigService.queryById(id));
    }

    /**
     * 新增代扣配置信息
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:add")
    @Log(title = "代扣配置信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageWithholdConfigBo bo) {
        return toAjax(waterfeeChargeManageWithholdConfigService.insertByBo(bo));
    }

    /**
     * 修改代扣配置信息
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:edit")
    @Log(title = "代扣配置信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageWithholdConfigBo bo) {
        return toAjax(waterfeeChargeManageWithholdConfigService.updateByBo(bo));
    }

    /**
     * 删除代扣配置信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageWithholdConfig:remove")
    @Log(title = "代扣配置信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageWithholdConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
