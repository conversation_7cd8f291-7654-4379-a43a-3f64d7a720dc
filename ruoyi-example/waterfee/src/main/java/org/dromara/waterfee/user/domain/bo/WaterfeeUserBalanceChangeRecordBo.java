package org.dromara.waterfee.user.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.user.domain.WaterfeeUserBalanceChangeRecord;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户余额变更记录业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeUserBalanceChangeRecord.class, reverseConvertGenerate = false)
public class WaterfeeUserBalanceChangeRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 变更前余额
     */
    private BigDecimal beforeBalance;

    /**
     * 变更金额
     */
    private BigDecimal changeAmount;

    /**
     * 变更后余额
     */
    private BigDecimal afterBalance;

    /**
     * 关联业务ID
     */
    private String businessId;

    /**
     * 关联业务类型
     */
    private String businessType;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 变更时间
     */
    private Date changeTime;

    /**
     * 备注
     */
    private String remark;
}