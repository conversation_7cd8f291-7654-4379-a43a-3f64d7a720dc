package org.dromara.waterfee.meter.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.meter.domain.MeterChangeRecord;
import org.dromara.waterfee.meter.domain.bo.MeterChangeRecordBo;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.mapper.MeterChangeRecordMapper;
import org.dromara.waterfee.meter.service.IMeterChangeRecordService;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表具变更记录Service业务层处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MeterChangeRecordServiceImpl extends ServiceImpl<MeterChangeRecordMapper, MeterChangeRecord> implements IMeterChangeRecordService {

    private final JdbcTemplate jdbcTemplate;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final IMeterReadingRecordService meterReadingRecordService;

    @Override
    public MeterChangeRecordVo queryById(Long changeId) {
        MeterChangeRecordVo vo = baseMapper.selectVoById(changeId);
        if (vo != null) {
            enrichChangeRecordInfo(Collections.singletonList(vo));
        }
        return vo;
    }

    @Override
    public TableDataInfo<MeterChangeRecordVo> queryPageList(MeterChangeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MeterChangeRecord> lqw = buildQueryWrapper(bo);
        Page<MeterChangeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        enrichChangeRecordInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    @Override
    public List<MeterChangeRecordVo> queryList(MeterChangeRecordBo bo) {
        LambdaQueryWrapper<MeterChangeRecord> lqw = buildQueryWrapper(bo);
        List<MeterChangeRecordVo> list = baseMapper.selectVoList(lqw);
        enrichChangeRecordInfo(list);
        return list;
    }

    /**
     * 补充变更记录相关信息
     */
    private void enrichChangeRecordInfo(List<MeterChangeRecordVo> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        try {
            // 收集所有需要查询的表号
            Set<String> oldMeterNos = records.stream()
                .map(MeterChangeRecordVo::getOldMeterNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
            Set<String> newMeterNos = records.stream()
                .map(MeterChangeRecordVo::getNewMeterNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

            // 查询水表信息
            String meterSql = "SELECT m.meter_no, " +
                "u.user_no, u.user_name " +
                "FROM waterfee_meter m " +
                "LEFT JOIN waterfee_user u ON m.user_id = u.user_id " +
                "WHERE m.meter_no IN ('" + String.join("','", oldMeterNos) + "','" +
                String.join("','", newMeterNos) + "') " +
                "AND m.tenant_id = ? AND m.del_flag = '0'";

            Map<String, Map<String, Object>> meterInfoMap = new HashMap<>();
            jdbcTemplate.queryForList(meterSql, LoginHelper.getTenantId()).forEach(row -> {
                meterInfoMap.put((String) row.get("meter_no"), row);
            });

            // 查询操作人信息
            Set<String> operatorIds = records.stream()
                .map(MeterChangeRecordVo::getOperator)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

            String userSql = "SELECT user_id, nick_name FROM sys_user " +
                "WHERE user_id IN ('" + String.join("','", operatorIds) + "') " +
                "AND tenant_id = ? AND del_flag = '0'";

            Map<String, String> operatorMap = new HashMap<>();
            jdbcTemplate.queryForList(userSql, LoginHelper.getTenantId()).forEach(row -> {
                operatorMap.put(row.get("user_id").toString(), (String) row.get("nick_name"));
            });

            // 补充信息
            for (MeterChangeRecordVo record : records) {
                // 补充旧表信息
                if (StringUtils.isNotBlank(record.getOldMeterNo())) {
                    Map<String, Object> oldMeterInfo = meterInfoMap.get(record.getOldMeterNo());
                    if (oldMeterInfo != null) {
                        record.setUserNo((String) oldMeterInfo.get("user_no"));
                        record.setUserName((String) oldMeterInfo.get("user_name"));
                    }
                }

                // 补充操作人显示名称
                if (StringUtils.isNotBlank(record.getOperator())) {
                    String operatorName = operatorMap.get(record.getOperator());
                    if (operatorName != null) {
                        record.setOperator(operatorName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("补充变更记录信息失败", e);
        }
    }

    private LambdaQueryWrapper<MeterChangeRecord> buildQueryWrapper(MeterChangeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MeterChangeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, MeterChangeRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getChangeType() != null, MeterChangeRecord::getChangeType, bo.getChangeType());
        lqw.eq(bo.getChangeReason() != null, MeterChangeRecord::getChangeReason, bo.getChangeReason());
        lqw.eq(StringUtils.isNotBlank(bo.getOldMeterNo()), MeterChangeRecord::getOldMeterNo, bo.getOldMeterNo());
        lqw.eq(StringUtils.isNotBlank(bo.getNewMeterNo()), MeterChangeRecord::getNewMeterNo, bo.getNewMeterNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), MeterChangeRecord::getAuditStatus, bo.getAuditStatus());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MeterChangeRecordBo bo) {
        // 1. 基础验证
        if (bo.getChangeType() != 2) { // 2表示换表
            return super.save(MapstructUtils.convert(bo, MeterChangeRecord.class));
        }

        // 2. 获取旧表信息
        WaterfeeMeterVo oldMeter = waterfeeMeterService.queryByNo(bo.getOldMeterNo());
        if (oldMeter == null) {
            throw new ServiceException("旧表信息不存在");
        }

        try {
            // 3. 创建新表记录
            WaterfeeMeterBo newMeter = new WaterfeeMeterBo();
            // 复制旧表的基础信息（保留原有业务属性）
            BeanUtils.copyProperties(oldMeter, newMeter, "meterId", "meterNo", "initialReading",
                "installDate", "createBy", "createTime", "createDept", "meterType", "manufacturer",
                "caliber", "accuracy", "installAddress", "communicationMode", "valveControl",
                "imei", "imsi");

            // 设置新表信息
            newMeter.setMeterNo(bo.getNewMeterNo());
            newMeter.setInitialReading(new BigDecimal(String.valueOf(bo.getNewMeterReading())));
            newMeter.setInstallDate(new Date());
            newMeter.setCreateBy(LoginHelper.getUserId());
            newMeter.setCreateTime(DateUtils.getNowDate());
            newMeter.setCreateDept(LoginHelper.getDeptId());

            // 从Bo中获取新表特性
            newMeter.setMeterType(bo.getMeterType());
            newMeter.setManufacturer(bo.getManufacturer());
            newMeter.setCaliber(bo.getCaliber());
            newMeter.setAccuracy(bo.getAccuracy());
            newMeter.setInstallAddress(bo.getInstallAddress());
            newMeter.setCommunicationMode(bo.getCommunicationMode());
            newMeter.setValveControl(bo.getValveControl());
            newMeter.setImei(bo.getImei());
            newMeter.setImsi(bo.getImsi());

            // 4. 保存新表记录
            boolean newMeterResult = waterfeeMeterService.insertByBo(newMeter);
            if (!newMeterResult) {
                throw new ServiceException("新增水表记录失败");
            }

            // 5. 标记旧表为已更换（del_flag = 2）
            WaterfeeMeterBo oldMeterUpdate = new WaterfeeMeterBo();
            oldMeterUpdate.setMeterId(oldMeter.getMeterId());
            oldMeterUpdate.setDelFlag("2"); // 2表示已更换
            boolean oldMeterResult = waterfeeMeterService.updateByBo(oldMeterUpdate);
            if (!oldMeterResult) {
                throw new ServiceException("更新旧表状态失败");
            }

            // 6. 查询旧表最新抄表记录
            WaterfeeMeterReadingRecord latestRecord = meterReadingRecordService.queryLatestEntityByMeterNo(bo.getOldMeterNo());
            if (latestRecord == null) {
                throw new ServiceException("未找到旧表抄表记录");
            }

            // 7. 创建新表的首条抄表记录
            WaterfeeMeterReadingRecord newRecord = new WaterfeeMeterReadingRecord();
            newRecord.setMeterNo(bo.getNewMeterNo());
            newRecord.setMeterType(bo.getMeterType().toString());
            newRecord.setMeterBookId(oldMeter.getMeterBookId());
            // 设置上期抄表信息：上期读数 = 旧表最新记录的本期读数 - 新表起始读数
            newRecord.setLastReading(latestRecord.getCurrentReading() - bo.getNewMeterReading());
            newRecord.setLastReadingTime(latestRecord.getReadingTime());
            // 设置本期抄表信息
            newRecord.setCurrentReading(bo.getNewMeterReading());
            newRecord.setReadingTime(new Date());
            newRecord.setSourceType("normal"); // 来源为换表
            newRecord.setOldMeterStopReading(0.0);
            newRecord.setWaterUsage(null); // 让数据库触发器计算水量

            // 8. 保存新表抄表记录
            boolean readingResult = meterReadingRecordService.insertByEntity(newRecord);
            if (!readingResult) {
                throw new ServiceException("新增抄表记录失败");
            }

            // 9. 保存换表记录
            MeterChangeRecord add = MapstructUtils.convert(bo, MeterChangeRecord.class);
            add.setChangeTime(new Date());
            add.setOperator(LoginHelper.getUserId().toString());
            add.setAuditStatus("0"); // 待审核
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setChangeId(add.getChangeId());
            }
            return flag;

        } catch (Exception e) {
            log.error("换表操作失败", e);
            throw new ServiceException("换表操作失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MeterChangeRecordBo bo) {
        MeterChangeRecord update = MapstructUtils.convert(bo, MeterChangeRecord.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditRecord(Long changeId) {
        MeterChangeRecord record = baseMapper.selectById(changeId);
        if (record != null) {
            record.setAuditStatus("1"); // 已审核
            record.setAuditTime(new Date());
            record.setAuditor(LoginHelper.getUserId().toString());
            return baseMapper.updateById(record) > 0;
        }
        return false;
    }

    @Override
    public List<MeterChangeRecordVo> queryByMeterNo(String meterNo) {
        LambdaQueryWrapper<MeterChangeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(meterNo), MeterChangeRecord::getOldMeterNo, meterNo)
            .or()
            .eq(StringUtils.isNotBlank(meterNo), MeterChangeRecord::getNewMeterNo, meterNo);
        lqw.orderByDesc(MeterChangeRecord::getChangeTime);

        List<MeterChangeRecordVo> list = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(list)) {
            enrichChangeRecordInfo(list);
        }
        return list;
    }
}
