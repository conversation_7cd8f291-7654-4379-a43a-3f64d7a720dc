package org.dromara.waterfee.complaint.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.complaint.domain.ComplaintSuggestion;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 投诉建议视图对象 complaint_suggestion
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ComplaintSuggestion.class)
public class ComplaintSuggestionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long complaintSuggestionId;

    /**
     * 提交人姓名
     */
    @ExcelProperty(value = "提交人姓名")
    private String submitterName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 投诉内容
     */
    @ExcelProperty(value = "投诉内容")
    private String content;

    /**
     * 投诉时间
     */
    @ExcelProperty(value = "投诉时间")
    private Date submitTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
