package org.dromara.waterfee.wechat.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.wechat.domain.bo.WechatDraftBo;
import org.dromara.waterfee.wechat.domain.vo.WechatDraftVo;

import java.util.Collection;
import java.util.List;

/**
 * 微信草稿服务接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IWechatDraftService {

    /**
     * 查询微信草稿
     *
     * @param draftId 微信草稿主键
     * @return 微信草稿
     */
    WechatDraftVo queryById(Long draftId);

    /**
     * 查询微信草稿列表
     *
     * @param bo 微信草稿
     * @return 微信草稿集合
     */
    TableDataInfo<WechatDraftVo> queryPageList(WechatDraftBo bo, PageQuery pageQuery);

    /**
     * 查询微信草稿列表
     *
     * @param bo 微信草稿
     * @return 微信草稿集合
     */
    List<WechatDraftVo> queryList(WechatDraftBo bo);

    /**
     * 新增微信草稿
     *
     * @param bo 微信草稿
     * @return 是否新增成功
     */
    Boolean insertByBo(WechatDraftBo bo);

    /**
     * 新增微信草稿并同时发布到微信
     *
     * @param bo 微信草稿
     * @return 是否新增并发布成功
     */
    Boolean insertAndPublishByBo(WechatDraftBo bo);

    /**
     * 修改微信草稿
     *
     * @param bo 微信草稿
     * @return 是否修改成功
     */
    Boolean updateByBo(WechatDraftBo bo);

    /**
     * 修改微信草稿并同步更新微信端草稿
     *
     * @param bo 微信草稿
     * @return 是否修改成功
     */
    Boolean updateAndSyncByBo(WechatDraftBo bo);

    /**
     * 校验并批量删除微信草稿信息
     *
     * @param ids 主键集合
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 发布草稿到微信公众号（创建草稿）
     *
     * @param draftId 草稿ID
     * @return 是否发布成功
     */
    Boolean publishDraft(Long draftId);

    /**
     * 正式发布草稿为订阅号消息推文
     *
     * @param draftId 草稿ID
     * @return 是否发布成功
     */
    Boolean publishArticle(Long draftId);

    /**
     * 创建微信草稿
     *
     * @param bo 微信草稿
     * @return 微信草稿媒体ID
     */
    String createWechatDraft(WechatDraftBo bo);

    /**
     * 删除微信草稿
     *
     * @param mediaId 微信草稿媒体ID
     * @return 是否删除成功
     */
    Boolean deleteWechatDraft(String mediaId);

    /**
     * 更新微信草稿
     *
     * @param mediaId 微信草稿媒体ID
     * @param bo 微信草稿信息
     * @return 新的微信草稿媒体ID
     */
    String updateWechatDraft(String mediaId, WechatDraftBo bo);

    /**
     * 获取微信草稿列表
     *
     * @param offset 偏移量
     * @param count 数量
     * @return 微信草稿列表
     */
    String getWechatDraftList(Integer offset, Integer count);

    /**
     * 删除发布的文章（同时删除本地草稿和微信端文章）
     *
     * @param draftId 草稿ID
     * @return 是否删除成功
     */
    Boolean deletePublishedArticle(Long draftId);
}
