package org.dromara.waterfee.user.domain.vo;

import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.waterfee.user.domain.WaterfeeUser;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 合收户管理视图对象 consolidated_account
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ConsolidatedAccount.class)
public class ConsolidatedAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long consolidatedAccountId;

    /**
     * 合收户编号
     */
    @ExcelProperty(value = "合收户编号")
    private String consolidatedAccountNo;

    /**
     * 合收户名
     */
    @ExcelProperty(value = "合收户名")
    private String consolidatedAccountName;

    /**
     * 户主姓名
     */
    @ExcelProperty(value = "户主姓名")
    private String ownerName;

    /**
     * 户主电话
     */
    @ExcelProperty(value = "户主电话")
    private String ownerPhone;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 合收户用户列表
     */
    private List<WaterfeeUser> userList;


}
