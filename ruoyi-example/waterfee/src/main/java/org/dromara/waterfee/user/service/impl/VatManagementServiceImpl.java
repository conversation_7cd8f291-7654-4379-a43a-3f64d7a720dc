package org.dromara.waterfee.user.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.VatManagementBo;
import org.dromara.waterfee.user.domain.vo.VatManagementVo;
import org.dromara.waterfee.user.domain.VatManagement;
import org.dromara.waterfee.user.mapper.VatManagementMapper;
import org.dromara.waterfee.user.service.IVatManagementService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 增值税管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class VatManagementServiceImpl implements IVatManagementService {

    private final VatManagementMapper baseMapper;

    /**
     * 查询增值税管理
     *
     * @param id 主键
     * @return 增值税管理
     */
    @Override
    public VatManagement queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询增值税管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 增值税管理分页列表
     */
    @Override
    public TableDataInfo<VatManagementVo> queryPageList(VatManagementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<VatManagement> lqw = buildQueryWrapper(bo);
        Page<VatManagementVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的增值税管理列表
     *
     * @param bo 查询条件
     * @return 增值税管理列表
     */
    @Override
    public List<VatManagementVo> queryList(VatManagementBo bo) {
        LambdaQueryWrapper<VatManagement> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VatManagement> buildQueryWrapper(VatManagementBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<VatManagement> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(VatManagement::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getBillNumber()), VatManagement::getBillNumber, bo.getBillNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceType()), VatManagement::getInvoiceType, bo.getInvoiceType());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceCode()), VatManagement::getInvoiceCode, bo.getInvoiceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), VatManagement::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceStatus()), VatManagement::getInvoiceStatus, bo.getInvoiceStatus());
        lqw.eq(bo.getTotalAmount() != null, VatManagement::getTotalAmount, bo.getTotalAmount());
        lqw.eq(bo.getTaxExclusiveAmount() != null, VatManagement::getTaxExclusiveAmount, bo.getTaxExclusiveAmount());
        lqw.eq(bo.getTaxRate() != null, VatManagement::getTaxRate, bo.getTaxRate());
        lqw.eq(bo.getTaxAmount() != null, VatManagement::getTaxAmount, bo.getTaxAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getIsTaxIncluded()), VatManagement::getIsTaxIncluded, bo.getIsTaxIncluded());
        lqw.like(StringUtils.isNotBlank(bo.getBuyerName()), VatManagement::getBuyerName, bo.getBuyerName());
        lqw.eq(StringUtils.isNotBlank(bo.getBuyerTaxId()), VatManagement::getBuyerTaxId, bo.getBuyerTaxId());
        lqw.eq(StringUtils.isNotBlank(bo.getBuyerAddress()), VatManagement::getBuyerAddress, bo.getBuyerAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getBuyerTel()), VatManagement::getBuyerTel, bo.getBuyerTel());
        lqw.eq(StringUtils.isNotBlank(bo.getBuyerBankAccount()), VatManagement::getBuyerBankAccount, bo.getBuyerBankAccount());
        lqw.like(StringUtils.isNotBlank(bo.getSellerName()), VatManagement::getSellerName, bo.getSellerName());
        lqw.eq(StringUtils.isNotBlank(bo.getSellerTaxId()), VatManagement::getSellerTaxId, bo.getSellerTaxId());
        lqw.eq(StringUtils.isNotBlank(bo.getSellerTel()), VatManagement::getSellerTel, bo.getSellerTel());
        lqw.eq(StringUtils.isNotBlank(bo.getSellerAddress()), VatManagement::getSellerAddress, bo.getSellerAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getSellerBankAccount()), VatManagement::getSellerBankAccount, bo.getSellerBankAccount());
        lqw.eq(bo.getIssueTime() != null, VatManagement::getIssueTime, bo.getIssueTime());
        lqw.eq(bo.getRedFlushTime() != null, VatManagement::getRedFlushTime, bo.getRedFlushTime());
        lqw.eq(bo.getCancelTime() != null, VatManagement::getCancelTime, bo.getCancelTime());
        lqw.eq(bo.getOperatorId() != null, VatManagement::getOperatorId, bo.getOperatorId());
        lqw.eq(StringUtils.isNotBlank(bo.getRedFlushReason()), VatManagement::getRedFlushReason, bo.getRedFlushReason());
        lqw.eq(StringUtils.isNotBlank(bo.getCancelReason()), VatManagement::getCancelReason, bo.getCancelReason());
        return lqw;
    }

    /**
     * 新增增值税管理
     *
     * @param bo 增值税管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(VatManagementBo bo) {
        VatManagement add = MapstructUtils.convert(bo, VatManagement.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改增值税管理
     *
     * @param bo 增值税管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(VatManagementBo bo) {
        VatManagement update = MapstructUtils.convert(bo, VatManagement.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VatManagement entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除增值税管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
