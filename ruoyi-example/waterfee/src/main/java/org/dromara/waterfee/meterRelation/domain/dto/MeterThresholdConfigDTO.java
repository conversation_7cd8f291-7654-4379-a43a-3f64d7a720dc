package org.dromara.waterfee.meterRelation.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:25
 **/

@Data
@Schema(description = "总分表阈值配置DTO")
public class MeterThresholdConfigDTO {

    private Long parentMeterId;

    private Long childMeterId;

    private BigDecimal threshold;
}
