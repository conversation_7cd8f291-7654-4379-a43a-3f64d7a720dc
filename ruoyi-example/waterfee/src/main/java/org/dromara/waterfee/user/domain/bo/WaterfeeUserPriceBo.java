package org.dromara.waterfee.user.domain.bo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;

@Data
public class WaterfeeUserPriceBo {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 价格-用水性质（字典waterfee_user_use_water_nature）
     */
    @NotBlank(message = "价格-用水性质（字典waterfee_user_use_water_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String priceUseWaterNature;

    /**
     * 计费方式（字典waterfee_user_billing_method）
     */
    @NotBlank(message = "计费方式（字典waterfee_user_billing_method）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billingMethod;

    /**
     * 是否有违约金（字典yes_no）
     */
    private String ifPenalty;

    /**
     * 违约金类型（字典waterfee_user_penalty_type）
     */
    private String penaltyType;

    /**
     * 是否有附加费（字典yes_no）
     */
    private String ifExtraCharge;

    /**
     * 附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
    private String extraChargeType;
}
