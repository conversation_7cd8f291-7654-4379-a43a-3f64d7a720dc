package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdConfigBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdConfigVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageWithholdConfigMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageWithholdConfigService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 代扣配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageWithholdConfigServiceImpl implements IWaterfeeChargeManageWithholdConfigService {

    private final WaterfeeChargeManageWithholdConfigMapper baseMapper;

    /**
     * 查询代扣配置信息
     *
     * @param id 主键
     * @return 代扣配置信息
     */
    @Override
    public WaterfeeChargeManageWithholdConfig queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询代扣配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代扣配置信息分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageWithholdConfigVo> queryPageList(WaterfeeChargeManageWithholdConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageWithholdConfig> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageWithholdConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代扣配置信息列表
     *
     * @param bo 查询条件
     * @return 代扣配置信息列表
     */
    @Override
    public List<WaterfeeChargeManageWithholdConfigVo> queryList(WaterfeeChargeManageWithholdConfigBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageWithholdConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageWithholdConfig> buildQueryWrapper(WaterfeeChargeManageWithholdConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageWithholdConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageWithholdConfig::getCreateTime);
        lqw.eq(bo.getUserId() != null, WaterfeeChargeManageWithholdConfig::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getBankAccount()), WaterfeeChargeManageWithholdConfig::getBankAccount, bo.getBankAccount());
        lqw.like(StringUtils.isNotBlank(bo.getBankName()), WaterfeeChargeManageWithholdConfig::getBankName, bo.getBankName());
        lqw.eq(bo.getSigned() != null, WaterfeeChargeManageWithholdConfig::getSigned, bo.getSigned());
        lqw.eq(bo.getSignTime() != null, WaterfeeChargeManageWithholdConfig::getSignTime, bo.getSignTime());
        lqw.eq(bo.getCancelTime() != null, WaterfeeChargeManageWithholdConfig::getCancelTime, bo.getCancelTime());
        return lqw;
    }

    /**
     * 新增代扣配置信息
     *
     * @param bo 代扣配置信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageWithholdConfigBo bo) {
        WaterfeeChargeManageWithholdConfig add = MapstructUtils.convert(bo, WaterfeeChargeManageWithholdConfig.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改代扣配置信息
     *
     * @param bo 代扣配置信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageWithholdConfigBo bo) {
        WaterfeeChargeManageWithholdConfig update = MapstructUtils.convert(bo, WaterfeeChargeManageWithholdConfig.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageWithholdConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代扣配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
