package org.dromara.waterfee.user.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.user.domain.WaterfeeUserBalanceChangeRecord;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户余额变更记录视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeUserBalanceChangeRecord.class)
public class WaterfeeUserBalanceChangeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 变更类型
     */
    @ExcelProperty(value = "变更类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_balance_change_type")
    private String changeType;

    /**
     * 变更前余额
     */
    @ExcelProperty(value = "变更前余额")
    private BigDecimal beforeBalance;

    /**
     * 变更金额
     */
    @ExcelProperty(value = "变更金额")
    private BigDecimal changeAmount;

    /**
     * 变更后余额
     */
    @ExcelProperty(value = "变更后余额")
    private BigDecimal afterBalance;

    /**
     * 关联业务ID
     */
    @ExcelProperty(value = "关联业务ID")
    private String businessId;

    /**
     * 关联业务类型
     */
    @ExcelProperty(value = "关联业务类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_balance_business_type")
    private String businessType;

    /**
     * 变更原因
     */
    @ExcelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;

    /**
     * 变更时间
     */
    @ExcelProperty(value = "变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date changeTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}