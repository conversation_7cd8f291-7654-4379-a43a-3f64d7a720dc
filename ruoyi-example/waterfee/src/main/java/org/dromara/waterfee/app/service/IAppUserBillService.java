package org.dromara.waterfee.app.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.dromara.waterfee.app.domain.bo.AppUserBillConsumptionBo;
import org.dromara.waterfee.app.domain.bo.AppUserBillDetailBo;
import org.dromara.waterfee.app.domain.vo.AppUserBillConsumptionVo;
import org.dromara.waterfee.app.domain.vo.AppUserBillDetailVo;

import java.util.List;

/**
 * App用户账单服务接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IAppUserBillService {

    /**
     * 根据用户ID和年份获取用水量信息
     *
     * @param bo 查询业务对象
     * @return 用水量信息列表
     */
    List<AppUserBillConsumptionVo> getUserConsumption(AppUserBillConsumptionBo bo);

    /**
     * 根据用户编号和年月获取详细账单信息
     *
     * @param bo 查询业务对象
     * @return 详细账单信息
     */
    AppUserBillDetailVo getUserBillDetail(AppUserBillDetailBo bo) throws JsonProcessingException;
}
