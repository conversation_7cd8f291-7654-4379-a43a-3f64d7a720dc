package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;

import java.math.BigDecimal;
import java.util.List;

/**
 * 缴费业务对象
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
public class WaterfeePayBillsBo {

    /**
     * 账单ID列表
     */
    @NotEmpty(message = "账单ID不能为空", groups = {AddGroup.class})
    private List<Long> billIds;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class})
    @PositiveOrZero(message = "支付金额必须>=0", groups = {AddGroup.class})
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @NotNull(message = "支付方式不能为空", groups = {AddGroup.class})
    private String paymentMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收费员
     */
    private String tollCollector;

}
