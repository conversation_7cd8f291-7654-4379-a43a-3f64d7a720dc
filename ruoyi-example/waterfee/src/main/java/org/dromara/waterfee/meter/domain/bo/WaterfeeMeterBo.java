package org.dromara.waterfee.meter.domain.bo;

import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 水表信息业务对象 waterfee_meter
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeMeter.class, reverseConvertGenerate = false)
public class WaterfeeMeterBo extends BaseEntity {

    /**
     * 水表ID
     */
    @NotNull(message = "水表ID不能为空", groups = {EditGroup.class})
    private Long meterId;

    /**
     * 水表编号
     */
    @NotBlank(message = "水表编号不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 0, max = 64, message = "水表编号长度不能超过64个字符")
    private String meterNo;

    /**
     * 水表名称
     */
//    @NotBlank(message = "水表名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 0, max = 100, message = "水表名称长度不能超过100个字符")
    private String meterName;

    /**
     * 水表类型(1-机械表 2-智能表)
     */
    @NotNull(message = "水表类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer meterType;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 水表类别(自来水表、中水表、直饮水表)
     */
    private String meterCategory;

    /**
     * 水表分类(总表、分表)
     */
    private String meterClassification;

    /**
     * 计量用途(居民用水、非居民用水)
     */
    private String measurementPurpose;

    /**
     * 口径
     */
    private String caliber;

    /**
     * 精度
     */
    private String accuracy;

    /**
     * 初始读数
     */
    private BigDecimal initialReading;

    /**
     * 安装日期
     */
    private Date installDate;

    /**
     * 业务区域ID
     */
    private Long businessAreaId;

    /**
     * 抄表手册ID
     */
    private Long meterBookId;

    /**
     * 安装地址
     */
    @Size(min = 0, max = 200, message = "安装地址长度不能超过200个字符")
    private String installAddress;

    /**
     * 表倍率
     */
    private BigDecimal meterRatio;

    /**
     * 通讯方式(NB-IoT、4G、Cat.1、LoRa)
     */
    private String communicationMode;

    /**
     * 阀控功能(0-无 1-有)
     */
    private Integer valveControl;

    /**
     * IMEI号
     */
    private String imei;

    /**
     * IMSI号
     */
    private String imsi;

    /**
     * 物联网平台
     */
    private String iotPlatform;

    /**
     * 是否预付费(0-否 1-是)
     */
    private Integer prepaid;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 关联用户编号
     */
    private String userNo;

    /**
     * 安装日期起始时间
     */
    private Date installDateStart;

    /**
     * 安装日期结束时间
     */
    private Date installDateEnd;

    /**
     * 口径模糊查询
     */
    private String caliberLike;

    /**
     * IMEI号模糊查询
     */
    private String imeiLike;

    /**
     * IMSI号模糊查询
     */
    private String imsiLike;

    /**
     * 安装地址模糊查询
     */
    private String installAddressLike;

    /**
     * 厂商模糊查询
     */
    private String manufacturerLike;

    /**
     * 水表编号模糊查询
     */
    private String meterNoLike;

    /**
     * 精度模糊查询
     */
    private String accuracyLike;

    /**
     * 物联网平台模糊查询
     */
    private String iotPlatformLike;

    /**
     * 用户编号模糊查询
     */
    private String userNoLike;

    /**
     * 通讯方式模糊查询
     */
    private String communicationModeLike;

    /**
     * 初始读数范围开始
     */
    private BigDecimal initialReadingStart;

    /**
     * 初始读数范围结束
     */
    private BigDecimal initialReadingEnd;

    /**
     * 表倍率范围开始
     */
    private BigDecimal meterRatioStart;

    /**
     * 表倍率范围结束
     */
    private BigDecimal meterRatioEnd;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 备注信息
     */
    private String remark;
}
