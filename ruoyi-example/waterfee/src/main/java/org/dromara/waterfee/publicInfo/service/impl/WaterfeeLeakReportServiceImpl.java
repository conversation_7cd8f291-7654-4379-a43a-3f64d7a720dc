package org.dromara.waterfee.leak.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.leak.domain.bo.WaterfeeLeakReportBo;
import org.dromara.waterfee.leak.domain.vo.WaterfeeLeakReportVo;
import org.dromara.waterfee.leak.domain.WaterfeeLeakReport;
import org.dromara.waterfee.leak.mapper.WaterfeeLeakReportMapper;
import org.dromara.waterfee.leak.service.IWaterfeeLeakReportService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 偷漏水举报Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RequiredArgsConstructor
@Service
public class WaterfeeLeakReportServiceImpl implements IWaterfeeLeakReportService {

    private final WaterfeeLeakReportMapper baseMapper;

    /**
     * 查询偷漏水举报
     *
     * @param reportId 主键
     * @return 偷漏水举报
     */
    @Override
    public WaterfeeLeakReport queryById(Long reportId){
        return baseMapper.selectById(reportId);
    }

    /**
     * 分页查询偷漏水举报列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 偷漏水举报分页列表
     */
    @Override
    public TableDataInfo<WaterfeeLeakReportVo> queryPageList(WaterfeeLeakReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeLeakReport> lqw = buildQueryWrapper(bo);
        Page<WaterfeeLeakReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的偷漏水举报列表
     *
     * @param bo 查询条件
     * @return 偷漏水举报列表
     */
    @Override
    public List<WaterfeeLeakReportVo> queryList(WaterfeeLeakReportBo bo) {
        LambdaQueryWrapper<WaterfeeLeakReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeLeakReport> buildQueryWrapper(WaterfeeLeakReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeLeakReport> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeLeakReport::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getReporterName()), WaterfeeLeakReport::getReporterName, bo.getReporterName());
        lqw.eq(StringUtils.isNotBlank(bo.getReporterPhone()), WaterfeeLeakReport::getReporterPhone, bo.getReporterPhone());
        lqw.eq(bo.getReportTime() != null, WaterfeeLeakReport::getReportTime, bo.getReportTime());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), WaterfeeLeakReport::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增偷漏水举报
     *
     * @param bo 偷漏水举报
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeLeakReportBo bo) {
        WaterfeeLeakReport add = MapstructUtils.convert(bo, WaterfeeLeakReport.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setReportId(add.getReportId());
        }
        return flag;
    }

    /**
     * 修改偷漏水举报
     *
     * @param bo 偷漏水举报
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeLeakReportBo bo) {
        WaterfeeLeakReport update = MapstructUtils.convert(bo, WaterfeeLeakReport.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeLeakReport entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除偷漏水举报信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
