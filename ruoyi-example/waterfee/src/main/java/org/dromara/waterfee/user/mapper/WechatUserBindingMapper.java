package org.dromara.waterfee.user.mapper;

import org.dromara.waterfee.user.domain.WechatUserBinding;
import org.dromara.waterfee.user.domain.vo.WechatUserBindingVo;
import org.dromara.waterfee.user.domain.bo.WechatUserBindingBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信账号绑定户号关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface WechatUserBindingMapper extends BaseMapperPlus<WechatUserBinding, WechatUserBindingVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WechatUserBindingVo>> P selectVoPage(IPage<WechatUserBinding> page, Wrapper<WechatUserBinding> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询微信账号绑定户号关系列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WechatUserBindingVo> queryList(@Param("page") Page<WechatUserBinding> page, @Param("query") WechatUserBindingBo query);

    List<WechatUserBindingVo> queryBindingUserListByOpenid(@Param("openid") String openid);


}
