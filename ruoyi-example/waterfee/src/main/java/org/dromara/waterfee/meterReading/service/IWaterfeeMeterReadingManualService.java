package org.dromara.waterfee.meterReading.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeMeterReadingManualBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeMeterReadingManualVo;

import java.util.Collection;
import java.util.List;

/**
 * 抄表补录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IWaterfeeMeterReadingManualService {

    /**
     * 查询抄表补录
     *
     * @param manualId 主键
     * @return 抄表补录
     */
    WaterfeeMeterReadingManualVo queryById(Long manualId);

    /**
     * 查询抄表补录列表
     *
     * @param bo 抄表补录
     * @return 抄表补录集合
     */
    TableDataInfo<WaterfeeMeterReadingManualVo> queryPageList(WaterfeeMeterReadingManualBo bo, PageQuery pageQuery);

    /**
     * 查询抄表补录列表
     *
     * @param bo 抄表补录
     * @return 抄表补录集合
     */
    List<WaterfeeMeterReadingManualVo> queryList(WaterfeeMeterReadingManualBo bo);

    /**
     * 新增抄表补录
     *
     * @param bo 抄表补录
     * @return 结果
     */
    Boolean insertByBo(WaterfeeMeterReadingManualBo bo);

    /**
     * 修改抄表补录
     *
     * @param bo 抄表补录
     * @return 结果
     */
    Boolean updateByBo(WaterfeeMeterReadingManualBo bo);

    /**
     * 校验并批量删除抄表补录
     *
     * @param ids     需要删除的抄表补录主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
