package org.dromara.waterfee.pay.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.pay.domain.WechatRefundRecord;
import org.dromara.waterfee.pay.domain.vo.WechatRefundRecordVo;
import org.dromara.waterfee.pay.domain.bo.WechatRefundRecordBo;
import org.dromara.waterfee.pay.service.IWechatRefundRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 微信退款记录
 * 前端访问路由地址为:/waterfee/refundRecord
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/refundRecord")
public class WechatRefundRecordController extends BaseController {

    private final IWechatRefundRecordService wechatRefundRecordService;

    /**
     * 查询微信退款记录列表
     */
    @SaCheckPermission("waterfee:refundRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WechatRefundRecordVo> list(WechatRefundRecordBo bo, PageQuery pageQuery) {
        return wechatRefundRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出微信退款记录列表
     */
    @SaCheckPermission("waterfee:refundRecord:export")
    @Log(title = "微信退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WechatRefundRecordBo bo, HttpServletResponse response) {
        List<WechatRefundRecordVo> list = wechatRefundRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "微信退款记录", WechatRefundRecordVo.class, response);
    }

    /**
     * 获取微信退款记录详细信息
     *
     * @param refundId 主键
     */
    @SaCheckPermission("waterfee:refundRecord:query")
    @GetMapping("/{refundId}")
    public R<WechatRefundRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("refundId") String refundId) {
        return R.ok(wechatRefundRecordService.queryById(refundId));
    }

    /**
     * 新增微信退款记录
     */
    @SaCheckPermission("waterfee:refundRecord:add")
    @Log(title = "微信退款记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WechatRefundRecordBo bo) {
        return toAjax(wechatRefundRecordService.insertByBo(bo));
    }

    /**
     * 修改微信退款记录
     */
    @SaCheckPermission("waterfee:refundRecord:edit")
    @Log(title = "微信退款记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WechatRefundRecordBo bo) {
        return toAjax(wechatRefundRecordService.updateByBo(bo));
    }

    /**
     * 删除微信退款记录
     *
     * @param refundIds 主键串
     */
    @SaCheckPermission("waterfee:refundRecord:remove")
    @Log(title = "微信退款记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{refundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("refundIds") String[] refundIds) {
        return toAjax(wechatRefundRecordService.deleteWithValidByIds(List.of(refundIds), true));
    }
}
