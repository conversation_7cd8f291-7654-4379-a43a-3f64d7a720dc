package org.dromara.waterfee.demo;

import org.dromara.common.core.domain.R;
import org.dromara.waterfee.counter.domain.bo.WaterfeeDepositBo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;

import static org.dromara.waterfee.demo.utils.RandomDataGenerator.*;

/**
 * 插入用户和水表数据的演示程序
 */
@RestController
@RequestMapping("/demo")
public class InsertUserAndMeterDemo {

    @Autowired
    private IWaterfeeUserService waterfeeUserService;

    @Autowired
    private IWaterfeeMeterService waterfeeMeterService;

    @Autowired
    private IWaterfeeCounterPaymentService counterPaymentService;

    @PostMapping("/insertUserAndMeter")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> insertUserAndMeter() {
        // 创建用户数据
        WaterfeeUserBo userBo = new WaterfeeUserBo();
        String randomUserName = generateRandomUserName();
        userBo.setUserName(randomUserName); // 使用随机生成的用户名
        userBo.setPhoneNumber(generateRandomPhoneNumber()); // 使用随机生成的手机号码
        userBo.setCertificateType("identity_card"); // 身份证
        userBo.setCertificateNumber(generateRandomIdCard()); // 使用随机生成的身份证号码
        userBo.setUserStatus("normal"); // 正常状态
        userBo.setAddress(generateRandomAddress()); // 使用随机生成的地址
        userBo.setSupplyDate(new Date());
        userBo.setAreaId(1905058024441294849L);
        userBo.setCommunityId(1L);
        userBo.setUseWaterNature("resident"); // 居民用水
        userBo.setUseWaterNumber(3L);
        userBo.setInvoiceType("VAT_invoice"); // 增值税发票
        userBo.setPriceUseWaterNature("resident"); // 居民水
        userBo.setBillingMethod("1933328427904176130"); //关联阶梯


        // 插入用户
        Boolean userResult = waterfeeUserService.insertByBo(userBo);
        if (userResult) {
            System.out.println("用户插入成功，用户ID: " + userBo.getUserId() + "，用户名: " + randomUserName);

            // 创建水表数据
            WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
            meterBo.setMeterNo(generateRandomMeterNo()); // 生成唯一水表编号
            meterBo.setMeterType(2); // 水表类型为2
            meterBo.setBusinessAreaId(1905058024441294849L);
            meterBo.setMeterBookId(2L); // 表册ID为2
            meterBo.setUserId(userBo.getUserId()); // 关联用户
            meterBo.setUserNo(userBo.getUserNo()); // 关联用户编号
            meterBo.setInstallDate(new Date());
            meterBo.setInstallAddress(userBo.getAddress()); // 使用用户地址作为安装地址


            // 插入水表
            Boolean meterResult = waterfeeMeterService.insertByBo(meterBo);
            if (meterResult) {
                System.out.println("水表创建成功，水表编号: " + meterBo.getMeterNo());
                System.out.println("用户与水表关联完成");

                // 为新用户充值10000元
                try {
                    WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
                    depositBo.setUserId(userBo.getUserId());
                    depositBo.setAmount(new BigDecimal("10000.00")); // 充值10000元
                    depositBo.setPaymentMethod("CASH"); // 现金支付
                    depositBo.setRemark("新用户注册赠送");
                    depositBo.setTollCollector("系统自动");

                    Boolean depositResult = counterPaymentService.addDeposit(depositBo);
                    if (depositResult) {
                        System.out.println("用户充值成功，充值金额: 10000.00元");
                        return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，已自动充值10000元");
                    } else {
                        System.out.println("用户充值失败");
                        return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，但充值失败");
                    }
                } catch (Exception e) {
                    System.out.println("用户充值异常: " + e.getMessage());
                    return R.ok("Mock数据插入成功：用户[" + randomUserName + "]和水表[" + meterBo.getMeterNo() + "]创建完成，但充值异常: " + e.getMessage());
                }
            } else {
                System.out.println("水表创建失败");
                return R.fail("水表创建失败");
            }
        } else {
            System.out.println("用户插入失败");
            return R.fail("用户插入失败");
        }
    }

    /**
     * 查询用户余额
     * @param userId 用户ID
     * @return 用户余额信息
     */
    @org.springframework.web.bind.annotation.GetMapping("/getUserBalance")
    public R<String> getUserBalance(@org.springframework.web.bind.annotation.RequestParam Long userId) {
        try {
            BigDecimal balance = waterfeeUserService.getUserBalance(userId);
            return R.ok("用户ID: " + userId + " 的余额为: " + balance + " 元");
        } catch (Exception e) {
            return R.fail("查询用户余额失败: " + e.getMessage());
        }
    }

    /**
     * 批量插入10000条用户记录
     * @return 插入结果
     */
    @org.springframework.web.bind.annotation.GetMapping("/insert10000Records")
    @Transactional(rollbackFor = Exception.class)
    public R<String> insert10000Records() {
        return insertBatchRecords(10000);
    }

    /**
     * 批量插入指定数量的用户记录
     * @param count 插入数量
     * @return 插入结果
     */
    @org.springframework.web.bind.annotation.GetMapping("/insertBatchRecords")
    @Transactional(rollbackFor = Exception.class)
    public R<String> insertBatchRecords(@org.springframework.web.bind.annotation.RequestParam(defaultValue = "1000") Integer count) {
        if (count <= 0 || count > 50000) {
            return R.fail("插入数量必须在1-50000之间");
        }

        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorLog = new StringBuilder();

        System.out.println("开始批量插入 " + count + " 条记录...");

        // 分批处理，每批100条
        int batchSize = 100;
        int totalBatches = (count + batchSize - 1) / batchSize;

        for (int batch = 0; batch < totalBatches; batch++) {
            int startIndex = batch * batchSize;
            int endIndex = Math.min(startIndex + batchSize, count);

            System.out.println("处理第 " + (batch + 1) + "/" + totalBatches + " 批，记录 " + (startIndex + 1) + "-" + endIndex);

            for (int i = startIndex; i < endIndex; i++) {
                try {
                    // 创建用户数据
                    WaterfeeUserBo userBo = new WaterfeeUserBo();
                    String randomUserName = generateRandomUserName();
                    userBo.setUserName(randomUserName);
                    userBo.setPhoneNumber(generateRandomPhoneNumber());
                    userBo.setCertificateType("identity_card");
                    userBo.setCertificateNumber(generateRandomIdCard());
                    userBo.setUserStatus("normal");
                    userBo.setAddress(generateRandomAddress());
                    userBo.setSupplyDate(new Date());
                    userBo.setAreaId(1905058024441294849L);
                    userBo.setCommunityId(1L);
                    userBo.setUseWaterNature("resident");
                    userBo.setUseWaterNumber(3L);
                    userBo.setInvoiceType("VAT_invoice");
                    userBo.setPriceUseWaterNature("resident");
                    userBo.setBillingMethod("1933328427904176130");

                    // 插入用户
                    Boolean userResult = waterfeeUserService.insertByBo(userBo);
                    if (!userResult) {
                        failCount++;
                        errorLog.append("记录").append(i + 1).append(": 用户创建失败\n");
                        continue;
                    }

                    // 创建水表数据
                    WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
                    meterBo.setMeterNo(generateRandomMeterNo());
                    meterBo.setMeterType(2);
                    meterBo.setBusinessAreaId(1905058024441294849L);
                    meterBo.setMeterBookId(2L);
                    meterBo.setUserId(userBo.getUserId());
                    meterBo.setUserNo(userBo.getUserNo());
                    meterBo.setInstallDate(new Date());
                    meterBo.setInstallAddress(userBo.getAddress());

                    // 插入水表
                    Boolean meterResult = waterfeeMeterService.insertByBo(meterBo);
                    if (!meterResult) {
                        failCount++;
                        errorLog.append("记录").append(i + 1).append(": 水表创建失败\n");
                        continue;
                    }

                    // 充值10000元
                    try {
                        WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
                        depositBo.setUserId(userBo.getUserId());
                        depositBo.setAmount(new BigDecimal("10000.00"));
                        depositBo.setPaymentMethod("CASH");
                        depositBo.setRemark("批量创建用户赠送");
                        depositBo.setTollCollector("系统自动");

                        Boolean depositResult = counterPaymentService.addDeposit(depositBo);
                        if (depositResult) {
                            successCount++;
                        } else {
                            successCount++; // 用户和水表创建成功，只是充值失败
                            errorLog.append("记录").append(i + 1).append(": 充值失败\n");
                        }
                    } catch (Exception e) {
                        successCount++; // 用户和水表创建成功，只是充值异常
                        errorLog.append("记录").append(i + 1).append(": 充值异常 - ").append(e.getMessage()).append("\n");
                    }

                } catch (Exception e) {
                    failCount++;
                    errorLog.append("记录").append(i + 1).append(": 创建异常 - ").append(e.getMessage()).append("\n");
                }
            }

            // 每批处理完后输出进度
            if (batch % 10 == 0 || batch == totalBatches - 1) {
                long currentTime = System.currentTimeMillis();
                long elapsed = currentTime - startTime;
                double progress = (double) (batch + 1) / totalBatches * 100;
                System.out.println(String.format("进度: %.1f%%, 已成功: %d, 已失败: %d, 耗时: %d秒",
                    progress, successCount, failCount, elapsed / 1000));
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        StringBuilder result = new StringBuilder();
        result.append("批量插入完成！\n\n");
        result.append("总记录数: ").append(count).append("\n");
        result.append("成功创建: ").append(successCount).append(" 条\n");
        result.append("失败: ").append(failCount).append(" 条\n");
        result.append("成功率: ").append(String.format("%.2f%%", (double) successCount / count * 100)).append("\n");
        result.append("总耗时: ").append(totalTime / 1000).append(" 秒\n");
        result.append("平均速度: ").append(String.format("%.1f", (double) count / (totalTime / 1000.0))).append(" 条/秒\n");

        if (errorLog.length() > 0 && errorLog.length() < 2000) {
            result.append("\n错误详情:\n").append(errorLog.toString());
        } else if (errorLog.length() >= 2000) {
            result.append("\n错误详情过多，仅显示前2000字符:\n").append(errorLog.substring(0, 2000)).append("...\n");
        }

        System.out.println("批量插入完成: 成功 " + successCount + " 条, 失败 " + failCount + " 条, 耗时 " + totalTime / 1000 + " 秒");

        return R.ok(result.toString());
    }
}
