package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 应收账追补记录对象 waterfee_charge_manage_ar_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_ar_adjustment")
public class WaterfeeChargeManageArAdjustment extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 账期
     */
    private String billPeriod;

    /**
     * 原应收金额
     */
    private Long originalAmount;

    /**
     * 调整后金额
     */
    private Long adjustedAmount;

    /**
     * 调整原因
     */
    private String reason;

    /**
     * 
     */
    private String remark;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
