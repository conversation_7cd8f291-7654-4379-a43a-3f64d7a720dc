package org.dromara.waterfee.bill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付状态枚举类
 */

@Getter
@AllArgsConstructor
public enum PaymentStatusEnum {

    UNPAID("NOT_PAY", "未支付"),

    WAITING_FOR_RESULT("WAITING_FOR_RESULT", "等待支付结果"),

    PAID("PAID", "已支付"),

    REFUNDING("REFUNDING", "退款中"),

    REFUND("REFUND", "已退款"),

    REFUND_ABNORMAL("REFUND_ABNORMAL", "退款异常");

    private final String code;

    private final String desc;

}
