package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;

import java.util.List;
import java.util.Map;

/**
 * 账单处理批量操作Mapper接口
 * 专门用于优化账单相关的批量数据库操作
 */
@Mapper
public interface BillProcessingBatchMapper {

    /**
     * 批量获取账单信息
     *
     * @param billIds 账单ID列表
     * @return 账单信息列表
     */
    @Select("<script>" +
            "SELECT bill_id, bill_number, customer_id, meter_id, meter_book_id, " +
            "billing_period_start, billing_period_end, previous_reading_value, current_reading_value, " +
            "consumption_volume, base_charge_amount, adjustments_amount, total_amount, " +
            "bill_status, billing_due_date, create_time, update_time, remark, del_flag, tenant_id " +
            "FROM waterfee_bills " +
            "WHERE bill_id IN " +
            "<foreach collection='billIds' item='billId' open='(' separator=',' close=')'>" +
            "#{billId}" +
            "</foreach>" +
            " AND del_flag = '0'" +
            "</script>")
    List<WaterfeeBillVo> batchSelectBillsByIds(@Param("billIds") List<Long> billIds);

    /**
     * 批量获取客户账单信息
     *
     * @param customerIds 客户ID列表
     * @return 账单信息列表
     */
    @Select("<script>" +
            "SELECT bill_id, bill_number, customer_id, total_amount, bill_status " +
            "FROM waterfee_bills " +
            "WHERE customer_id IN " +
            "<foreach collection='customerIds' item='customerId' open='(' separator=',' close=')'>" +
            "#{customerId}" +
            "</foreach>" +
            " AND bill_status = '0' " +
            " AND del_flag = '0'" +
            "</script>")
    List<WaterfeeBillVo> batchSelectUnpaidBillsByCustomers(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量获取客户余额信息
     *
     * @param customerIds 客户ID列表
     * @return 客户余额信息
     */
    @Select("<script>" +
            "SELECT user_id as customer_id, balance as account_balance, 0 as frozen_amount " +
            "FROM waterfee_user " +
            "WHERE user_id IN " +
            "<foreach collection='customerIds' item='customerId' open='(' separator=',' close=')'>" +
            "#{customerId}" +
            "</foreach>" +
            " AND del_flag = '0'" +
            "</script>")
    List<Map<String, Object>> batchSelectCustomerBalances(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量更新账单状态
     *
     * @param billIds 账单ID列表
     * @param status  新状态
     * @return 更新成功的记录数
     */
    int batchUpdateBillStatus(@Param("billIds") List<Long> billIds, @Param("status") String status);

    /**
     * 批量插入支付记录
     *
     * @param paymentRecords 支付记录列表
     * @return 插入成功的记录数
     */
    int batchInsertPaymentRecords(@Param("paymentRecords") List<Map<String, Object>> paymentRecords);

    /**
     * 批量获取账单统计信息
     *
     * @param meterNos 水表编号列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    @Select("<script>" +
            "SELECT meter_no, " +
            "COUNT(*) as bill_count, " +
            "SUM(total_amount) as total_amount, " +
            "SUM(CASE WHEN bill_status = '1' THEN 1 ELSE 0 END) as paid_count, " +
            "SUM(CASE WHEN bill_status = '0' THEN 1 ELSE 0 END) as unpaid_count " +
            "FROM waterfee_bill " +
            "WHERE meter_no IN " +
            "<foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "#{meterNo}" +
            "</foreach>" +
            " AND create_time BETWEEN #{startDate} AND #{endDate}" +
            " AND del_flag = '0' " +
            "GROUP BY meter_no" +
            "</script>")
    List<Map<String, Object>> batchGetBillStatistics(@Param("meterNos") List<String> meterNos,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    /**
     * 批量检查账单是否存在
     *
     * @param meterNos 水表编号列表
     * @param billingPeriod 账单周期
     * @return 已存在账单的水表编号列表
     */
    @Select("<script>" +
            "SELECT DISTINCT meter_no FROM waterfee_bill " +
            "WHERE meter_no IN " +
            "<foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "#{meterNo}" +
            "</foreach>" +
            " AND billing_period_start = #{billingPeriod}" +
            " AND del_flag = '0'" +
            "</script>")
    List<String> batchCheckBillExists(@Param("meterNos") List<String> meterNos,
                                    @Param("billingPeriod") String billingPeriod);

    /**
     * 批量获取客户支付能力信息
     *
     * @param customerIds 客户ID列表
     * @return 支付能力信息
     */
    @Select("<script>" +
            "SELECT u.user_id as customer_id, " +
            "u.balance as account_balance, " +
            "0 as frozen_amount, " +
            "u.balance as available_balance, " +
            "COALESCE(ub.unpaid_amount, 0) as unpaid_amount " +
            "FROM waterfee_user u " +
            "LEFT JOIN (" +
            "  SELECT customer_id, SUM(total_amount) as unpaid_amount " +
            "  FROM waterfee_bills " +
            "  WHERE customer_id IN " +
            "  <foreach collection='customerIds' item='customerId' open='(' separator=',' close=')'>" +
            "    #{customerId}" +
            "  </foreach>" +
            "  AND bill_status = '0' AND del_flag = '0' " +
            "  GROUP BY customer_id" +
            ") ub ON u.user_id = ub.customer_id " +
            "WHERE u.user_id IN " +
            "<foreach collection='customerIds' item='customerId' open='(' separator=',' close=')'>" +
            "#{customerId}" +
            "</foreach>" +
            " AND u.del_flag = '0'" +
            "</script>")
    List<Map<String, Object>> batchGetCustomerPaymentCapacity(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量获取最近的支付记录
     *
     * @param customerIds 客户ID列表
     * @param limit       记录数限制
     * @return 支付记录列表
     */
    @Select("<script>" +
            "SELECT * FROM (" +
            "  SELECT pd.*, ROW_NUMBER() OVER (PARTITION BY pd.user_id ORDER BY pd.payment_time DESC) as rn " +
            "  FROM waterfee_payment_detail pd " +
            "  WHERE pd.user_id IN " +
            "  <foreach collection='customerIds' item='customerId' open='(' separator=',' close=')'>" +
            "    #{customerId}" +
            "  </foreach>" +
            "  AND pd.del_flag = '0'" +
            ") ranked " +
            "WHERE ranked.rn &lt;= #{limit} " +
            "ORDER BY ranked.user_id, ranked.payment_time DESC" +
            "</script>")
    List<Map<String, Object>> batchGetRecentPaymentRecords(@Param("customerIds") List<Long> customerIds,
                                                          @Param("limit") int limit);

    /**
     * 批量获取账单详细信息（包含客户信息）
     *
     * @param billIds 账单ID列表
     * @return 账单详细信息
     */
    @Select("<script>" +
            "SELECT b.*, u.user_name as customer_name, u.phone_number as contact_phone, u.balance as account_balance " +
            "FROM waterfee_bills b " +
            "LEFT JOIN waterfee_user u ON b.customer_id = u.user_id AND u.del_flag = '0' " +
            "WHERE b.bill_id IN " +
            "<foreach collection='billIds' item='billId' open='(' separator=',' close=')'>" +
            "#{billId}" +
            "</foreach>" +
            " AND b.del_flag = '0'" +
            "</script>")
    List<Map<String, Object>> batchGetBillDetailsWithCustomer(@Param("billIds") List<Long> billIds);

    /**
     * 批量更新客户账户余额
     *
     * @param balanceUpdates 余额更新信息列表
     * @return 更新成功的记录数
     */
    int batchUpdateCustomerBalances(@Param("balanceUpdates") List<Map<String, Object>> balanceUpdates);

    /**
     * 批量获取账单支付历史
     *
     * @param billIds 账单ID列表
     * @return 支付历史记录
     */
    @Select("<script>" +
            "SELECT bill_id, payment_amount, payment_time, payment_method, payment_status " +
            "FROM waterfee_payment_detail " +
            "WHERE bill_id IN " +
            "<foreach collection='billIds' item='billId' open='(' separator=',' close=')'>" +
            "#{billId}" +
            "</foreach>" +
            " AND del_flag = '0' " +
            "ORDER BY bill_id, payment_time DESC" +
            "</script>")
    List<Map<String, Object>> batchGetBillPaymentHistory(@Param("billIds") List<Long> billIds);

    /**
     * 批量检查客户支付能力
     *
     * @param paymentRequests 支付请求列表（包含客户ID和金额）
     * @return 支付能力检查结果
     */
    @Select("<script>" +
            "SELECT user_id as customer_id, " +
            "balance as account_balance, " +
            "0 as frozen_amount, " +
            "balance as available_balance " +
            "FROM waterfee_user " +
            "WHERE user_id IN " +
            "<foreach collection='paymentRequests' item='request' open='(' separator=',' close=')'>" +
            "#{request.customerId}" +
            "</foreach>" +
            " AND del_flag = '0'" +
            "</script>")
    List<Map<String, Object>> batchCheckPaymentCapacity(@Param("paymentRequests") List<Map<String, Object>> paymentRequests);
}
