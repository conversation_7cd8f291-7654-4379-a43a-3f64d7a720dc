package org.dromara.waterfee.template.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 模板管理业务对象 WaterfeeTemplateBo
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class WaterfeeTemplateBo extends BaseEntity {

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空", groups = {EditGroup.class})
    private Long templateId;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String templateName;

    /**
     * 模板类型（WORD-Word文档 EXCEL-Excel文档 PDF-PDF文档）
     */
    @NotBlank(message = "模板类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String templateType;

    /**
     * 模板分类（BILL-账单模板 REPORT-报表模板 NOTICE-通知模板）
     */
    private String templateCategory;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 是否启用（0-禁用 1-启用）
     */
    private String isEnabled;

    /**
     * 模板描述
     */
    private String templateDescription;

    /**
     * 上传者
     */
    private String uploadBy;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 启用者
     */
    private String enabledBy;

    /**
     * 启用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enabledTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上传的文件（用于文件上传）
     */
    private MultipartFile file;
}
