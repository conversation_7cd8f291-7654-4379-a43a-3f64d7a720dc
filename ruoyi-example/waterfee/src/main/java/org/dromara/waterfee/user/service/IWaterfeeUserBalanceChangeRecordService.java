package org.dromara.waterfee.user.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBalanceChangeRecordBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserBalanceChangeRecordVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户余额变更记录Service接口
 */
public interface IWaterfeeUserBalanceChangeRecordService {

    /**
     * 查询用户余额变更记录
     */
    WaterfeeUserBalanceChangeRecordVo queryById(Long recordId);

    /**
     * 查询用户余额变更记录列表
     */
    TableDataInfo<WaterfeeUserBalanceChangeRecordVo> queryPageList(WaterfeeUserBalanceChangeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询用户余额变更记录列表
     */
    List<WaterfeeUserBalanceChangeRecordVo> queryList(WaterfeeUserBalanceChangeRecordBo bo);

    /**
     * 新增用户余额变更记录
     */
    Boolean insertByBo(WaterfeeUserBalanceChangeRecordBo bo);

    /**
     * 记录余额变更
     *
     * @param userId 用户ID
     * @param userNo 用户编号
     * @param changeType 变更类型
     * @param beforeBalance 变更前余额
     * @param changeAmount 变更金额
     * @param afterBalance 变更后余额
     * @param businessId 关联业务ID
     * @param businessType 关联业务类型
     * @param changeReason 变更原因
     * @param operator 操作人
     * @param remark 备注
     */
    void recordBalanceChange(Long userId, String userNo, String changeType, 
                           BigDecimal beforeBalance, BigDecimal changeAmount, BigDecimal afterBalance,
                           String businessId, String businessType, String changeReason, 
                           String operator, String remark);
}