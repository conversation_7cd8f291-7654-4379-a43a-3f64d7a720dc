package org.dromara.waterfee.bill.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.bill.domain.PaymentDetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 缴费明细视图对象 waterfee_payment_detail
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "缴费明细视图对象")
@AutoMapper(target = PaymentDetail.class)
public class PaymentDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 缴费明细ID
     */
    @ExcelProperty(value = "缴费明细ID")
    @Schema(description = "缴费明细ID")
    private String paymentDetailId;

    /**
     * 关联账单ID
     */
    @ExcelProperty(value = "关联账单ID")
    @Schema(description = "关联账单ID")
    private Long billId;

    // 可以考虑关联查询账单号等信息展示给前端
    // @ExcelProperty(value = "账单编号")
    // @Schema(description = "账单编号")
    // private String billNumber;

    /**
     * 支付金额
     */
    @ExcelProperty(value = "支付金额")
    @Schema(description = "支付金额")
    private BigDecimal paymentAmount;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    @Schema(description = "支付时间")
    private Date paymentTime;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_payment_method") // 假设字典类型为 waterfee_payment_method
    @Schema(description = "支付方式")
    private String paymentMethod;

    /**
     * 交易流水号
     */
    @ExcelProperty(value = "交易流水号")
    @Schema(description = "交易流水号")
    private String transactionId;

    /**
     * 支付状态
     */
    @ExcelProperty(value = "支付状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_payment_status") // 假设字典类型为 waterfee_payment_status
    @Schema(description = "支付状态")
    private String paymentStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 收入时间
     */
    @ExcelProperty(value = "收入时间")
    @Schema(description = "收入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date incomeTime;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用户姓名
     */
    private String userName;
}
