package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;

import java.util.List;
import java.util.Map;

/**
 * 抄表记录批量操作Mapper接口
 * 专门用于优化批量数据库操作
 */
@Mapper
public interface MeterReadingBatchMapper {

    /**
     * 批量获取水表信息
     *
     * @param meterNos 水表编号列表
     * @return 水表信息列表
     */
    @Select("<script>" +
            "SELECT meter_id, meter_no, meter_type, manufacturer, meter_category, " +
            "meter_classification, measurement_purpose, caliber, accuracy, initial_reading, " +
            "install_date, business_area_id, meter_book_id, sort_no, install_address, " +
            "meter_ratio, communication_mode, valve_control, imei, imsi, iot_platform, " +
            "prepaid, user_id, create_by, create_time, update_by, update_time, remark, " +
            "del_flag, tenant_id " +
            "FROM waterfee_meter " +
            "WHERE meter_no IN " +
            "<foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "#{meterNo}" +
            "</foreach>" +
            " AND del_flag = '0'" +
            "</script>")
    List<WaterfeeMeterVo> batchSelectMetersByNos(@Param("meterNos") List<String> meterNos);

    /**
     * 批量获取最新抄表记录
     *
     * @param meterNos 水表编号列表
     * @return 最新抄表记录列表
     */
    @Select("<script>" +
            "SELECT r1.* FROM waterfee_meter_reading_record r1 " +
            "INNER JOIN (" +
            "  SELECT meter_no, MAX(reading_time) as max_time " +
            "  FROM waterfee_meter_reading_record " +
            "  WHERE meter_no IN " +
            "  <foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "    #{meterNo}" +
            "  </foreach>" +
            "  AND del_flag = '0' " +
            "  GROUP BY meter_no" +
            ") r2 ON r1.meter_no = r2.meter_no AND r1.reading_time = r2.max_time " +
            "WHERE r1.del_flag = '0'" +
            "</script>")
    List<WaterfeeMeterReadingRecord> batchSelectLatestRecords(@Param("meterNos") List<String> meterNos);

    /**
     * 批量插入抄表记录
     *
     * @param records 抄表记录列表
     * @return 插入成功的记录数
     */
    int batchInsertReadingRecords(@Param("records") List<WaterfeeMeterReadingRecord> records);

    /**
     * 批量检查季度重复记录
     *
     * @param meterNos 水表编号列表
     * @param taskId   任务ID
     * @param year     年份
     * @param quarter  季度
     * @return 已存在记录的水表编号列表
     */
    @Select("<script>" +
            "SELECT DISTINCT meter_no FROM waterfee_meter_reading_record " +
            "WHERE meter_no IN " +
            "<foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "#{meterNo}" +
            "</foreach>" +
            " AND task_id = #{taskId}" +
            " AND YEAR(reading_time) = #{year}" +
            " AND QUARTER(reading_time) = #{quarter}" +
            " AND del_flag = '0'" +
            "</script>")
    List<String> batchCheckQuarterDuplicates(@Param("meterNos") List<String> meterNos,
                                           @Param("taskId") Long taskId,
                                           @Param("year") int year,
                                           @Param("quarter") int quarter);

    /**
     * 批量获取水表统计信息
     *
     * @param meterBookIds 表册ID列表
     * @return 统计信息
     */
    @Select("<script>" +
            "SELECT meter_book_id, COUNT(*) as total_count, " +
            "SUM(CASE WHEN meter_type = 1 THEN 1 ELSE 0 END) as mechanical_count, " +
            "SUM(CASE WHEN meter_type = 2 THEN 1 ELSE 0 END) as intelligent_count " +
            "FROM waterfee_meter " +
            "WHERE meter_book_id IN " +
            "<foreach collection='meterBookIds' item='bookId' open='(' separator=',' close=')'>" +
            "#{bookId}" +
            "</foreach>" +
            " AND del_flag = '0' " +
            "GROUP BY meter_book_id" +
            "</script>")
    List<Map<String, Object>> batchGetMeterStatistics(@Param("meterBookIds") List<Long> meterBookIds);

    /**
     * 批量更新抄表记录状态
     *
     * @param recordIds 记录ID列表
     * @param status    状态
     * @return 更新成功的记录数
     */
    int batchUpdateRecordStatus(@Param("recordIds") List<Long> recordIds, @Param("status") String status);

    /**
     * 批量删除抄表记录（逻辑删除）
     *
     * @param recordIds 记录ID列表
     * @return 删除成功的记录数
     */
    int batchDeleteRecords(@Param("recordIds") List<Long> recordIds);

    /**
     * 获取指定时间范围内的抄表记录统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN is_audited = '1' THEN 1 ELSE 0 END) as audited_count, " +
            "SUM(CASE WHEN is_pending = '1' THEN 1 ELSE 0 END) as pending_count, " +
            "SUM(CASE WHEN current_reading IS NOT NULL THEN 1 ELSE 0 END) as completed_count, " +
            "AVG(CASE WHEN water_usage IS NOT NULL THEN water_usage ELSE 0 END) as avg_water_usage " +
            "FROM waterfee_meter_reading_record " +
            "WHERE reading_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0'")
    Map<String, Object> getReadingStatistics(@Param("startTime") String startTime, 
                                           @Param("endTime") String endTime);

    /**
     * 获取任务执行统计
     *
     * @param taskIds 任务ID列表
     * @return 任务统计结果
     */
    @Select("<script>" +
            "SELECT task_id, " +
            "COUNT(*) as total_records, " +
            "SUM(CASE WHEN current_reading IS NOT NULL THEN 1 ELSE 0 END) as completed_records, " +
            "SUM(CASE WHEN is_audited = '1' THEN 1 ELSE 0 END) as audited_records, " +
            "MIN(reading_time) as first_reading_time, " +
            "MAX(reading_time) as last_reading_time " +
            "FROM waterfee_meter_reading_record " +
            "WHERE task_id IN " +
            "<foreach collection='taskIds' item='taskId' open='(' separator=',' close=')'>" +
            "#{taskId}" +
            "</foreach>" +
            " AND del_flag = '0' " +
            "GROUP BY task_id" +
            "</script>")
    List<Map<String, Object>> batchGetTaskStatistics(@Param("taskIds") List<Long> taskIds);

    /**
     * 批量获取水表的最近N次抄表记录
     *
     * @param meterNos 水表编号列表
     * @param limit    记录数限制
     * @return 抄表记录列表
     */
    @Select("<script>" +
            "SELECT * FROM (" +
            "  SELECT *, ROW_NUMBER() OVER (PARTITION BY meter_no ORDER BY reading_time DESC) as rn " +
            "  FROM waterfee_meter_reading_record " +
            "  WHERE meter_no IN " +
            "  <foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "    #{meterNo}" +
            "  </foreach>" +
            "  AND del_flag = '0'" +
            ") ranked " +
            "WHERE rn &lt;= #{limit} " +
            "ORDER BY meter_no, reading_time DESC" +
            "</script>")
    List<WaterfeeMeterReadingRecord> batchGetRecentRecords(@Param("meterNos") List<String> meterNos, 
                                                          @Param("limit") int limit);

    /**
     * 批量检查水表是否存在未完成的抄表记录
     *
     * @param meterNos 水表编号列表
     * @return 存在未完成记录的水表编号列表
     */
    @Select("<script>" +
            "SELECT DISTINCT meter_no FROM waterfee_meter_reading_record " +
            "WHERE meter_no IN " +
            "<foreach collection='meterNos' item='meterNo' open='(' separator=',' close=')'>" +
            "#{meterNo}" +
            "</foreach>" +
            " AND current_reading IS NULL" +
            " AND is_audited != '1'" +
            " AND del_flag = '0'" +
            "</script>")
    List<String> batchCheckIncompleteRecords(@Param("meterNos") List<String> meterNos);
}
