package org.dromara.waterfee.bill.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.bill.domain.PaymentDetail;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 缴费明细业务对象 waterfee_payment_detail
 *
 * <AUTHOR> Name
 * @date 2024-xx-xx
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "缴费明细业务对象")
@AutoMapper(target = PaymentDetail.class, reverseConvertGenerate = false)
public class PaymentDetailBo extends BaseEntity {

    /**
     * 缴费明细ID (主键, 可使用UUID或雪花算法生成)
     */
    @Schema(description = "缴费明细ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "缴费明细ID不能为空", groups = {EditGroup.class})
    private String paymentDetailId;

    /**
     * 关联账单ID
     */
    @Schema(description = "关联账单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "关联账单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String billId;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付金额不能为空", groups = {AddGroup.class, EditGroup.class})
    @DecimalMin(value = "0.01", message = "支付金额必须>=0", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal paymentAmount;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date paymentTime;

    /**
     * 支付方式 (例如: WECHAT_PAY, ALIPAY, BANK_TRANSFER, CASH)
     */
    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "支付方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String paymentMethod;

    /**
     * 交易流水号 (支付平台返回)
     */
    @Schema(description = "交易流水号")
    private String transactionId;

    /**
     * 支付状态 (例如: SUCCESS, FAILED, PENDING)
     */
    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "支付状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String paymentStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 户号
     */
    private String userNo;

    /**
     * 关联微信用户
     */
    private String openid;

}
