package org.dromara.waterfee.template.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.template.domain.bo.WaterfeeTemplateBo;
import org.dromara.waterfee.template.domain.vo.WaterfeeTemplateVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 模板管理服务接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IWaterfeeTemplateService {

    /**
     * 查询模板管理
     *
     * @param templateId 模板管理主键
     * @return 模板管理
     */
    WaterfeeTemplateVo queryById(Long templateId);

    /**
     * 查询模板管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模板管理分页列表
     */
    TableDataInfo<WaterfeeTemplateVo> queryPageList(WaterfeeTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询模板管理列表
     *
     * @param bo 查询条件
     * @return 模板管理列表
     */
    List<WaterfeeTemplateVo> queryList(WaterfeeTemplateBo bo);

    /**
     * 新增模板管理
     *
     * @param bo 模板管理
     * @return 结果
     */
    Boolean insertByBo(WaterfeeTemplateBo bo);

//    /**
//     * 上传模板文件
//     *
//     * @param file        上传的文件
//     * @param templateBo  模板信息
//     * @return 结果
//     */
//    Boolean uploadTemplate(MultipartFile file, WaterfeeTemplateBo templateBo);

    /**
     * 修改模板管理
     *
     * @param bo 模板管理
     * @return 结果
     */
    Boolean updateByBo(WaterfeeTemplateBo bo);

    /**
     * 启用模板
     *
     * @param templateId 模板ID
     * @return 结果
     */
    Boolean enableTemplate(Long templateId);

    /**
     * 禁用模板
     *
     * @param templateId 模板ID
     * @return 结果
     */
    Boolean disableTemplate(Long templateId);

    /**
     * 获取启用的模板
     *
     * @param templateType     模板类型
     * @param templateCategory 模板分类
     * @return 启用的模板
     */
    WaterfeeTemplateVo getEnabledTemplate(String templateType, String templateCategory);

    /**
     * 校验并批量删除模板管理信息
     *
     * @param ids     需要删除的模板管理主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

//    /**
//     * 下载模板文件
//     *
//     * @param templateId 模板ID
//     * @return 文件路径
//     */
//    String downloadTemplate(Long templateId);
}
