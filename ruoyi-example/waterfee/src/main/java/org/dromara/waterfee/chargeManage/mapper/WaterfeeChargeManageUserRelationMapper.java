package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageUserRelationVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageUserRelationBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 依托用户关系维护Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageUserRelationMapper extends BaseMapperPlus<WaterfeeChargeManageUserRelation, WaterfeeChargeManageUserRelationVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageUserRelationVo>> P selectVoPage(IPage<WaterfeeChargeManageUserRelation> page, Wrapper<WaterfeeChargeManageUserRelation> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询依托用户关系维护列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageUserRelationVo> queryList(@Param("page") Page<WaterfeeChargeManageUserRelation> page, @Param("query") WaterfeeChargeManageUserRelationBo query);

}
