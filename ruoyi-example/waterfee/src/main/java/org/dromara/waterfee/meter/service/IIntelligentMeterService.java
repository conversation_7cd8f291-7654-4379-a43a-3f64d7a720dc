package org.dromara.waterfee.meter.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.domain.vo.IntelligentMeterDetailVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;

import java.util.List;

/**
 * 智能表服务接口
 */
public interface IIntelligentMeterService {

    /**
     * 分页查询智能表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 智能表列表
     */
    TableDataInfo<WaterfeeMeterVo> queryPageList(WaterfeeMeterBo bo, PageQuery pageQuery);

    /**
     * 查询智能表列表
     *
     * @param bo 查询条件
     * @return 智能表列表
     */
    List<WaterfeeMeterVo> queryList(WaterfeeMeterBo bo);

    /**
     * 查询智能表详情
     *
     * @param meterId 水表ID
     * @return 智能表详情
     */
    IntelligentMeterDetailVo queryDetailById(Long meterId);

    /**
     * 读取单个水表数据
     *
     * @param meterId 水表ID
     * @return 水表读数信息
     */
    MeterReadingRecordVo readMeter(Long meterId);

    /**
     * 批量读取水表数据
     *
     * @param meterIds 水表ID列表
     * @return 水表读数信息列表
     */
    List<MeterReadingRecordVo> batchReadMeter(List<Long> meterIds);

    /**
     * 开阀
     *
     * @param meterId 水表ID
     * @return 是否成功
     */
    void openValve(Long meterId);

    /**
     * 关阀
     *
     * @param meterId 水表ID
     * @return 是否成功
     */
    void closeValve(Long meterId);

    /**
     * 清零
     *
     * @param meterId 水表ID
     * @return 是否成功
     */
    void clearValve(Long meterId);

    /**
     * 阀门摆动
     *
     * @param meterId 水表ID
     * @return 是否成功
     */
    void swingValve(Long meterId);

    /**
     * 新增智能表
     *
     * @param bo 智能表信息
     * @return 是否成功
     */
    boolean insertByBo(WaterfeeMeterBo bo);

    /**
     * 修改智能表
     *
     * @param bo 智能表信息
     * @return 是否成功
     */
    boolean updateByBo(WaterfeeMeterBo bo);

    /**
     * 批量删除智能表
     *
     * @param ids     水表ID列表
     * @param isValid 是否校验
     * @return 是否成功
     */
    boolean deleteWithValidByIds(List<Long> ids, Boolean isValid);

    /**
     * 记录抄表数据
     *
     * @param meterId      水表ID
     * @param readingValue 读数值
     * @param operator     操作人
     * @param remark       备注
     */
    void recordMeterReading(Long meterId, Double readingValue, String operator, String remark);
}
