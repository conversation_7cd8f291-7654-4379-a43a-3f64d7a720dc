package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;

import java.math.BigDecimal;

/**
 * 预存款退款业务对象
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
public class WaterfeeDepositRefundBo {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = {AddGroup.class})
    private Long userId;

    /**
     * 退款金额
     */
    @NotNull(message = "退款金额不能为空", groups = {AddGroup.class})
    @PositiveOrZero(message = "退款金额必须>=0", groups = {AddGroup.class})
    private BigDecimal refundAmount;

    /**
     * 退款原因
     */
    @NotBlank(message = "退款原因不能为空", groups = {AddGroup.class})
    private String refundReason;

    /**
     * 退款方式（CASH:现金, BANK_CARD:银行卡, WECHAT:微信, ALIPAY:支付宝）
     */
    @NotBlank(message = "退款方式不能为空", groups = {AddGroup.class})
    private String refundMethod;

    /**
     * 银行卡号（退款方式为银行卡时必填）
     */
    private String bankCardNo;

    /**
     * 开户行（退款方式为银行卡时必填）
     */
    private String bankName;

    /**
     * 账户名（退款方式为银行卡时必填）
     */
    private String accountName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否强制退款（跳过部分校验）
     */
    private Boolean forceRefund = false;
}
