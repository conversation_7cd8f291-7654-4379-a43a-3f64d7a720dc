package org.dromara.waterfee.user.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class WechatUserInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 用水户名称（脱敏）
     */
    private String userName;

    /**
     * 用水地址
     */
    private String address;

    // getter和setter方法
    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
