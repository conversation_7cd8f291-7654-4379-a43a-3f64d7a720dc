package org.dromara.waterfee.user.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 合收户关系对象 consolidated_account_item
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("consolidated_account_item")
public class ConsolidatedAccountItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "consolidated_account_item_id")
    private Long consolidatedAccountItemId;

    /**
     * 关联的合收户id
     */
    private Long consolidatedAccountId;

    /**
     * 关联的用户id
     */
    private Long userId;

    /**
     * 删除标志（0代表存在 其余代表删除 使用主键值作为删除后的值，用于建立索引保证数据唯一性）
     */
    @TableLogic
    private String delFlag;


}
