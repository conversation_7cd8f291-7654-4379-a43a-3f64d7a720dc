package org.dromara.waterfee.priceManage.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;

import java.math.BigDecimal;

/**
 * 阶梯价格配置对象 waterfee_price_tier
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@TableName("waterfee_price_tier")
public class WaterfeePriceTier extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 价格配置ID (关联 waterfee_price_config.id)
     */
    private Long priceConfigId;

    /**
     * 阶梯序号 (从1开始)
     */
    private Integer tierNumber;

    /**
     * 阶梯起始量 (包含)
     */
    private BigDecimal startQuantity;

    /**
     * 阶梯结束量 (包含, null表示以上)
     */
    private BigDecimal endQuantity;

    /**
     * 本阶梯价格
     */
    private BigDecimal price;

    /**
     * 水资源税 (单位：元/立方米)
     */
    private BigDecimal waterResourceTax;

    /**
     * 污水处理费 (单位：元/立方米)
     */
    private BigDecimal sewageTreatmentFee;

    /**
     * 垃圾处理费 (单位：元/立方米)
     */
    private BigDecimal garbageDisposalFee;

    /**
     * 卫生费 (单位：元/立方米)
     */
    private BigDecimal sanitationFee;

    /**
     * 维护基金 (单位：元/立方米)
     */
    private BigDecimal maintenanceFund;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
