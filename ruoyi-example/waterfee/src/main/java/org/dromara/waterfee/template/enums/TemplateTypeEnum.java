package org.dromara.waterfee.template.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum TemplateTypeEnum {

    /**
     * Word文档
     */
    WORD("WORD", "Word文档"),

    /**
     * Excel文档
     */
    EXCEL("EXCEL", "Excel文档"),

    /**
     * PDF文档
     */
    PDF("PDF", "PDF文档");

    /**
     * 类型码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型码获取类型名称
     *
     * @param code 类型码
     * @return 类型名称
     */
    public static String getNameByCode(String code) {
        for (TemplateTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return code;
    }

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 枚举
     */
    public static TemplateTypeEnum getByCode(String code) {
        for (TemplateTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 获取所有类型
     *
     * @return 类型列表
     */
    public static java.util.List<java.util.Map<String, String>> getAllTypes() {
        java.util.List<java.util.Map<String, String>> types = new java.util.ArrayList<>();
        for (TemplateTypeEnum type : values()) {
            java.util.Map<String, String> typeMap = new java.util.HashMap<>();
            typeMap.put("code", type.getCode());
            typeMap.put("name", type.getName());
            types.add(typeMap);
        }
        return types;
    }
}
