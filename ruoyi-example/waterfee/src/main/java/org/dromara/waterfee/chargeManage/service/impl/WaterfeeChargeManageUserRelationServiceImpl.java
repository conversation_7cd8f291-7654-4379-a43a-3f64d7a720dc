package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageUserRelationBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageUserRelationVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageUserRelationMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageUserRelationService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 依托用户关系维护Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageUserRelationServiceImpl implements IWaterfeeChargeManageUserRelationService {

    private final WaterfeeChargeManageUserRelationMapper baseMapper;

    /**
     * 查询依托用户关系维护
     *
     * @param id 主键
     * @return 依托用户关系维护
     */
    @Override
    public WaterfeeChargeManageUserRelation queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询依托用户关系维护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 依托用户关系维护分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageUserRelationVo> queryPageList(WaterfeeChargeManageUserRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageUserRelation> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageUserRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的依托用户关系维护列表
     *
     * @param bo 查询条件
     * @return 依托用户关系维护列表
     */
    @Override
    public List<WaterfeeChargeManageUserRelationVo> queryList(WaterfeeChargeManageUserRelationBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageUserRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageUserRelation> buildQueryWrapper(WaterfeeChargeManageUserRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageUserRelation> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageUserRelation::getCreateTime);
        lqw.eq(bo.getMasterUserId() != null, WaterfeeChargeManageUserRelation::getMasterUserId, bo.getMasterUserId());
        lqw.eq(bo.getAttachedUserId() != null, WaterfeeChargeManageUserRelation::getAttachedUserId, bo.getAttachedUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelationType()), WaterfeeChargeManageUserRelation::getRelationType, bo.getRelationType());
        return lqw;
    }

    /**
     * 新增依托用户关系维护
     *
     * @param bo 依托用户关系维护
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageUserRelationBo bo) {
        WaterfeeChargeManageUserRelation add = MapstructUtils.convert(bo, WaterfeeChargeManageUserRelation.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改依托用户关系维护
     *
     * @param bo 依托用户关系维护
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageUserRelationBo bo) {
        WaterfeeChargeManageUserRelation update = MapstructUtils.convert(bo, WaterfeeChargeManageUserRelation.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageUserRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除依托用户关系维护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
