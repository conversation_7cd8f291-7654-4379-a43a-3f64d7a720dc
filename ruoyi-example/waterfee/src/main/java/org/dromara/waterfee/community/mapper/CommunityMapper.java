package org.dromara.waterfee.community.mapper;

import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.community.domain.Community;
import org.dromara.waterfee.community.domain.vo.CommunityVo;

/**
 * 社区管理数据层
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface CommunityMapper extends BaseMapperPlus<Community, CommunityVo> {

    /**
     * 统计指定ID的数量
     *
     * @param id 社区ID
     * @return 该id的数量
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    long countCommunityById(Long id);
}
