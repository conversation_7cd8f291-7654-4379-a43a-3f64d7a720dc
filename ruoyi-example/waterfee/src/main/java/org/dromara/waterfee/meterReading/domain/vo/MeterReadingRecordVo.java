package org.dromara.waterfee.meterReading.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 抄表记录视图对象 waterfee_meter_reading_record
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeMeterReadingRecord.class)
public class MeterReadingRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 抄表记录ID
     */
    @ExcelProperty(value = "抄表记录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long recordId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 水表类型
     */
    @ExcelProperty(value = "水表类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_meter_type")
    private String meterType;

    /**
     * 上期抄表读数
     */
    @ExcelProperty(value = "上期抄表读数")
    private Double lastReading;

    /**
     * 上期抄表时间
     */
    @ExcelProperty(value = "上期抄表时间")
    private Date lastReadingTime;

    /**
     * 本期抄表读数
     */
    @ExcelProperty(value = "本期抄表读数")
    private Double currentReading;

    /**
     * 旧表止数
     */
    @ExcelProperty(value = "旧表止数")
    private Double oldMeterStopReading;

    /**
     * 本期水量
     */
    @ExcelProperty(value = "本期水量")
    private Double waterUsage;

    /**
     * 本期抄表时间
     */
    @ExcelProperty(value = "本期抄表时间")
    private Date readingTime;

    /**
     * 抄表任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 来源
     */
    @ExcelProperty(value = "来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "normal=正常抄表,manual=人工补录")
    private String sourceType;

    /**
     * 是否审核
     */
    @ExcelProperty(value = "是否审核", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未审核,1=已审核")
    private String isAudited;

    /**
     * 补录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long manualId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 水表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long meterId;

    /**
     * 水表安装地址
     */
    private String meterAddress;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户地址
     */
    private String userAddress;

    /**
     * 用户电话
     */
    private String userPhone;


    /**
     * 是否挂起（0-正常 1-已挂起）
     */
    private String isPending;

    /**
     * 挂起原因
     */
    private String pendingReason;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
