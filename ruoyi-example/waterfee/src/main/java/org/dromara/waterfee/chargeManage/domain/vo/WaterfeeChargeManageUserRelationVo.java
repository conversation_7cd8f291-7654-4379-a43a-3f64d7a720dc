package org.dromara.waterfee.chargeManage.domain.vo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 依托用户关系维护视图对象 waterfee_charge_manage_user_relation
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageUserRelation.class)
public class WaterfeeChargeManageUserRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 主用户ID
     */
    @ExcelProperty(value = "主用户ID")
    private Long masterUserId;

    /**
     * 附属用户ID
     */
    @ExcelProperty(value = "附属用户ID")
    private Long attachedUserId;

    /**
     * 关系类型（单位-职工、业主-租户等）
     */
    @ExcelProperty(value = "关系类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位-职工、业主-租户等")
    private String relationType;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
