package org.dromara.waterfee.user.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.user.domain.qo.RecordQo;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserPriceChangeRecordBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserPriceChangeRecordVo;
import org.dromara.waterfee.user.domain.WaterfeeUserPriceChangeRecord;
import org.dromara.waterfee.user.mapper.WaterfeeUserPriceChangeRecordMapper;
import org.dromara.waterfee.user.service.IWaterfeeUserPriceChangeRecordService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用水用户用水价格变更记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class WaterfeeUserPriceChangeRecordServiceImpl implements IWaterfeeUserPriceChangeRecordService {

    private final WaterfeeUserPriceChangeRecordMapper baseMapper;

    /**
     * 查询用水用户用水价格变更记录
     *
     * @param priceChangeId 主键
     * @return 用水用户用水价格变更记录
     */
    @Override
    public WaterfeeUserPriceChangeRecord queryById(Long priceChangeId){
        return baseMapper.selectById(priceChangeId);
    }

    /**
     * 分页查询用水用户用水价格变更记录列表
     *
     * @param recordQo        查询条件
     * @param pageQuery 分页参数
     * @return 用水用户用水价格变更记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeUserPriceChangeRecordVo> queryPageList(RecordQo recordQo, PageQuery pageQuery) {
        Page<WaterfeeUserPriceChangeRecordVo> result = baseMapper.queryList(pageQuery.build(), recordQo);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用水用户用水价格变更记录列表
     *
     * @param bo 查询条件
     * @return 用水用户用水价格变更记录列表
     */
    @Override
    public List<WaterfeeUserPriceChangeRecordVo> queryList(WaterfeeUserPriceChangeRecordBo bo) {
        LambdaQueryWrapper<WaterfeeUserPriceChangeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeUserPriceChangeRecord> buildQueryWrapper(WaterfeeUserPriceChangeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeUserPriceChangeRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeUserPriceChangeRecord::getCreateTime);
        lqw.eq(bo.getCreateTime() != null, WaterfeeUserPriceChangeRecord::getCreateTime, bo.getCreateTime());
        return lqw;
    }

    /**
     * 新增用水用户用水价格变更记录
     *
     * @param bo 用水用户用水价格变更记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeUserPriceChangeRecordBo bo) {
        WaterfeeUserPriceChangeRecord add = MapstructUtils.convert(bo, WaterfeeUserPriceChangeRecord.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPriceChangeId(add.getPriceChangeId());
        }
        return flag;
    }

    /**
     * 修改用水用户用水价格变更记录
     *
     * @param bo 用水用户用水价格变更记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeUserPriceChangeRecordBo bo) {
        WaterfeeUserPriceChangeRecord update = MapstructUtils.convert(bo, WaterfeeUserPriceChangeRecord.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeUserPriceChangeRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用水用户用水价格变更记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
