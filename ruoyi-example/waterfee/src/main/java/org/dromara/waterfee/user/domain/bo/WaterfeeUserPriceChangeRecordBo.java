package org.dromara.waterfee.user.domain.bo;

import org.dromara.waterfee.user.domain.WaterfeeUserPriceChangeRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 用水用户用水价格变更记录业务对象 waterfee_user_price_change_record
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeUserPriceChangeRecord.class, reverseConvertGenerate = false)
public class WaterfeeUserPriceChangeRecordBo extends BaseEntity {

    /**
     * 主键
     */
    private Long priceChangeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 原价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    private String beforePriceUseWaterNature;

    /**
     * 原计费方式（字典waterfee_user_billing_method）
     */
    private String beforeBillingMethod;

    /**
     * 原是否有违约金（字典yes_no）
     */
//    private String beforeIfPenalty;

    /**
     * 原违约金类型（字典waterfee_user_penalty_type）
     */
//    private String beforePenaltyType;

    /**
     * 原是否有附加费（字典yes_no）
     */
//    private String beforeIfExtraCharge;

    /**
     * 原附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
//    private String beforeExtraChargeType;

    /**
     * 新价格-用水性质（字典waterfee_user_use_water_nature）
     */
//    @NotBlank(message = "新价格-用水性质（字典waterfee_user_use_water_nature）不能为空", groups = { AddGroup.class, EditGroup.class })
//    private String afterPriceUseWaterNature;

    /**
     * 新计费方式（字典waterfee_user_billing_method）
     */
    @NotBlank(message = "新计费方式（字典waterfee_user_billing_method）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String afterBillingMethod;

    /**
     * 新是否有违约金（字典yes_no）
     */
//    private String afterIfPenalty;

    /**
     * 新违约金类型（字典waterfee_user_penalty_type）
     */
//    private String afterPenaltyType;

    /**
     * 新是否有附加费（字典yes_no）
     */
//    private String afterIfExtraCharge;

    /**
     * 新附加费内容（多选，逗号隔开）（字典waterfee_user_extra_charge_type）
     */
//    private String afterExtraChargeType;


}
