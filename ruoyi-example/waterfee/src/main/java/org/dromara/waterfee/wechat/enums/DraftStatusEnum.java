package org.dromara.waterfee.wechat.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 草稿状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum DraftStatusEnum {

    /**
     * 草稿
     */
    DRAFT("DRAFT", "草稿"),

    /**
     * 发布中
     */
    PUBLISHING("PUBLISHING", "发布中"),

    /**
     * 已发布
     */
    PUBLISHED("PUBLISHED", "已发布");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        for (DraftStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return code;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static DraftStatusEnum getByCode(String code) {
        for (DraftStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
