package org.dromara.waterfee.meter.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 智能表基本信息视图对象
 */
@Data
@ExcelIgnoreUnannotated
public class IntelligentMeterVo {

  private static final long serialVersionUID = 1L;

  /**
   * 水表ID
   */
  @ExcelProperty(value = "水表ID")
  private Long meterId;

  /**
   * 水表编号
   */
  @ExcelProperty(value = "水表编号")
  private String meterNo;

  /**
   * 水表名称
   */
  @ExcelProperty(value = "水表名称")
  private String meterName;

  /**
   * 水表类型（1：机械表 2：智能表）
   */
  @ExcelProperty(value = "水表类型")
  private Integer meterType;

  /**
   * 水表口径
   */
  @ExcelProperty(value = "水表口径")
  private String caliber;

  /**
   * 安装地址
   */
  @ExcelProperty(value = "安装地址")
  private String installAddress;

  /**
   * 安装时间
   */
  @ExcelProperty(value = "安装时间")
  private Date installTime;

  /**
   * 状态（0正常 1停用）
   */
  @ExcelProperty(value = "状态")
  private String status;

  /**
   * 备注
   */
  @ExcelProperty(value = "备注")
  private String remark;
}