package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class MechanicalWatchesByYearVO {
    /**
     * 年月
     */
    @ExcelProperty(value = "年月")
    private String yearMonth;

    /**
     * 居民用水量
     */
    @ExcelProperty(value = "居民用水（m³）")
    private Double residentialWaterUsage;

    /**
     * 非居民用水量
     */
    @ExcelProperty(value = "非居民用水（m³）")
    private Double businessWaterUsage;

    /**
     * 特种用水量
     */
    @ExcelProperty(value = "特种用水（m³）")
    private Double specialWaterUsage;

    /**
     * 绿化用水量
     */
    @ExcelProperty(value = "绿化用水（m³）")
    private Double greenWaterUsage;

    /**
     * 合计水量
     */
    @ExcelProperty(value = "合计水量（m³）")
    private Double totalWaterUsage;
}
