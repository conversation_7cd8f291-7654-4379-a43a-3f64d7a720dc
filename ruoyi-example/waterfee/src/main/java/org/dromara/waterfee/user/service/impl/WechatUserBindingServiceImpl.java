package org.dromara.waterfee.user.service.impl;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.vo.WechatUserInfoVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.user.domain.bo.WechatUserBindingBo;
import org.dromara.waterfee.user.domain.vo.WechatUserBindingVo;
import org.dromara.waterfee.user.domain.WechatUserBinding;
import org.dromara.waterfee.user.mapper.WechatUserBindingMapper;
import org.dromara.waterfee.user.service.IWechatUserBindingService;
import org.dromara.common.satoken.utils.LoginHelper;
import cn.hutool.core.util.DesensitizedUtil;
import org.dromara.waterfee.user.service.IWaterfeeUserService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class WechatUserBindingServiceImpl implements IWechatUserBindingService {

    private final WechatUserBindingMapper baseMapper;
    private final IWaterfeeUserService waterfeeUserService;

    /**
     * 通过openid查询绑定的用水户列表
     * @param openid
     * @return
     */
    @Override
    public List<WechatUserBindingVo> queryBindingUserListByOpenid(String openid, HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(openid)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            throw new RuntimeException("openid不能为空");
        }
        return baseMapper.queryBindingUserListByOpenid(openid);
    }

    /**
     * 根据openid和userNo查询绑定的用水户信息
     * @param openid
     * @param userNo
     * @return
     */
    @Override
    public WechatUserBindingVo queryBindingUserByOpenidAndUserNo(String openid, String userNo) {
        LambdaQueryWrapper<WechatUserBinding> lqw = Wrappers.lambdaQuery();
        lqw.eq(WechatUserBinding::getOpenid, openid);
        lqw.eq(WechatUserBinding::getUserNo, userNo);
        lqw.eq(WechatUserBinding::getDelFlag, 0);
        return baseMapper.selectVoOne(lqw);
    }


    /**
     * 查询微信账号绑定户号关系
     *
     * @param wechatUserId 主键
     * @return 微信账号绑定户号关系
     */
    @Override
    public WechatUserBinding queryById(Long wechatUserId){
        return baseMapper.selectById(wechatUserId);
    }

    /**
     * 分页查询微信账号绑定户号关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 微信账号绑定户号关系分页列表
     */
    @Override
    public TableDataInfo<WechatUserBindingVo> queryPageList(WechatUserBindingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WechatUserBinding> lqw = buildQueryWrapper(bo);
        Page<WechatUserBindingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的微信账号绑定户号关系列表
     *
     * @param bo 查询条件
     * @return 微信账号绑定户号关系列表
     */
    @Override
    public List<WechatUserBindingVo> queryList(WechatUserBindingBo bo) {
        LambdaQueryWrapper<WechatUserBinding> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WechatUserBinding> buildQueryWrapper(WechatUserBindingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WechatUserBinding> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WechatUserBinding::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getOpenid()), WechatUserBinding::getOpenid, bo.getOpenid());
        lqw.eq(StringUtils.isNotBlank(bo.getUserNo()), WechatUserBinding::getUserNo, bo.getUserNo());
        return lqw;
    }

    /**
     * 新增微信账号绑定户号关系
     *
     * @param bo 微信账号绑定户号关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WechatUserBindingBo bo) {
        WechatUserBinding add = MapstructUtils.convert(bo, WechatUserBinding.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWechatUserId(add.getWechatUserId());
        }
        return flag;
    }

    /**
     * 修改微信账号绑定户号关系
     *
     * @param bo 微信账号绑定户号关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WechatUserBindingBo bo) {
        WechatUserBinding update = MapstructUtils.convert(bo, WechatUserBinding.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WechatUserBinding entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除微信账号绑定户号关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据用户编码查询用户信息
     *
     * @param userNo 用户编码
     * @return 用户信息（包含脱敏处理的姓名）
     */
    @Override
    public WechatUserInfoVo queryUserInfoByUserNo(String userNo) {
        if (StringUtils.isEmpty(userNo)) {
            return null;
        }

        // 调用用户服务查询用户信息
        WaterfeeUser user = waterfeeUserService.queryByUserNo(userNo);
        if (user == null) {
            return null;
        }

        // 创建返回对象并设置值
        WechatUserInfoVo userInfoVo = new WechatUserInfoVo();
        userInfoVo.setUserNo(user.getUserNo());
        userInfoVo.setAddress(user.getAddress());

        // 对姓名进行脱敏处理
        if (StringUtils.isNotEmpty(user.getUserName())) {
            userInfoVo.setUserName(DesensitizedUtil.chineseName(user.getUserName()));
        }

        return userInfoVo;
    }

    /**
     * 绑定用户
     * @param wechatUserBinding
     */
    @Override
    public Boolean bindUser(WechatUserBinding wechatUserBinding) {
        if(wechatUserBinding == null) {
            throw new RuntimeException("绑定信息不能为空");
        }
        if((StringUtils.isEmpty(wechatUserBinding.getOpenid()))) {
            throw new RuntimeException("openid不能为空");
        }
        if(StringUtils.isEmpty(wechatUserBinding.getUserNo())) {
            throw new RuntimeException("户号不能为空");
        }

        // 检查当前openid是否已经关联了对应的户号信息
        LambdaQueryWrapper<WechatUserBinding> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WechatUserBinding::getOpenid, wechatUserBinding.getOpenid())
                   .eq(WechatUserBinding::getUserNo, wechatUserBinding.getUserNo())
                    .eq(WechatUserBinding::getDelFlag, 0);
        Long count = baseMapper.selectCount(queryWrapper);
        if(count > 0) {
            throw new RuntimeException("该微信账号已绑定此户号，请勿重复绑定");
        }

        wechatUserBinding.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(wechatUserBinding) > 0;
    }

    /**
     * 解绑用户
     * @param wechatUserBinding
     * @return 是否解绑成功
     */
    @Override
    public Boolean unbindUser(WechatUserBinding wechatUserBinding) {
        if(wechatUserBinding == null) {
            throw new RuntimeException("解绑信息不能为空");
        }
        if(StringUtils.isEmpty(wechatUserBinding.getOpenid())){
            throw new RuntimeException("openid不能为空");
        }
        if(wechatUserBinding.getWechatUserId() == null) {
            throw new RuntimeException("微信绑定ID不能为空");
        }
        // 查询绑定关系
        WechatUserBinding binding = baseMapper.selectById(wechatUserBinding.getWechatUserId());
        if (binding == null) {
            throw new RuntimeException("未找到绑定关系");
        }
        if(binding.getOpenid().equals(wechatUserBinding.getOpenid())) {
            return baseMapper.deleteById(binding) > 0;
        }else {
            throw new RuntimeException("请使用绑定账号的openid信息来解绑");
        }
    }

    /**
     * 根据id查询用户余额
     * @return
     */
    public WechatUserBindingVo queryUserBalanceById(Long wechatUserId) {
        WechatUserBinding wechatUserBinding = baseMapper.selectById(wechatUserId);
        if(wechatUserBinding == null) {
            throw new RuntimeException("未找到绑定关系");
        }
        WechatUserBindingVo wechatUserBindingVo = new WechatUserBindingVo();
        BeanUtils.copyProperties(wechatUserBinding, wechatUserBindingVo);
        WaterfeeUser waterfeeUser = waterfeeUserService.queryByUserNo(wechatUserBindingVo.getUserNo());
        if(waterfeeUser == null) {
            throw new RuntimeException("未找到用户信息");
        }
        wechatUserBindingVo.setBalance(waterfeeUser.getBalance());
        return wechatUserBindingVo;
    }
}
