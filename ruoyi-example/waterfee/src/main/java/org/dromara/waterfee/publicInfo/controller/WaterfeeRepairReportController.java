package org.dromara.waterfee.publicInfo.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.repair.domain.WaterfeeRepairReport;
import org.dromara.waterfee.repair.domain.vo.WaterfeeRepairReportVo;
import org.dromara.waterfee.repair.domain.bo.WaterfeeRepairReportBo;
import org.dromara.waterfee.repair.service.IWaterfeeRepairReportService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 报修管理
 * 前端访问路由地址为:/waterfee/repairReport
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/repairReport")
public class WaterfeeRepairReportController extends BaseController {

    private final IWaterfeeRepairReportService waterfeeRepairReportService;

    /**
     * 查询报修管理列表
     */
    @SaCheckPermission("waterfee:repairReport:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeRepairReportVo> list(WaterfeeRepairReportBo bo, PageQuery pageQuery) {
        return waterfeeRepairReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报修管理列表
     */
    @SaCheckPermission("waterfee:repairReport:export")
    @Log(title = "报修管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeRepairReportBo bo, HttpServletResponse response) {
        List<WaterfeeRepairReportVo> list = waterfeeRepairReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "报修管理", WaterfeeRepairReportVo.class, response);
    }

    /**
     * 获取报修管理详细信息
     *
     * @param repairId 主键
     */
    @SaCheckPermission("waterfee:repairReport:query")
    @GetMapping("/{repairId}")
    public R<WaterfeeRepairReport> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("repairId") Long repairId) {
        return R.ok(waterfeeRepairReportService.queryById(repairId));
    }

    /**
     * 新增报修管理
     */
    @SaCheckPermission("waterfee:repairReport:add")
    @Log(title = "报修管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeRepairReportBo bo) {
        return toAjax(waterfeeRepairReportService.insertByBo(bo));
    }

    /**
     * 修改报修管理
     */
    @SaCheckPermission("waterfee:repairReport:edit")
    @Log(title = "报修管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeRepairReportBo bo) {
        return toAjax(waterfeeRepairReportService.updateByBo(bo));
    }

    /**
     * 删除报修管理
     *
     * @param repairIds 主键串
     */
    @SaCheckPermission("waterfee:repairReport:remove")
    @Log(title = "报修管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{repairIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("repairIds") Long[] repairIds) {
        return toAjax(waterfeeRepairReportService.deleteWithValidByIds(List.of(repairIds), true));
    }
}
