package org.dromara.waterfee.user.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.statisticalReport.domain.NatureStatisticsUserVO;
import org.dromara.waterfee.statisticalReport.domain.NewAccountUserDetailsVO;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 用水用户管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Mapper
public interface WaterfeeUserMapper  extends BaseMapperPlus<WaterfeeUser, WaterfeeUserVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeUserVo>> P selectVoPage(IPage<WaterfeeUser> page, Wrapper<WaterfeeUser> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
     * 查询用水用户管理列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeUserVo> queryList(@Param("page") Page<WaterfeeUser> page, @Param("query") WaterfeeUserBo query);

    /**
     * 批量逻辑删除用户
     *
     * @param ids 需要删除的用户ID集合
     * @return 影响行数
     */
//    int logicDeleteByIds(@Param("ids") Collection<Long> ids);

    List<WaterfeeUser> selectUserByUserNameNu(@Param("keyword") String keyword);

    /**
     * 报表统计 - 按性质统计用户明细表（搜索条件是用户性质/不包括销户和停户的户数）
     * @param customerNature
     * @return
     */
    List<NatureStatisticsUserVO> getNatureStatisticsUser(String customerNature);

    /**
     * 新开户用户明细表
     * @param year
     * @return
     */
    List<NewAccountUserDetailsVO> getNewAccountUserDetails(String year);
}
