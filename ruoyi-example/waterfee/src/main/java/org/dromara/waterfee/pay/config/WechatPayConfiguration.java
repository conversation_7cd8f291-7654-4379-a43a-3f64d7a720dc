package org.dromara.waterfee.pay.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * 微信支付配置类
 * <AUTHOR>
 */

@Configuration
public class WechatPayConfiguration {

    /** 商户号 */
    @Value("${wechatPay.merchant-id}")
    private String merchantId;

    /** 商户API私钥路径 */
//    @Value("${wechatPay.private-key-path}")
//    private String privateKeyPath;
        /** 商户API私钥  开发调试使用 生产使用路径挂载*/
    private String privateKey="-----BEGIN PRIVATE KEY-----\n" +
        "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDVoH4wtpAsgR7C\n" +
        "60xBstwM+OucVJBEkxteQ4uUF9tV41UJd9I7wwzuk//Vuu3VggYvocXd2gezZ8j7\n" +
        "R/2BAThlUMPdcxha0pK7AzSTwfXDebXsy51FSvXGSd6+7apar28j8IpYVUUl9rws\n" +
        "7esYNt7wKqQwXAUX8Op9VtOxU1GE+VkteggshJA/ziqwjp4OlGMsEFsQ2hS1NcOa\n" +
        "dRHVgpnev/0WOjIDIi3L6hFqLRslXaSFMCLhqqtWDmZ75meI0MrOno6v7yMfsuPa\n" +
        "NRKKzANO+b51aETOslKnSe77JzzP8Ii01GYNSTLLDrBsOau392cJ9eMvRAG8GC3W\n" +
        "NPzrCD13AgMBAAECggEBAJ6U11g+DeSbrUQw+5jBt4yBtBWGyNm/AlAFgDXdR9sI\n" +
        "OPYgVpY6gv0QLJhyfV9DeGgfhQzdb14EqzOuC0eaqky7mcpoSCoTqwzcokkVcOU9\n" +
        "EonH1OW9cZlS+OFu+Ej9dlmNlIViN0SpVqNbsmt1QCW04dFsXfOZydhlfhxip183\n" +
        "hyEfEvkWgE1FitW58EBZc3UujiqaRSJpHsP2L6Dbc4fCIm5y+zcDpkREqAnS3xOn\n" +
        "4PTml0Qgq4PviE9psJK4JOAFEW3G4vBJr6oZZBvE/8gc9LVIUllweqFaq6G2kPAs\n" +
        "TD7fCze9a4dUrH6pKryGwFfa+7iaQzavf19wt+fasyECgYEA9QxI9EEl5Mn3V0bh\n" +
        "1xdn1u4PxDFoUyEZDau6fZJ/RHdYmFRsFPWlz8Rlk64tMox8bXcOoa1Eh8YAGyC8\n" +
        "o1yTF6vW1Az7qz9fmX1SbhmWp1gKO087nqI5Lj33IlUe8uEgrBWY7FBVodyaJuaz\n" +
        "yuDCuWBgTITDKMR43UqgURnPFo8CgYEA3yy0PWTtMOhihdqIJz0r9aCynoyjVjqH\n" +
        "e4dAFzgFJvm7w8QaPIfK/z0famNx0+oZob61HZb9VKz94ssupkrnlS7qMtQTfU+i\n" +
        "0l4ZvueD3yyCmqNFA7P/ttW3IakgHK+GEyQ4IN43BO9sIRq8da9yvsgz3793rpTe\n" +
        "1o4K/LPxHpkCgYAN66u9THIlpB+L8rfjqy239QAqShA+ILQ2pZnLhmX6crkytiEJ\n" +
        "s8a+nc6TbCKfK4HqCghnwNsSx3m8EMkGkry8bIOgujgVy1zX8FmdbZC0Yvj50490\n" +
        "fIy5BsYiSime7JEZlYSBcDLdJhYPva1bChelxkbSMfyQNVEUaSfgU5IDGwKBgDDA\n" +
        "Jy8yGM4SNVNgBVXs+ulRDTOtLRDtACNvzARGbHmzG90LvV9zmlhfFbm7t2W/QZIf\n" +
        "gpjQHs6ca2lbUPlqa2QfN3Ga+hbpinvBqoYVNK1Pn8f/2leQvk9GKBbmRul9PiB4\n" +
        "TPqBECu30Tr6CCj9Bme+e/ZocNBmHLdpl+TTudwhAoGBAL9VoWEAJ/memHfR5xf9\n" +
        "ANmINJWR3EnQGKUlqQNwuDCFwfs6tOIo6Hom3hitsIB3FQ/9s4GN0WTkixqbKeQW\n" +
        "cju4jdIs8baEZpQ7jk02sRp8I6zAF1LSudXpR0liH/HvwK7CqVj9anesrXvmuYP3\n" +
        "/+gaXO1HPF0REsxv3BQh58Yg\n" +
        "-----END PRIVATE KEY-----";

    /** 商户证书序列号 */
    @Value("${wechatPay.merchant-serial-number}")
    private String merchantSerialNumber;

    /** 商户APIV3密钥 */
    @Value("${wechatPay.api-v3-key}")
    private String apiV3Key;

    @Bean
    public Config wechatPayConfig() throws IOException {
        return new RSAAutoCertificateConfig.Builder()
            .merchantId(merchantId)
//            .privateKeyFromPath(privateKeyPath)
            .privateKey(privateKey)
            .merchantSerialNumber(merchantSerialNumber)
            .apiV3Key(apiV3Key)
            .build();
    }

    @Bean
    public NotificationConfig notificationConfig() {
        return new RSAAutoCertificateConfig.Builder()
            .merchantId(merchantId)
//            .privateKeyFromPath(privateKeyPath)
            .privateKey(privateKey)
            .merchantSerialNumber(merchantSerialNumber)
            .apiV3Key(apiV3Key)
            .build();
    }
}
