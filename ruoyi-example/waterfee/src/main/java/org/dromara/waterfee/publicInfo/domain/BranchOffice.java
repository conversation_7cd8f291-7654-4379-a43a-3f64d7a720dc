package org.dromara.waterfee.branch.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 营业网点对象 branch_office
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_office")
public class BranchOffice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "branch_id")
    private Long branchId;

    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 经度
     */
    private String lon;

    /**
     * 维度
     */
    private String lat;

    /**
     * 备注
     */
    private String remark;


}
