package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 违约金调整/减免业务对象 waterfee_charge_manage_penalty_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManagePenaltyAdjustment.class, reverseConvertGenerate = false)
public class WaterfeeChargeManagePenaltyAdjustmentBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private Long billId;

    /**
     * 原违约金
     */
    @NotNull(message = "原违约金不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long originalPenalty;

    /**
     * 调整后违约金
     */
    @NotNull(message = "调整后违约金不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long adjustedPenalty;

    /**
     * 
     */
    private String reason;

    /**
     * 
     */
    private String remark;


}
