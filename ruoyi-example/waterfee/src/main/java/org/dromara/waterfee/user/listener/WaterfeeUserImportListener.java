package org.dromara.waterfee.user.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.BusinessStatusEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.excel.core.ExcelListener;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserImportVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.dao.DuplicateKeyException;

import java.util.List;

/**
 * 用水用户导入监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class WaterfeeUserImportListener extends AnalysisEventListener<WaterfeeUserImportVo> implements ExcelListener<WaterfeeUserImportVo> {

    private final IWaterfeeUserService userService;

    private final Boolean isUpdateSupport;

    private final Long operUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public WaterfeeUserImportListener(Boolean isUpdateSupport) {
        this.userService = SpringUtils.getBean(IWaterfeeUserService.class);
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(WaterfeeUserImportVo userVo, AnalysisContext context) {
//        WaterfeeUser meterUser = userService.selectUserByMeterNo(userVo.getMeterNo());
//        WaterfeeUser waterfeeUser = this.userService.selectUserByCertificateTypeAndCertificateNumberAndMeterNo(userVo.getCertificateType() ,userVo.getCertificateNumber(), userVo.getMeterNo());
        WaterfeeUser meterUser = null;
        WaterfeeUser waterfeeUser = null;
        try {
            //判断当前水表是否已绑定用户
            if(!ObjectUtil.isNull(meterUser) && ObjectUtil.isNull(waterfeeUser)) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、用户 ").append(meterUser.getUserName()).append("填写的水表编号").append(userVo.getMeterNo()).append("已与其他用户绑定");
            } else if (ObjectUtil.isNull(waterfeeUser)) {
                WaterfeeUserBo user = BeanUtil.toBean(userVo, WaterfeeUserBo.class);
                ValidatorUtils.validate(user);
                user.setCreateBy(operUserId);
                user.setAuditStatus(BusinessStatusEnum.FINISH.getStatus());
                //新用户生成用户编号
                boolean flag = false;
                //增加重试机制防止编号重复导致新增失败
//                int retryCount = 3;
//                while (retryCount-- > 0) {
//                    try {
//                        // 生成用户编号
//                        String userNo = userService.generateUserNo(user.getAreaId(), user.getCommunityId());
//                        user.setUserNo(userNo);
//                        flag = userService.insertByBo(user);
//                        break;
//                    }catch (DuplicateKeyException e) {
//                        throw new RuntimeException("生成用户编号失败，请重试");
//                    }
//                }
                flag = userService.insertByBo(user);
                if (!flag) {
                    throw new ServiceException("新增用水用户基本信息失败");
                }
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、用户 ").append(user.getUserName()).append(" 导入成功");
            } else if (!ObjectUtil.isNull(waterfeeUser) && isUpdateSupport && waterfeeUser.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                Long userId = waterfeeUser.getUserId();
                WaterfeeUserBo user = BeanUtil.toBean(userVo, WaterfeeUserBo.class);
                user.setUserId(userId);
                ValidatorUtils.validate(user);
                user.setUpdateBy(operUserId);
                userService.updateByBo(user);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、用户 ").append(user.getUserName()).append(" 更新成功");
            } else if(!ObjectUtil.isNull(waterfeeUser) && isUpdateSupport && !waterfeeUser.getAuditStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、用户 ").append(meterUser.getUserName()).append("证件号").append(userVo.getCertificateNumber()).append("已提交了相关工单，工单执行中不支持更新信息");
            } else {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、用户 ").append(waterfeeUser.getUserName()).append(" 已存在");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、用户 " + HtmlUtil.cleanHtmlTag(userVo.getUserName()) + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<WaterfeeUserImportVo> getExcelResult() {
        return new ExcelResult<WaterfeeUserImportVo>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<WaterfeeUserImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
