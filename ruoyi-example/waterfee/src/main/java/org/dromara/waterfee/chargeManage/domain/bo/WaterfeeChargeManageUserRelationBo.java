package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 依托用户关系维护业务对象 waterfee_charge_manage_user_relation
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageUserRelation.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageUserRelationBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 主用户ID
     */
    @NotNull(message = "主用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long masterUserId;

    /**
     * 附属用户ID
     */
    @NotNull(message = "附属用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attachedUserId;

    /**
     * 关系类型（单位-职工、业主-租户等）
     */
    private String relationType;

    /**
     * 
     */
    private String remark;


}
