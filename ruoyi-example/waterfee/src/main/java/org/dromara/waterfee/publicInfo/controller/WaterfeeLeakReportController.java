package org.dromara.waterfee.publicInfo.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.leak.domain.WaterfeeLeakReport;
import org.dromara.waterfee.leak.domain.vo.WaterfeeLeakReportVo;
import org.dromara.waterfee.leak.domain.bo.WaterfeeLeakReportBo;
import org.dromara.waterfee.leak.service.IWaterfeeLeakReportService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 偷漏水举报
 * 前端访问路由地址为:/waterfee/leakReport
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/leakReport")
public class WaterfeeLeakReportController extends BaseController {

    private final IWaterfeeLeakReportService waterfeeLeakReportService;

    /**
     * 查询偷漏水举报列表
     */
    @SaCheckPermission("waterfee:leakReport:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeLeakReportVo> list(WaterfeeLeakReportBo bo, PageQuery pageQuery) {
        return waterfeeLeakReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出偷漏水举报列表
     */
    @SaCheckPermission("waterfee:leakReport:export")
    @Log(title = "偷漏水举报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeLeakReportBo bo, HttpServletResponse response) {
        List<WaterfeeLeakReportVo> list = waterfeeLeakReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "偷漏水举报", WaterfeeLeakReportVo.class, response);
    }

    /**
     * 获取偷漏水举报详细信息
     *
     * @param reportId 主键
     */
    @SaCheckPermission("waterfee:leakReport:query")
    @GetMapping("/{reportId}")
    public R<WaterfeeLeakReport> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("reportId") Long reportId) {
        return R.ok(waterfeeLeakReportService.queryById(reportId));
    }

    /**
     * 新增偷漏水举报
     */
    @SaCheckPermission("waterfee:leakReport:add")
    @Log(title = "偷漏水举报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeLeakReportBo bo) {
        return toAjax(waterfeeLeakReportService.insertByBo(bo));
    }

    /**
     * 修改偷漏水举报
     */
    @SaCheckPermission("waterfee:leakReport:edit")
    @Log(title = "偷漏水举报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeLeakReportBo bo) {
        return toAjax(waterfeeLeakReportService.updateByBo(bo));
    }

    /**
     * 删除偷漏水举报
     *
     * @param reportIds 主键串
     */
    @SaCheckPermission("waterfee:leakReport:remove")
    @Log(title = "偷漏水举报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{reportIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("reportIds") Long[] reportIds) {
        return toAjax(waterfeeLeakReportService.deleteWithValidByIds(List.of(reportIds), true));
    }
}
