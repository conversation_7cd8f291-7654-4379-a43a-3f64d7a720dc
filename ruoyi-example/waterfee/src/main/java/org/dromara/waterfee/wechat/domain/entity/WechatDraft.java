package org.dromara.waterfee.wechat.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 微信草稿对象 wechat_draft
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wechat_draft")
public class WechatDraft extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 草稿ID
     */
    @TableId(type = IdType.AUTO)
    private Long draftId;

    /**
     * 微信草稿媒体ID
     */
    private String mediaId;

    /**
     * 草稿标题
     */
    private String title;

    /**
     * 草稿类型（WATER_OUTAGE-停水通知 WATER_SUPPLY-供水通知）
     */
    private String draftType;

    /**
     * 草稿内容
     */
    private String content;

    /**
     * 封面图片媒体ID
     */
    private String thumbMediaId;

    /**
     * 封面图片URL
     */
    private String thumbUrl;

    /**
     * 作者
     */
    private String author;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 是否显示封面（0-不显示 1-显示）
     */
//    private Boolean showCoverPic;

    /**
     * 原文链接
     */
    private String contentSourceUrl;

    /**
     * 是否开启评论（0-不开启 1-开启）
     */
    private Boolean needOpenComment;

    /**
     * 是否粉丝才可评论（0-所有人 1-仅粉丝）
     */
    private Boolean onlyFansCanComment;

    /**
     * 草稿状态（DRAFT-草稿 PUBLISHED-已发布）
     */
    private String draftStatus;

    /**
     * 文章状态（UNPUBLISHED-未发布文章 PUBLISHED-已发布文章）
     */
    private String articleStatus;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 文章发布时间
     */
    private Date articlePublishTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;
}
