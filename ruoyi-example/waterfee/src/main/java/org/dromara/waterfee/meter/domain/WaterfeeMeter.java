package org.dromara.waterfee.meter.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 水表信息对象 waterfee_meter
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter")
public class WaterfeeMeter extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 水表ID
     */
    @TableId(value = "meter_id")
    private Long meterId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 水表类型(1-机械表、2-智能表)
     */
    private Integer meterType;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 水表类别(自来水表、中水表、直饮水表)
     */
    private String meterCategory;

    /**
     * 水表分类(总表、分表)
     */
    private String meterClassification;

    /**
     * 计量用途(居民用水、非居民用水)
     */
    private String measurementPurpose;

    /**
     * 口径
     */
    private String caliber;

    /**
     * 精度
     */
    private String accuracy;

    /**
     * 初始读数
     */
    private BigDecimal initialReading;

    /**
     * 安装日期
     */
    private Date installDate;

    /**
     * 业务区域ID
     */
    private Long businessAreaId;

    /**
     * 抄表手册ID
     */
    private Long meterBookId;

    /**
     * 排序号（手册内排序）
     */
    private Integer sortNo;

    /**
     * 安装地址
     */
    private String installAddress;

    /**
     * 表倍率
     */
    private BigDecimal meterRatio;

    /**
     * 通讯方式(NB-IoT、4G、Cat.1、LoRa)
     */
    private String communicationMode;

    /**
     * 阀控功能(0-无、1-有)
     */
    private Integer valveControl;

    /**
     * IMEI号
     */
    private String imei;

    /**
     * IMSI号
     */
    private String imsi;

    /**
     * 物联网平台
     */
    private String iotPlatform;

    /**
     * 是否预付费(0-否、1-是)
     */
    private Integer prepaid;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 关联用户编号
     */
    private String userNo;

    /**
     * 用水性质(居民、商业、工业、特种、洗浴)
     */
    private String waterNature;

    /**
     * 价格名称
     */
    private String priceName;

    /**
     * 是否启用违约金
     */
    private Boolean penaltyEnabled;

    /**
     * 违约金类型(固定金额、固定利率、日利率)
     */
    private String penaltyType;

    /**
     * 违约金值
     */
    private BigDecimal penaltyValue;

    /**
     * 是否启用附加费
     */
    private Boolean additionalFeesEnabled;

    /**
     * 附加费项目(JSON格式)
     */
    private String additionalFeesItems;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注信息
     */
    private String remark;
}
