package org.dromara.waterfee.bill.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 表册账单汇总视图对象
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@ExcelIgnoreUnannotated
public class MeterBookBillSummaryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 表册ID
     */
    @ExcelProperty(value = "表册ID")
    private Long meterBookId;

    /**
     * 表册编号
     */
    @ExcelProperty(value = "表册编号")
    private String meterBookNo;

    /**
     * 表册名称
     */
    @ExcelProperty(value = "表册名称")
    private String meterBookName;

    /**
     * 抄表员ID
     */
    @ExcelProperty(value = "抄表员ID")
    private Long meterReader;

    /**
     * 抄表员名称
     */
    @ExcelProperty(value = "抄表员名称")
    private String meterReaderName;

    /**
     * 账单月份
     */
    @ExcelProperty(value = "账单月份")
    private String billMonth;

    /**
     * 账单总数
     */
    @ExcelProperty(value = "账单总数")
    private Integer totalBills;

    /**
     * 总计用户数
     */
    @ExcelProperty(value = "总计用户数")
    private Integer totalUsers;

    /**
     * 草稿账单数
     */
    @ExcelProperty(value = "草稿账单数")
    private Integer draftBills;

    /**
     * 已发行账单数
     */
    @ExcelProperty(value = "已发行账单数")
    private Integer issuedBills;

    /**
     * 已支付账单数
     */
    @ExcelProperty(value = "已支付账单数")
    private Integer paidBills;

    /**
     * 已逾期账单数
     */
    @ExcelProperty(value = "已逾期账单数")
    private Integer overdueBills;

    /**
     * 总金额
     */
    @ExcelProperty(value = "总金额")
    private BigDecimal totalAmount;

    /**
     * 已收金额
     */
    @ExcelProperty(value = "已收金额")
    private BigDecimal paidAmount;

    /**
     * 未收金额
     */
    @ExcelProperty(value = "未收金额")
    private BigDecimal unpaidAmount;

    /**
     * 发行状态
     */
    @ExcelProperty(value = "发行状态")
    private String executeStatus;

    /**
     * 发行人
     */
    @ExcelProperty(value = "发行人")
    private String executor;

    /**
     * 发行时间
     */
    @ExcelProperty(value = "发行时间")
    private Date executeTime;
}
