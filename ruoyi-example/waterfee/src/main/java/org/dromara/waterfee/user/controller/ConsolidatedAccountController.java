package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.ConsolidatedAccount;
import org.dromara.waterfee.user.domain.vo.ConsolidatedAccountVo;
import org.dromara.waterfee.user.domain.bo.ConsolidatedAccountBo;
import org.dromara.waterfee.user.service.IConsolidatedAccountService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 合收户管理
 * 前端访问路由地址为:/waterfee/consolidatedAccount
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/consolidatedAccount")
public class ConsolidatedAccountController extends BaseController {

    private final IConsolidatedAccountService consolidatedAccountService;

    /**
     * 查询合收户管理列表
     */
    @SaCheckPermission("waterfee:consolidatedAccount:list")
    @GetMapping("/list")
    public TableDataInfo<ConsolidatedAccountVo> list(ConsolidatedAccountBo bo, PageQuery pageQuery) {
        return consolidatedAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出合收户管理列表
     */
    @SaCheckPermission("waterfee:consolidatedAccount:export")
    @Log(title = "合收户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ConsolidatedAccountBo bo, HttpServletResponse response) {
        List<ConsolidatedAccountVo> list = consolidatedAccountService.queryList(bo);
        ExcelUtil.exportExcel(list, "合收户管理", ConsolidatedAccountVo.class, response);
    }

    /**
     * 获取合收户管理详细信息
     *
     * @param consolidatedAccountId 主键
     */
    @SaCheckPermission("waterfee:consolidatedAccount:query")
    @GetMapping("/{consolidatedAccountId}")
    public R<ConsolidatedAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("consolidatedAccountId") Long consolidatedAccountId) {
        return R.ok(consolidatedAccountService.getDetailById(consolidatedAccountId));
    }

    /**
     * 新增合收户管理
     */
    @SaCheckPermission("waterfee:consolidatedAccount:add")
    @Log(title = "合收户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ConsolidatedAccountBo bo) {
        return toAjax(consolidatedAccountService.insertByBo(bo));
    }

    /**
     * 修改合收户管理
     */
    @SaCheckPermission("waterfee:consolidatedAccount:edit")
    @Log(title = "合收户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ConsolidatedAccountBo bo) {
        return toAjax(consolidatedAccountService.updateByBo(bo));
    }

    /**
     * 删除合收户管理
     *
     * @param consolidatedAccountIds 主键串
     */
    @SaCheckPermission("waterfee:consolidatedAccount:remove")
    @Log(title = "合收户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{consolidatedAccountIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("consolidatedAccountIds") Long[] consolidatedAccountIds) {
        return toAjax(consolidatedAccountService.deleteWithValidByIds(List.of(consolidatedAccountIds), true));
    }
}
