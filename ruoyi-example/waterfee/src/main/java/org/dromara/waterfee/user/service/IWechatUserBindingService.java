package org.dromara.waterfee.user.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.WechatUserBinding;
import org.dromara.waterfee.user.domain.vo.WechatUserBindingVo;
import org.dromara.waterfee.user.domain.bo.WechatUserBindingBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.waterfee.user.domain.vo.WechatUserInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 微信账号绑定户号关系Service接口
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface IWechatUserBindingService {

    /**
     * 通过openid查询绑定的用水户列表
     * @param openid
     * @return
     */
    List<WechatUserBindingVo> queryBindingUserListByOpenid(String openid, HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据openid和userNo查询绑定的用水户信息
     * @param openid
     * @param userNo
     * @return
     */
    WechatUserBindingVo queryBindingUserByOpenidAndUserNo(String openid, String userNo);

    /**
     * 查询微信账号绑定户号关系
     *
     * @param wechatUserId 主键
     * @return 微信账号绑定户号关系
     */
    WechatUserBinding queryById(Long wechatUserId);

    /**
     * 分页查询微信账号绑定户号关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 微信账号绑定户号关系分页列表
     */
    TableDataInfo<WechatUserBindingVo> queryPageList(WechatUserBindingBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的微信账号绑定户号关系列表
     *
     * @param bo 查询条件
     * @return 微信账号绑定户号关系列表
     */
    List<WechatUserBindingVo> queryList(WechatUserBindingBo bo);

    /**
     * 新增微信账号绑定户号关系
     *
     * @param bo 微信账号绑定户号关系
     * @return 是否新增成功
     */
    Boolean insertByBo(WechatUserBindingBo bo);

    /**
     * 修改微信账号绑定户号关系
     *
     * @param bo 微信账号绑定户号关系
     * @return 是否修改成功
     */
    Boolean updateByBo(WechatUserBindingBo bo);

    /**
     * 校验并批量删除微信账号绑定户号关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据用户编码查询用户信息
     *
     * @param userNo 用户编码
     * @return 用户信息（包含脱敏处理的姓名）
     */
    WechatUserInfoVo queryUserInfoByUserNo(String userNo);

    /**
     * 绑定用户
     * @param wechatUserBinding
     */
    Boolean bindUser(WechatUserBinding wechatUserBinding);

    /**
     * 解绑用户
     * @param wechatUserBinding
     * @return 是否解绑成功
     */
    Boolean unbindUser(WechatUserBinding wechatUserBinding);

    /**
     * 查询用户余额
     * @return
     */
    WechatUserBindingVo queryUserBalanceById(Long wechatUserId);
}
