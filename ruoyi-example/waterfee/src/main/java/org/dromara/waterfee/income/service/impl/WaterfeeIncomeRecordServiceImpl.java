package org.dromara.waterfee.income.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.bill.domain.PaymentDetail;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.income.domain.WaterfeeIncomeRecord;
import org.dromara.waterfee.income.domain.bo.WaterfeeIncomeRecordBo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo;
import org.dromara.waterfee.income.domain.vo.WaterfeeIncomeSummaryVo;
import org.dromara.waterfee.income.mapper.WaterfeeIncomeRecordMapper;
import org.dromara.waterfee.income.service.IWaterfeeIncomeRecordService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.lock.UserLockManager;
import org.dromara.waterfee.user.service.IWaterfeeUserBalanceChangeRecordService;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 收入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeeIncomeRecordServiceImpl implements IWaterfeeIncomeRecordService {

    private final WaterfeeIncomeRecordMapper baseMapper;
    private final PaymentDetailMapper paymentDetailMapper;
    private final IWaterfeeUserService userMapper;
    private final UserLockManager userLockManager;
    private final IWaterfeeUserBalanceChangeRecordService balanceChangeRecordService;

    /**
     * 查询收入记录
     *
     * @param incomeId 收入记录主键
     * @return 收入记录
     */
    @Override
    public WaterfeeIncomeRecordVo queryById(Long incomeId) {
        return baseMapper.selectVoById(incomeId);
    }

    /**
     * 查询收入记录列表
     *
     * @param bo 收入记录
     * @return 收入记录
     */
    @Override
    public TableDataInfo<WaterfeeIncomeRecordVo> queryPageList(WaterfeeIncomeRecordBo bo, PageQuery pageQuery) {
        // 从PaymentDetail表查询数据并转换为WaterfeeIncomeRecordVo
        LambdaQueryWrapper<PaymentDetail> lqw = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(bo.getUserNo())) {
            // 通过用户编号关联查询
            lqw.exists("SELECT 1 FROM waterfee_user u WHERE u.user_id = waterfee_payment_detail.user_id AND u.user_no = {0}", bo.getUserNo());
        }

        if (StringUtils.isNotBlank(bo.getIncomeType())) {
            lqw.eq(PaymentDetail::getIncomeType, bo.getIncomeType());
        }

        if (StringUtils.isNotBlank(bo.getCollectorName())) {
            lqw.like(PaymentDetail::getTollCollector, bo.getCollectorName());
        }

        if (bo.getStartTime() != null) {
            lqw.ge(PaymentDetail::getIncomeTime, bo.getStartTime());
        }

        if (bo.getEndTime() != null) {
            lqw.le(PaymentDetail::getIncomeTime, bo.getEndTime());
        }

        if (StringUtils.isNotBlank(bo.getPayMethod())) {
            String paymentMethod = convertPayMethodToPaymentMethod(bo.getPayMethod());
            lqw.eq(PaymentDetail::getPaymentMethod, paymentMethod);
        }

        // 只查询成功的支付记录
        lqw.eq(PaymentDetail::getPaymentStatus, "SUCCESS");
        lqw.orderByDesc(PaymentDetail::getIncomeTime);

        Page<PaymentDetail> page = paymentDetailMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO
        List<WaterfeeIncomeRecordVo> voList = page.getRecords().stream()
            .map(this::convertToIncomeVo)
            .collect(Collectors.toList());

        return TableDataInfo.build(voList);
    }

    /**
     * 查询收入记录列表
     *
     * @param bo 收入记录
     * @return 收入记录
     */
    @Override
    public List<WaterfeeIncomeRecordVo> queryList(WaterfeeIncomeRecordBo bo) {
        LambdaQueryWrapper<PaymentDetail> lqw = buildQueryWrapper(bo);
        List<PaymentDetail> list = paymentDetailMapper.selectList(lqw);

        return list.stream()
            .map(this::convertToIncomeVo)
            .collect(Collectors.toList());
    }

    private LambdaQueryWrapper<PaymentDetail> buildQueryWrapper(WaterfeeIncomeRecordBo bo) {
        LambdaQueryWrapper<PaymentDetail> lqw = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(bo.getUserNo())) {
            lqw.exists("SELECT 1 FROM waterfee_user u WHERE u.user_id = waterfee_payment_detail.user_id AND u.user_no = {0}", bo.getUserNo());
        }

        lqw.eq(StringUtils.isNotBlank(bo.getIncomeType()), PaymentDetail::getIncomeType, bo.getIncomeType());
        lqw.like(StringUtils.isNotBlank(bo.getCollectorName()), PaymentDetail::getTollCollector, bo.getCollectorName());
        lqw.ge(bo.getStartTime() != null, PaymentDetail::getIncomeTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, PaymentDetail::getIncomeTime, bo.getEndTime());

        if (StringUtils.isNotBlank(bo.getPayMethod())) {
            String paymentMethod = convertPayMethodToPaymentMethod(bo.getPayMethod());
            lqw.eq(PaymentDetail::getPaymentMethod, paymentMethod);
        }

        // 只查询成功的支付记录
        lqw.eq(PaymentDetail::getPaymentStatus, "SUCCESS");
        lqw.orderByDesc(PaymentDetail::getIncomeTime);

        return lqw;
    }

    /**
     * 新增收入记录
     *
     * @param bo 收入记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(WaterfeeIncomeRecordBo bo) {
        // 如果收入时间为空，设置为当前时间
        if (bo.getIncomeTime() == null) {
            bo.setIncomeTime(new Date());
        }

        // 如果是预存款相关操作，需要更新用户余额
        boolean needUpdateBalance = "prestore".equals(bo.getIncomeType()) ||
            ("refund".equals(bo.getIncomeType()) && "deposit".equals(bo.getPayMethod()));

        Long userId = null;
        String userNo = bo.getUserNo();

        if (needUpdateBalance && StringUtils.isNotBlank(userNo)) {
            // 查询用户信息
            WaterfeeUser user = userMapper.queryByUserNo(userNo);

            if (user == null) {
                throw new ServiceException("用户不存在：" + userNo);
            }

            userId = user.getUserId();

            // 获取用户锁
            ReentrantLock lock = userLockManager.getUserLock(userNo);
            try {
                // 尝试获取锁，最多等待3秒
                if (lock.tryLock(3, TimeUnit.SECONDS)) {
                    try {
                        // 重新查询用户信息（确保获取最新余额）
                        user = userMapper.queryById(userId);
                        if (user == null) {
                            throw new ServiceException("用户不存在：" + userId);
                        }

                        // 更新用户余额
                        BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
                        BigDecimal newBalance;

                        if ("prestore".equals(bo.getIncomeType())) {
                            // 充值，增加余额
                            newBalance = currentBalance.add(bo.getAmount());
                        } else {
                            // 退款，增加余额
                            newBalance = currentBalance.add(bo.getAmount());
                        }

                        WaterfeeUser updateUser = new WaterfeeUser();
                        updateUser.setUserId(userId);
                        updateUser.setBalance(newBalance);
                        updateUser.setUpdateBy(LoginHelper.getUserId());
                        updateUser.setUpdateTime(DateUtils.getNowDate());
                        boolean rows = userMapper.updateById(updateUser);

                        if (rows) {
                            log.info("用户余额更新成功，户号：{}，当前余额：{}，操作金额：{}，更新后余额：{}",
                                userNo, currentBalance, bo.getAmount(), newBalance);
                        } else {
                            log.error("用户余额更新失败，户号：{}", userNo);
                            throw new ServiceException("更新用户余额失败");
                        }

                        // 创建PaymentDetail记录
                        PaymentDetail paymentDetail = new PaymentDetail();
                        paymentDetail.setPaymentDetailId(generatePaymentNumber());
                        paymentDetail.setUserId(userId);
                        paymentDetail.setPaymentAmount(bo.getAmount());
                        paymentDetail.setPaymentTime(bo.getIncomeTime());
                        paymentDetail.setPaymentMethod(convertPayMethodToPaymentMethod(bo.getPayMethod()));
                        paymentDetail.setPaymentStatus("SUCCESS");
                        paymentDetail.setRemark(bo.getRemark());

                        // 设置income相关字段
                        paymentDetail.setIncomeType(bo.getIncomeType());
                        paymentDetail.setTollCollector(bo.getCollectorName());
                        paymentDetail.setIncomeTime(bo.getIncomeTime());

                        boolean flag = paymentDetailMapper.insert(paymentDetail) > 0;
                        if (flag) {
                            bo.setIncomeId(Long.valueOf(paymentDetail.getPaymentDetailId()));
                        }

                        // 记录余额变更
                        String changeType = "prestore".equals(bo.getIncomeType()) ? "RECHARGE" : "REFUND";
                        String businessType = "prestore".equals(bo.getIncomeType()) ? "DEPOSIT" : "REFUND";
                        String changeReason = "prestore".equals(bo.getIncomeType()) ? "预存充值" : "退款到预存款";

                        balanceChangeRecordService.recordBalanceChange(
                            userId, userNo, changeType,
                            currentBalance, bo.getAmount(), newBalance,
                            paymentDetail.getPaymentDetailId(), businessType, changeReason,
                            bo.getCollectorName(), bo.getRemark()
                        );

                        return flag;
                    } finally {
                        // 释放锁
                        lock.unlock();
                    }
                } else {
                    log.warn("获取用户余额锁超时，户号：{}", userNo);
                    throw new ServiceException("系统繁忙，请稍后再试");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("更新用户余额时被中断，户号：{}", userNo, e);
                throw new ServiceException("操作被中断");
            }
        }
        return false;
    }

    /**
     * 修改收入记录
     *
     * @param bo 收入记录
     * @return 结果
     */
    @Override
    public Boolean updateByBo(WaterfeeIncomeRecordBo bo) {
        WaterfeeIncomeRecord update = MapstructUtils.convert(bo, WaterfeeIncomeRecord.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除收入记录
     *
     * @param ids 需要删除的收入记录主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 进行业务校验，例如判断是否已关联其他业务
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据用户编号查询收入记录
     *
     * @param userNo 用户编号
     * @return 收入记录集合
     */
    @Override
    public List<WaterfeeIncomeRecordVo> queryListByUserNo(String userNo) {
        LambdaQueryWrapper<PaymentDetail> lqw = new LambdaQueryWrapper<>();
        lqw.exists("SELECT 1 FROM waterfee_user u WHERE u.user_id = waterfee_payment_detail.user_id AND u.user_no = {0}", userNo);
        lqw.orderByDesc(PaymentDetail::getIncomeTime);

        List<PaymentDetail> list = paymentDetailMapper.selectList(lqw);
        return list.stream()
            .map(this::convertToIncomeVo)
            .collect(Collectors.toList());
    }

    /**
     * 统计不同收入类型的金额
     *
     * @return 收入统计对象
     */
    @Override
    public WaterfeeIncomeSummaryVo getIncomeSummary() {
        // 从PaymentDetail表统计不同收入类型的金额
        QueryWrapper<PaymentDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("income_type", "SUM(payment_amount) AS total_amount")
            .eq("payment_status", "SUCCESS")
            .groupBy("income_type");

        List<Map<String, Object>> summaryData = paymentDetailMapper.selectMaps(queryWrapper);

        WaterfeeIncomeSummaryVo summary = new WaterfeeIncomeSummaryVo();
        BigDecimal totalIncome = BigDecimal.ZERO;

        for (Map<String, Object> data : summaryData) {
            String incomeType = (String) data.get("income_type");
            BigDecimal amount = (BigDecimal) data.get("total_amount");

            if (amount != null) {
                totalIncome = totalIncome.add(amount);

                switch (incomeType) {
                    case "bill_payment" -> summary.setBillPaymentAmount(amount);
                    case "prestore" -> summary.setPrestoreAmount(amount);
                    case "refund" -> summary.setRefundAmount(amount);
                    default -> {
                        throw new IllegalArgumentException("未知收入类型: " + incomeType);
                    }
                }
            }
        }

        summary.setTotalIncomeAmount(totalIncome);
        return summary;
    }

    private WaterfeeIncomeRecordVo convertToIncomeVo(PaymentDetail detail) {
        WaterfeeIncomeRecordVo vo = new WaterfeeIncomeRecordVo();
        vo.setIncomeId(detail.getPaymentDetailId());
        vo.setIncomeType(detail.getIncomeType());
        vo.setCollectorName(detail.getTollCollector());
        vo.setIncomeTime(detail.getIncomeTime());
        vo.setAmount(detail.getPaymentAmount());
        vo.setPayMethod(convertPaymentMethodToPayMethod(detail.getPaymentMethod()));
        vo.setRemark(detail.getRemark());

        // 通过userId查询用户编号
        if (detail.getUserId() != null) {
            WaterfeeUser user = userMapper.queryById(detail.getUserId());
            if (user != null) {
                vo.setUserNo(user.getUserNo());
            }
        }

        return vo;
    }

    private String convertPaymentMethodToPayMethod(String paymentMethod) {
        return switch (paymentMethod) {
            case "WECHAT_PAY" -> "wechat";
            case "ALIPAY" -> "alipay";
            case "CASH" -> "cash";
            case "DEPOSIT" -> "deposit";
            case "BANK_TRANSFER" -> "bank";
            default -> "other";
        };
    }

    private String convertPayMethodToPaymentMethod(String payMethod) {
        return switch (payMethod) {
            case "wechat" -> "WECHAT_PAY";
            case "alipay" -> "ALIPAY";
            case "cash" -> "CASH";
            case "deposit" -> "DEPOSIT";
            case "bank" -> "BANK_TRANSFER";
            default -> "OTHER";
        };
    }

    private String generatePaymentNumber() {
        return "PAY" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }
}
