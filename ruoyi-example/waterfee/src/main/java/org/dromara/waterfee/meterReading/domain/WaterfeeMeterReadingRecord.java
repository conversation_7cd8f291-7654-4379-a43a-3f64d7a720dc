package org.dromara.waterfee.meterReading.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 抄表记录对象 waterfee_meter_reading_record
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter_reading_record")
public class WaterfeeMeterReadingRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private Long createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private Long updateBy;

    /**
     * 抄表记录ID
     */
    @TableId(value = "record_id")
    private Long recordId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 水表类型（字典waterfee_meter_type）
     */
    private String meterType;

    /**
     * 上期抄表读数
     */
    private Double lastReading;

    /**
     * 上期抄表时间
     */
    private Date lastReadingTime;

    /**
     * 本期抄表读数
     */
    private Double currentReading;

    /**
     * 旧表止数
     */
    private Double oldMeterStopReading;

    /**
     * 本期水量 = 旧表止数 + 本期读数 - 上期读数
     */
    private Double waterUsage;

    /**
     * 本期抄表时间
     */
    private Date readingTime;

    /**
     * 抄表手册ID
     */
    @ExcelProperty(value = "抄表手册ID")
    private Long meterBookId;

    /**
     * 抄表任务ID
     */
    private Long taskId;

    /**
     * 来源（normal-正常抄表 manual-人工补录）
     */
    private String sourceType;

    /**
     * 是否审核（0-未审核 1-已审核）
     */
    private String isAudited;

    /**
     * 补录ID（若是补录数据则关联）
     */
    private Long manualId;

    /**
     * 是否挂起（0-正常 1-已挂起）
     */
    private String isPending;

    /**
     * 挂起原因
     */
    private String pendingReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
