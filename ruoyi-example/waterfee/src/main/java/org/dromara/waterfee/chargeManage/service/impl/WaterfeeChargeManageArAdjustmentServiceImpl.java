package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArAdjustmentBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageArAdjustmentMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageArAdjustmentService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 应收账追补记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageArAdjustmentServiceImpl implements IWaterfeeChargeManageArAdjustmentService {

    private final WaterfeeChargeManageArAdjustmentMapper baseMapper;

    /**
     * 查询应收账追补记录
     *
     * @param id 主键
     * @return 应收账追补记录
     */
    @Override
    public WaterfeeChargeManageArAdjustment queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询应收账追补记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收账追补记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageArAdjustmentVo> queryPageList(WaterfeeChargeManageArAdjustmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageArAdjustment> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageArAdjustmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应收账追补记录列表
     *
     * @param bo 查询条件
     * @return 应收账追补记录列表
     */
    @Override
    public List<WaterfeeChargeManageArAdjustmentVo> queryList(WaterfeeChargeManageArAdjustmentBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageArAdjustment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageArAdjustment> buildQueryWrapper(WaterfeeChargeManageArAdjustmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageArAdjustment> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageArAdjustment::getCreateTime);
        lqw.eq(bo.getUserId() != null, WaterfeeChargeManageArAdjustment::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getMeterNo()), WaterfeeChargeManageArAdjustment::getMeterNo, bo.getMeterNo());
        lqw.eq(StringUtils.isNotBlank(bo.getBillPeriod()), WaterfeeChargeManageArAdjustment::getBillPeriod, bo.getBillPeriod());
        lqw.eq(bo.getOriginalAmount() != null, WaterfeeChargeManageArAdjustment::getOriginalAmount, bo.getOriginalAmount());
        lqw.eq(bo.getAdjustedAmount() != null, WaterfeeChargeManageArAdjustment::getAdjustedAmount, bo.getAdjustedAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), WaterfeeChargeManageArAdjustment::getReason, bo.getReason());
        return lqw;
    }

    /**
     * 新增应收账追补记录
     *
     * @param bo 应收账追补记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageArAdjustmentBo bo) {
        WaterfeeChargeManageArAdjustment add = MapstructUtils.convert(bo, WaterfeeChargeManageArAdjustment.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改应收账追补记录
     *
     * @param bo 应收账追补记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageArAdjustmentBo bo) {
        WaterfeeChargeManageArAdjustment update = MapstructUtils.convert(bo, WaterfeeChargeManageArAdjustment.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageArAdjustment entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应收账追补记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
