package org.dromara.waterfee.pay.service;

import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import com.wechat.pay.java.service.payments.model.Transaction;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.waterfee.pay.domain.WechatCloseOrderParam;
import org.dromara.waterfee.pay.domain.WechatPrePayParam;
import org.dromara.waterfee.pay.domain.WechatQueryOrderParam;

public interface IWechatJsapiPaymentService {

    /** 关闭订单 */
    void closeOrder(WechatCloseOrderParam wechatCloseOrderParam);

    /** JSAPI支付下单，并返回JSAPI调起支付数据 */
    PrepayWithRequestPaymentResponse prepayWithRequestPayment(HttpServletRequest request, HttpServletResponse response, WechatPrePayParam wechatPrePayParam);

    /** 微信支付订单号查询订单 只能查询支付成功的订单 */
    Transaction queryOrderById(WechatQueryOrderParam wechatQueryOrderParam);

    /** 商户订单号查询订单 */
    Transaction queryOrderByOutTradeNo(WechatQueryOrderParam wechatQueryOrderParam);

    /** 微信支付回调 */
    String wechatPayCallBack(HttpServletRequest request, HttpServletResponse response);

}
