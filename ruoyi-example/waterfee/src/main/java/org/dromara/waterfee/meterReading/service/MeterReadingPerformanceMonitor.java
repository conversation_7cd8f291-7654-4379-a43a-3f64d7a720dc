package org.dromara.waterfee.meterReading.service;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 抄表任务性能监控服务
 * 用于监控和统计抄表任务的执行性能
 */
@Slf4j
@Service
public class MeterReadingPerformanceMonitor {

    // 性能指标
    private final AtomicInteger totalTasksExecuted = new AtomicInteger(0);
    private final AtomicInteger totalMetersProcessed = new AtomicInteger(0);
    private final AtomicInteger totalSuccessCount = new AtomicInteger(0);
    private final AtomicInteger totalFailCount = new AtomicInteger(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);

    // 账单处理指标
    private final AtomicInteger totalBillsGenerated = new AtomicInteger(0);
    private final AtomicInteger totalBillsAudited = new AtomicInteger(0);
    private final AtomicInteger totalPaymentsProcessed = new AtomicInteger(0);
    private final AtomicInteger totalPaymentsSucceeded = new AtomicInteger(0);
    private final AtomicInteger totalPaymentsFailed = new AtomicInteger(0);

    // 实时性能数据
    private final Map<String, PerformanceMetric> taskMetrics = new ConcurrentHashMap<>();
    private final Map<String, Long> recentExecutionTimes = new ConcurrentHashMap<>();

    // 性能阈值配置
    private static final long SLOW_TASK_THRESHOLD_MS = 30000; // 30秒
    private static final int HIGH_FAIL_RATE_THRESHOLD = 10; // 10%
    private static final int MAX_RECENT_RECORDS = 100;

    /**
     * 记录任务开始执行
     */
    public void recordTaskStart(String taskId, int meterCount) {
        PerformanceMetric metric = new PerformanceMetric();
        metric.setTaskId(taskId);
        metric.setMeterCount(meterCount);
        metric.setStartTime(System.currentTimeMillis());
        taskMetrics.put(taskId, metric);

        log.debug("任务开始执行 - 任务ID: {}, 水表数量: {}", taskId, meterCount);
    }

    /**
     * 记录任务执行完成（包含账单处理信息）
     */
    public void recordTaskComplete(String taskId, int successCount, int failCount,
                                   int billsGenerated, int billsAudited,
                                   int paymentsProcessed, int paymentsSucceeded, int paymentsFailed) {
        recordTaskComplete(taskId, successCount, failCount);

        // 更新账单处理统计
        totalBillsGenerated.addAndGet(billsGenerated);
        totalBillsAudited.addAndGet(billsAudited);
        totalPaymentsProcessed.addAndGet(paymentsProcessed);
        totalPaymentsSucceeded.addAndGet(paymentsSucceeded);
        totalPaymentsFailed.addAndGet(paymentsFailed);
    }

    /**
     * 记录任务执行完成
     */
    public void recordTaskComplete(String taskId, int successCount, int failCount) {
        PerformanceMetric metric = taskMetrics.get(taskId);
        if (metric == null) {
            log.warn("未找到任务性能指标，任务ID: {}", taskId);
            return;
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - metric.getStartTime();

        metric.setEndTime(endTime);
        metric.setDuration(duration);
        metric.setSuccessCount(successCount);
        metric.setFailCount(failCount);
        metric.setCompleted(true);

        // 更新全局统计
        totalTasksExecuted.incrementAndGet();
        totalMetersProcessed.addAndGet(metric.getMeterCount());
        totalSuccessCount.addAndGet(successCount);
        totalFailCount.addAndGet(failCount);
        totalProcessingTime.addAndGet(duration);

        // 记录最近执行时间
        recentExecutionTimes.put(taskId, duration);
        cleanupOldRecords();

        // 性能分析和告警
        analyzePerformance(metric);

        log.info("任务执行完成 - 任务ID: {}, 耗时: {}ms, 成功: {}, 失败: {}, 成功率: {}%",
            taskId, duration, successCount, failCount,
            metric.getMeterCount() > 0 ? (successCount * 100.0 / metric.getMeterCount()) : 0);

    }

    /**
     * 获取性能统计报告
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();

        int totalTasks = totalTasksExecuted.get();
        int totalMeters = totalMetersProcessed.get();
        int totalSuccess = totalSuccessCount.get();
        int totalFail = totalFailCount.get();
        long totalTime = totalProcessingTime.get();

        report.setTotalTasksExecuted(totalTasks);
        report.setTotalMetersProcessed(totalMeters);
        report.setTotalSuccessCount(totalSuccess);
        report.setTotalFailCount(totalFail);
        report.setTotalProcessingTime(totalTime);

        // 设置账单处理统计
        report.setTotalBillsGenerated(totalBillsGenerated.get());
        report.setTotalBillsAudited(totalBillsAudited.get());
        report.setTotalPaymentsProcessed(totalPaymentsProcessed.get());
        report.setTotalPaymentsSucceeded(totalPaymentsSucceeded.get());
        report.setTotalPaymentsFailed(totalPaymentsFailed.get());

        if (totalTasks > 0) {
            report.setAverageTaskDuration(totalTime / totalTasks);
            report.setAverageMetersPerTask(totalMeters / totalTasks);
        }

        if (totalMeters > 0) {
            report.setOverallSuccessRate((totalSuccess * 100.0) / totalMeters);
            report.setAverageProcessingTimePerMeter(totalTime / totalMeters);
        }

        // 计算最近任务的平均执行时间
        if (!recentExecutionTimes.isEmpty()) {
            double avgRecentTime = recentExecutionTimes.values().stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0);
            report.setRecentAverageTaskDuration((long) avgRecentTime);
        }

        return report;
    }

    /**
     * 性能分析和告警
     */
    private void analyzePerformance(PerformanceMetric metric) {
        // 检查慢任务
        if (metric.getDuration() > SLOW_TASK_THRESHOLD_MS) {
            log.warn("检测到慢任务 - 任务ID: {}, 耗时: {}ms, 水表数量: {}",
                metric.getTaskId(), metric.getDuration(), metric.getMeterCount());
        }

        // 检查高失败率
        if (metric.getMeterCount() > 0) {
            double failRate = (metric.getFailCount() * 100.0) / metric.getMeterCount();
            if (failRate > HIGH_FAIL_RATE_THRESHOLD) {
                log.warn("检测到高失败率任务 - 任务ID: {}, 失败率: {}%, 失败数: {}/{}",
                    metric.getTaskId(), failRate, metric.getFailCount(), metric.getMeterCount());
            }
        }

        // 性能趋势分析
        analyzeTrend(metric);
    }

    /**
     * 性能趋势分析
     */
    private void analyzeTrend(PerformanceMetric metric) {
        if (recentExecutionTimes.size() < 5) {
            return; // 数据不足，无法分析趋势
        }

        // 计算最近几次执行的平均时间
        double recentAvg = recentExecutionTimes.values().stream()
            .skip(Math.max(0, recentExecutionTimes.size() - 5))
            .mapToLong(Long::longValue)
            .average()
            .orElse(0.0);

        // 如果当前任务执行时间明显超过最近平均值，发出告警
        if (metric.getDuration() > recentAvg * 1.5) {
            log.warn("任务执行时间异常 - 任务ID: {}, 当前耗时: {}ms, 最近平均: {}ms",
                metric.getTaskId(), metric.getDuration(), recentAvg);
        }
    }

    /**
     * 清理旧记录
     */
    private void cleanupOldRecords() {
        if (recentExecutionTimes.size() > MAX_RECENT_RECORDS) {
            // 简单的清理策略：保留最近的记录
            recentExecutionTimes.entrySet().removeIf(entry ->
                recentExecutionTimes.size() > MAX_RECENT_RECORDS * 0.8);
        }
    }

    /**
     * 重置统计数据
     */
    public void resetStatistics() {
        totalTasksExecuted.set(0);
        totalMetersProcessed.set(0);
        totalSuccessCount.set(0);
        totalFailCount.set(0);
        totalProcessingTime.set(0);

        // 重置账单处理统计
        totalBillsGenerated.set(0);
        totalBillsAudited.set(0);
        totalPaymentsProcessed.set(0);
        totalPaymentsSucceeded.set(0);
        totalPaymentsFailed.set(0);

        taskMetrics.clear();
        recentExecutionTimes.clear();

        log.info("性能统计数据已重置");
    }

    /**
     * 获取当前活跃任务数量
     */
    public int getActiveTaskCount() {
        return (int) taskMetrics.values().stream()
            .filter(metric -> !metric.isCompleted())
            .count();
    }

    /**
     * 性能指标类
     */
    @Setter
    @Getter
    public static class PerformanceMetric {
        // getters and setters
        private String taskId;
        private int meterCount;
        private long startTime;
        private long endTime;
        private long duration;
        private int successCount;
        private int failCount;
        private boolean completed = false;

    }

    /**
     * 性能报告类
     */
    @Setter
    @Getter
    public static class PerformanceReport {
        // getters and setters
        private int totalTasksExecuted;
        private int totalMetersProcessed;
        private int totalSuccessCount;
        private int totalFailCount;
        private long totalProcessingTime;
        private long averageTaskDuration;
        private long recentAverageTaskDuration;
        private int averageMetersPerTask;
        private double overallSuccessRate;
        private long averageProcessingTimePerMeter;

        // 账单处理统计的getter和setter
        // 账单处理统计
        private int totalBillsGenerated;
        private int totalBillsAudited;
        private int totalPaymentsProcessed;
        private int totalPaymentsSucceeded;
        private int totalPaymentsFailed;

        /**
         * 获取支付成功率
         */
        public double getPaymentSuccessRate() {
            if (totalPaymentsProcessed == 0) return 0.0;
            return (totalPaymentsSucceeded * 100.0) / totalPaymentsProcessed;
        }

        /**
         * 获取账单审核率
         */
        public double getBillAuditRate() {
            if (totalBillsGenerated == 0) return 0.0;
            return (totalBillsAudited * 100.0) / totalBillsGenerated;
        }
    }
}
