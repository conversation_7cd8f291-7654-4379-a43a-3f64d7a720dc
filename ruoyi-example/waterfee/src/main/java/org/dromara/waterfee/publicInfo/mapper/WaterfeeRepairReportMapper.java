package org.dromara.waterfee.repair.mapper;

import org.dromara.waterfee.repair.domain.WaterfeeRepairReport;
import org.dromara.waterfee.repair.domain.vo.WaterfeeRepairReportVo;
import org.dromara.waterfee.repair.domain.bo.WaterfeeRepairReportBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 报修管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface WaterfeeRepairReportMapper extends BaseMapperPlus<WaterfeeRepairReport, WaterfeeRepairReportVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeRepairReportVo>> P selectVoPage(IPage<WaterfeeRepairReport> page, Wrapper<WaterfeeRepairReport> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询报修管理列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeRepairReportVo> queryList(@Param("page") Page<WaterfeeRepairReport> page, @Param("query") WaterfeeRepairReportBo query);

}
