package org.dromara.waterfee.chargeManage.domain.vo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 违约金调整/减免视图对象 waterfee_charge_manage_penalty_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManagePenaltyAdjustment.class)
public class WaterfeeChargeManagePenaltyAdjustmentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long billId;

    /**
     * 原违约金
     */
    @ExcelProperty(value = "原违约金")
    private Long originalPenalty;

    /**
     * 调整后违约金
     */
    @ExcelProperty(value = "调整后违约金")
    private Long adjustedPenalty;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String reason;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
