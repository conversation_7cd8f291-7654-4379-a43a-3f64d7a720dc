package org.dromara.waterfee.bill.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.counter.domain.bo.WechatPayBillBo;
import org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账单管理
 * 前端访问路由地址为:/bill/bill
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/bill")
public class WaterfeeBillController extends BaseController {

    private final IWaterfeeBillService waterfeeBillService;

    /**
     * 查询账单管理列表
     */
    @SaCheckPermission("waterfee:bill:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeBillVo> list(WaterfeeBillBo bo, PageQuery pageQuery) {
        return waterfeeBillService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询账单管理列表
     */
    @SaCheckPermission("waterfee:bill:list")
    @GetMapping("/listDetail")
    public TableDataInfo<WaterfeeBillVo> listDetail(WaterfeeBillBo bo, PageQuery pageQuery) {
        return waterfeeBillService.queryPageListDetail(bo, pageQuery);
    }

    /**
     * 导出账单管理列表
     */
    @SaCheckPermission("waterfee:bill:export")
    @Log(title = "账单管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeBillBo bo, HttpServletResponse response) {
        List<WaterfeeBillVo> list = waterfeeBillService.queryList(bo);
        ExcelUtil.exportExcel(list, "账单管理", WaterfeeBillVo.class, response);
    }

    /**
     * 获取账单管理详细信息
     *
     * @param billId 主键
     */
    @SaCheckPermission("waterfee:bill:query")
    @GetMapping("/{billId}")
    public R<WaterfeeBillVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("billId") Long billId) {
        return R.ok(waterfeeBillService.queryById(billId));
    }

    /**
     * 新增账单管理
     */
    @SaCheckPermission("waterfee:bill:add")
    @Log(title = "账单管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeBillBo bo) {
        return toAjax(waterfeeBillService.insertByBo(bo));
    }

    /**
     * 修改账单管理
     */
    @SaCheckPermission("waterfee:bill:edit")
    @Log(title = "账单管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeBillBo bo) {
        return toAjax(waterfeeBillService.updateByBo(bo));
    }

    /**
     * 删除账单管理
     *
     * @param billIds 主键串
     */
    @SaCheckPermission("waterfee:bill:remove")
    @Log(title = "账单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{billIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] billIds) {
        return toAjax(waterfeeBillService.deleteWithValidByIds(List.of(billIds), true));
    }

    /**
     * 调整账单用量
     */
    @SaCheckPermission("waterfee:bill:adjust")
    @Log(title = "账单金额调整", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/adjust")
    public R<Void> adjustConsumption(@RequestBody WaterfeeBillBo bo) {
        if (bo.getBillId() == null || bo.getAdjustmentsAmount() == null) {
            return R.fail("账单ID和调整金额不能为空");
        }
        return toAjax(waterfeeBillService.adjustConsumption(bo));
    }

    /**
     * 支付账单
     */
    //支付不走该接口，而是走水费窗口收费的支付接口
    @SaCheckPermission("waterfee:bill:pay")
    @Log(title = "账单支付", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/pay/{billId}")
    public R<Void> payBill(@NotNull(message = "账单ID不能为空") @PathVariable("billId") Long billId,
                           @NotNull(message = "支付金额不能为空") @RequestParam("amount") BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            return R.fail("支付金额必须>=0");
        }
        return toAjax(waterfeeBillService.payBill(billId, amount));
    }

    /**
     * 查询待发行账单列表
     */
    @SaCheckPermission("waterfee:bill:issue")
    @GetMapping("/pending-issue/list")
    public TableDataInfo<WaterfeeBillVo> pendingIssueList(WaterfeeBillBo bo, PageQuery pageQuery) {
        return waterfeeBillService.queryPendingIssueList(bo, pageQuery);
    }

    /**
     * 发行账单
     */
    @SaCheckPermission("waterfee:bill:issue")
    @Log(title = "账单发行", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/issue/{meterBookIds}")
    public R<Void> issueBills(@NotEmpty(message = "账单ID不能为空")
                              @PathVariable Long[] meterBookIds) {
        return toAjax(waterfeeBillService.issueBills(List.of(meterBookIds)));
    }

    /**
     * 查询按表册分组的账单汇总信息
     */
    @SaCheckPermission("waterfee:bill:issue")
    @GetMapping("/issue/list")
    public TableDataInfo<MeterBookBillSummaryVo> issueList(WaterfeeBillBo bo, PageQuery pageQuery) {
        return waterfeeBillService.queryMeterBookBillSummaryPageList(bo, pageQuery);
    }
}
