package org.dromara.waterfee.meter.exception;

import lombok.Getter;

/**
 * 智能表设备通信异常
 */
@Getter
public class MeterDeviceException extends RuntimeException {

  private static final long serialVersionUID = 1L;

  /**
   * 错误码
   */
  private final Integer code;

  /**
   * 错误信息
   */
  private final String message;

  public MeterDeviceException(String message) {
    this.code = 500;
    this.message = message;
  }

  public MeterDeviceException(Integer code, String message) {
    this.code = code;
    this.message = message;
  }
}