package org.dromara.waterfee.chargeManage.domain.vo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 应收账分账记录视图对象 waterfee_charge_manage_ar_split
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageArSplit.class)
public class WaterfeeChargeManageArSplitVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 账单ID
     */
    @ExcelProperty(value = "账单ID")
    private Long billId;

    /**
     * 项目编码
     */
    @ExcelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long departmentId;

    /**
     * 分账金额
     */
    @ExcelProperty(value = "分账金额")
    private Long splitAmount;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
