package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdRecordBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdRecordVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageWithholdRecordMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageWithholdRecordService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 代扣记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageWithholdRecordServiceImpl implements IWaterfeeChargeManageWithholdRecordService {

    private final WaterfeeChargeManageWithholdRecordMapper baseMapper;

    /**
     * 查询代扣记录
     *
     * @param id 主键
     * @return 代扣记录
     */
    @Override
    public WaterfeeChargeManageWithholdRecord queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询代扣记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代扣记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageWithholdRecordVo> queryPageList(WaterfeeChargeManageWithholdRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageWithholdRecord> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageWithholdRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代扣记录列表
     *
     * @param bo 查询条件
     * @return 代扣记录列表
     */
    @Override
    public List<WaterfeeChargeManageWithholdRecordVo> queryList(WaterfeeChargeManageWithholdRecordBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageWithholdRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageWithholdRecord> buildQueryWrapper(WaterfeeChargeManageWithholdRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageWithholdRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageWithholdRecord::getCreateTime);
        lqw.eq(bo.getUserId() != null, WaterfeeChargeManageWithholdRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getAmount() != null, WaterfeeChargeManageWithholdRecord::getAmount, bo.getAmount());
        lqw.eq(bo.getWithholdTime() != null, WaterfeeChargeManageWithholdRecord::getWithholdTime, bo.getWithholdTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WaterfeeChargeManageWithholdRecord::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增代扣记录
     *
     * @param bo 代扣记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageWithholdRecordBo bo) {
        WaterfeeChargeManageWithholdRecord add = MapstructUtils.convert(bo, WaterfeeChargeManageWithholdRecord.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改代扣记录
     *
     * @param bo 代扣记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageWithholdRecordBo bo) {
        WaterfeeChargeManageWithholdRecord update = MapstructUtils.convert(bo, WaterfeeChargeManageWithholdRecord.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageWithholdRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代扣记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
