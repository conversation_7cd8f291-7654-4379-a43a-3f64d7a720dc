package org.dromara.waterfee.community.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 社区信息表 wf_community
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_community")
public class Community extends TenantEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 社区编号 */
    private String communityNo;

    /** 社区名称 */
    private String communityName;

    /** 所属区域ID */
    private Long areaId;

    /** 所属区域名称 */
    private String areaName;

    /** 社区地址 */
    private String address;

    /** 社区描述 */
    private String description;

}
