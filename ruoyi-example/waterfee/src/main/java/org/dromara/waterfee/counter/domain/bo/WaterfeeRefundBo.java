package org.dromara.waterfee.counter.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 退费业务对象
 *
 * <AUTHOR> Li
 * @date 2025-05-07
 */
@Data
public class WaterfeeRefundBo {

    /**
     * 账单ID
     */
    @NotNull(message = "账单ID不能为空")
    private Long billId;

    /**
     * 退费金额
     */
    @NotNull(message = "退费金额不能为空")
    @PositiveOrZero(message = "退费金额必须>=0")
    private BigDecimal refundAmount;

    /**
     * 退费原因
     */
    @NotBlank(message = "退费原因不能为空")
    private String refundReason;

    /**
     * 退款方式（CASH:现金, BANK_CARD:银行卡, DEPOSIT:预存款）
     */
    @NotBlank(message = "退款方式不能为空")
    private String refundMethod;
}
