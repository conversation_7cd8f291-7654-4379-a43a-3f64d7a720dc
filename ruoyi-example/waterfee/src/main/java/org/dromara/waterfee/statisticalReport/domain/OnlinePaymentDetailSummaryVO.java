package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OnlinePaymentDetailSummaryVO {

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 水量
     */
//    private BigDecimal consumptionVolume;

    /**
     * 水费金额
     */
    @ExcelProperty(value = "水费")
    private Double waterBillOnly;

    /**
     * 水资源费金额
     */
    @ExcelProperty(value = "水资源费")
    private BigDecimal waterResourceTax;

    /**
     * 污水费金额
     */
    @ExcelProperty(value = "污水费")
    private BigDecimal sewageTreatmentFee;

    /**
     * 缴费金额
     */
    @ExcelProperty(value = "缴费金额")
    private BigDecimal paymentAmount;

    /**
     * 缴费日期
     */
    @ExcelProperty(value = "缴费时间")
    private Date paymentTime;

    /**
     * 支付单号
     */
    @ExcelProperty(value = "支付单号")
    private String transactionId;
}
