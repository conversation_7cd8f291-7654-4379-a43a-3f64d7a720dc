package org.dromara.waterfee.user.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.domain.bo.*;
import org.dromara.waterfee.user.domain.dto.WaterfeeMeterAddDto;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserWorkFlowVo;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserImportVo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用水用户管理
 * 前端访问路由地址为:/user/用水用户管理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user")
public class WaterfeeUserController extends BaseController {

    private final IWaterfeeUserService waterfeeUserService;

    /**
     * 查询用水用户管理列表
     */
    @SaCheckPermission("waterfee:user:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeUserVo> list(WaterfeeUserBo bo, PageQuery pageQuery) {
        return waterfeeUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用水用户管理列表
     */
    @SaCheckPermission("waterfee:user:export")
    @Log(title = "用水用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeUserBo bo, HttpServletResponse response) {
        List<WaterfeeUserVo> list = waterfeeUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "用水用户管理", WaterfeeUserVo.class, response);
    }

    /**
     * 获取用水用户管理详细信息
     *
     * @param userId 主键
     */
    @SaCheckPermission("waterfee:user:query")
    @GetMapping("/{userId}")
    public R<WaterfeeUser> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("userId") Long userId) {
        return R.ok(waterfeeUserService.queryById(userId));
    }

    /**
     * 新增用水用户管理
     */
    @SaCheckPermission("waterfee:user:add")
    @Log(title = "用水用户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeUserBo bo) {
        return toAjax(waterfeeUserService.insertByBo(bo));
    }

    /**
     * 修改用水用户管理
     */
    @SaCheckPermission("waterfee:user:edit")
    @Log(title = "用水用户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeUserBo bo) {
        return toAjax(waterfeeUserService.updateByBo(bo));
    }

    /**
     * 删除用水用户管理
     *
     * @param userIds 主键串
     */
    @SaCheckPermission("waterfee:user:remove")
    @Log(title = "用水用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("userIds") Long[] userIds) {
        return toAjax(waterfeeUserService.deleteWithValidByIds(List.of(userIds), true));
    }

    /**
     * 审核用水用户
     */
//    @SaCheckPermission("waterfee:user:audit")
//    @Log(title = "用水用户审核", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping("/audit/{userId}")
//    public R<Void> audit(@NotNull(message = "用户ID不能为空") @PathVariable Long userId) {
//        return toAjax(waterfeeUserService.auditUser(userId));
//    }

    /**
     * 用户过户操作
     */
    @SaCheckPermission("waterfee:user:transfer")
    @Log(title = "用水用户过户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/transfer")
    public R<Void> transferOwnership(@Validated(AddGroup.class) @RequestBody WaterfeeUserTransferOwnershipRecordBo bo) {
        return toAjax(waterfeeUserService.transferOwnership(bo));
    }

    /**
     * 用户价格变更操作
     */
    @SaCheckPermission("waterfee:user:priceChange")
    @Log(title = "用水用户价格变更", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/priceChange")
    public R<Void> priceChange(@Validated(AddGroup.class) @RequestBody WaterfeeUserPriceChangeRecordBo bo) {
        return toAjax(waterfeeUserService.updateUserPrice(bo));
    }

    /**
     * 启用用水用户
     */
    @SaCheckPermission("waterfee:user:activate")
    @Log(title = "用水用户启用", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/activate/{userId}")
    public R<Void> activate(@NotNull(message = "用户ID不能为空") @PathVariable Long userId) {
        return toAjax(waterfeeUserService.activateUser(userId));
    }

    /**
     * 报停用水用户
     */
    @SaCheckPermission("waterfee:user:deactivate")
    @Log(title = "用水用户报停", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/deactivate")
    public R<Void> deactivate(@Validated @RequestBody WaterfeeUserDeactivateRecordBo bo) {
        return toAjax(waterfeeUserService.deactivateUser(bo));
    }

    /**
     * 销户用水用户
     */
    @SaCheckPermission("waterfee:user:cancellation")
    @Log(title = "用水用户销户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancellation")
    public R<Void> cancellation(@Validated @RequestBody WaterfeeUserCancellationRecordBo bo) {
        return toAjax(waterfeeUserService.cancellationUser(bo));
    }

    /**
     * 用户基础信息变更操作
     */
    @SaCheckPermission("waterfee:user:basicInfoChange")
    @Log(title = "用水用户基础信息变更", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/basicInfoChange")
    public R<Void> basicInfoChange(@Validated(AddGroup.class) @RequestBody WaterfeeUserBasicInfoChangeRecordBo bo) {
        return toAjax(waterfeeUserService.updateUserBasicInfo(bo));
    }

    /**
     * 批量启用用水用户
     */
    @SaCheckPermission("waterfee:user:activate")
    @Log(title = "批量启用用水用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchActivate")
    public R<Void> batchActivate(@NotEmpty(message = "用户ID不能为空") @RequestBody List<Long> userIds) {
        return toAjax(waterfeeUserService.batchActivateUsers(userIds));
    }

    /**
     * 批量报停用水用户
     */
    @SaCheckPermission("waterfee:user:deactivate")
    @Log(title = "批量报停用水用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchDeactivate")
    public R<Void> batchDeactivate(@Validated @RequestBody WaterfeeUserBatchDeactivateBo bo) {
        return toAjax(waterfeeUserService.batchDeactivateUsers(bo));
    }

    /**
     * 批量销户用水用户
     */
    @SaCheckPermission("waterfee:user:cancellation")
    @Log(title = "批量销户用水用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchCancellation")
    public R<Void> batchCancellation(@Validated @RequestBody WaterfeeUserBatchCancellationBo bo) {
        return toAjax(waterfeeUserService.batchCancellationUsers(bo));
    }

    /**
     * 工作流 - 新增用水用户基本信息
     */
//    @SaCheckPermission("waterfee:user:add")
//    @Log(title = "工作流 - 新增用水用户基本信息", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping("/basicInfo")
//    public R<WaterfeeUser> addBasicInfo(@Validated(AddGroup.class) @RequestBody WaterfeeUserBasicInfoBo bo) {
//        return R.ok(waterfeeUserService.insertUserBasicInfo(bo));
//    }

    /**
     * 工作流 - 编辑用水用户基本信息
     */
//    @SaCheckPermission("waterfee:user:edit")
//    @Log(title = "工作流 - 编辑用水用户基本信息", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping("/basicInfo")
//    public R<WaterfeeUser> editBasicInfo(@Validated(EditGroup.class) @RequestBody WaterfeeUserBasicInfoBo bo) {
//        return R.ok(waterfeeUserService.updateUserDraftBasicInfo(bo));
//    }

    /**
     * 工作流 - 为用水用户选择或新增水表信息
     */
//    @SaCheckPermission("waterfee:user:meter")
//    @Log(title = "工作流 - 用水用户关联水表", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/bindMeter")
//    public R<Void> bindMeter(@Validated @RequestBody WaterfeeMeterAddDto dto) {
//        return toAjax(waterfeeUserService.bindUserMeter(dto));
//    }

    /**
     * 工作流 -  设定用水用户价格信息
     */
//    @SaCheckPermission("waterfee:user:edit")
//    @Log(title = "工作流 - 设定用水用户价格信息", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping("/price")
//    public R<Void> updatePrice(@Validated(EditGroup.class) @RequestBody WaterfeeUserPriceBo bo) {
//        return toAjax(waterfeeUserService.updateUserPriceInfo(bo));
//    }

    /**
     * 工作流 - 获取用水用户详细信息
     *
     * @param userId 主键
     */
//    @SaCheckPermission("waterfee:user:query")
//    @GetMapping("/workFlowGetDetail/{userId}")
//    public R<WaterfeeUserWorkFlowVo> WorkFlowGetDetail(@NotNull(message = "主键不能为空")
//                                               @PathVariable("userId") Long userId) {
//        return R.ok(waterfeeUserService.workFlowGetDetail(userId));
//    }

    /**
     * 导入用水用户数据
     */
    @SaCheckPermission("waterfee:user:import")
    @Log(title = "用水用户管理", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        return R.ok(waterfeeUserService.importUser(file, updateSupport));
    }

    /**
     * 获取导入用水用户模板
     */
    @SaCheckPermission("waterfee:user:import")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(List.of(), "用水用户数据", WaterfeeUserImportVo.class, response);
    }

    /**
     * 批量设定为重点户
     */
    @SaCheckPermission("waterfee:user:edit")
    @Log(title = "批量设定为重点户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchSetSpecific")
    public R<Void> batchSetSpecific(@NotEmpty(message = "用户ID不能为空") @RequestBody List<Long> userIds) {
        return toAjax(waterfeeUserService.batchSetSpecificUsers(userIds));
    }

    /**
     * 批量取消重点户
     */
    @SaCheckPermission("waterfee:user:edit")
    @Log(title = "批量取消重点户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchCancelSpecific")
    public R<Void> batchCancelSpecific(@NotEmpty(message = "用户ID不能为空") @RequestBody List<Long> userIds) {
        return toAjax(waterfeeUserService.batchCancelSpecificUsers(userIds));
    }
}
