package org.dromara.waterfee.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.user.domain.WaterfeeUserBalanceChangeRecord;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBalanceChangeRecordBo;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserBalanceChangeRecordVo;
import org.dromara.waterfee.user.mapper.WaterfeeUserBalanceChangeRecordMapper;
import org.dromara.waterfee.user.service.IWaterfeeUserBalanceChangeRecordService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户余额变更记录Service业务层处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeeUserBalanceChangeRecordServiceImpl implements IWaterfeeUserBalanceChangeRecordService {

    private final WaterfeeUserBalanceChangeRecordMapper baseMapper;

    @Override
    public WaterfeeUserBalanceChangeRecordVo queryById(Long recordId) {
        return baseMapper.selectVoById(recordId);
    }

    @Override
    public TableDataInfo<WaterfeeUserBalanceChangeRecordVo> queryPageList(WaterfeeUserBalanceChangeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeUserBalanceChangeRecord> lqw = buildQueryWrapper(bo);
        Page<WaterfeeUserBalanceChangeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<WaterfeeUserBalanceChangeRecordVo> queryList(WaterfeeUserBalanceChangeRecordBo bo) {
        LambdaQueryWrapper<WaterfeeUserBalanceChangeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeUserBalanceChangeRecord> buildQueryWrapper(WaterfeeUserBalanceChangeRecordBo bo) {
        LambdaQueryWrapper<WaterfeeUserBalanceChangeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, WaterfeeUserBalanceChangeRecord::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserNo()), WaterfeeUserBalanceChangeRecord::getUserNo, bo.getUserNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), WaterfeeUserBalanceChangeRecord::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), WaterfeeUserBalanceChangeRecord::getBusinessType, bo.getBusinessType());
        lqw.like(StringUtils.isNotBlank(bo.getOperator()), WaterfeeUserBalanceChangeRecord::getOperator, bo.getOperator());
        lqw.ge(bo.getChangeTime() != null, WaterfeeUserBalanceChangeRecord::getChangeTime, bo.getChangeTime());
        lqw.orderByDesc(WaterfeeUserBalanceChangeRecord::getChangeTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(WaterfeeUserBalanceChangeRecordBo bo) {
        WaterfeeUserBalanceChangeRecord add = MapstructUtils.convert(bo, WaterfeeUserBalanceChangeRecord.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRecordId(add.getRecordId());
        }
        return flag;
    }

    @Override
    public void recordBalanceChange(Long userId, String userNo, String changeType, 
                                  BigDecimal beforeBalance, BigDecimal changeAmount, BigDecimal afterBalance,
                                  String businessId, String businessType, String changeReason, 
                                  String operator, String remark) {
        try {
            WaterfeeUserBalanceChangeRecord record = new WaterfeeUserBalanceChangeRecord();
            record.setUserId(userId);
            record.setUserNo(userNo);
            record.setChangeType(changeType);
            record.setBeforeBalance(beforeBalance);
            record.setChangeAmount(changeAmount);
            record.setAfterBalance(afterBalance);
            record.setBusinessId(businessId);
            record.setBusinessType(businessType);
            record.setChangeReason(changeReason);
            record.setOperator(operator != null ? operator : LoginHelper.getUsername());
            record.setChangeTime(new Date());
            record.setRemark(remark);
            record.setCreateBy(LoginHelper.getUserId());
            record.setCreateTime(new Date());
            record.setCreateDept(LoginHelper.getDeptId());
            record.setTenantId(LoginHelper.getTenantId());

            baseMapper.insert(record);
            
            log.info("记录余额变更成功，用户：{}，变更类型：{}，变更前：{}，变更金额：{}，变更后：{}",
                userNo, changeType, beforeBalance, changeAmount, afterBalance);
        } catch (Exception e) {
            log.error("记录余额变更失败，用户：{}，变更类型：{}", userNo, changeType, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}