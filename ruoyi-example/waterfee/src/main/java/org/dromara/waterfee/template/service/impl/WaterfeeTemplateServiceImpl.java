package org.dromara.waterfee.template.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.template.domain.bo.WaterfeeTemplateBo;
import org.dromara.waterfee.template.domain.entity.WaterfeeTemplate;
import org.dromara.waterfee.template.domain.vo.WaterfeeTemplateVo;
import org.dromara.waterfee.template.enums.TemplateCategoryEnum;
import org.dromara.waterfee.template.enums.TemplateTypeEnum;
import org.dromara.waterfee.template.mapper.WaterfeeTemplateMapper;
import org.dromara.waterfee.template.service.IWaterfeeTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 模板管理服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeeTemplateServiceImpl implements IWaterfeeTemplateService {

    private final WaterfeeTemplateMapper baseMapper;

    @Override
    public WaterfeeTemplateVo queryById(Long templateId) {
        WaterfeeTemplateVo vo = baseMapper.selectVoById(templateId);
        if (vo != null) {
            setTemplateInfo(vo);
        }
        return vo;
    }

    @Override
    public TableDataInfo<WaterfeeTemplateVo> queryPageList(WaterfeeTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeTemplate> lqw = buildQueryWrapper(bo);
        Page<WaterfeeTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 设置模板信息
        result.getRecords().forEach(this::setTemplateInfo);

        return TableDataInfo.build(result);
    }

    @Override
    public List<WaterfeeTemplateVo> queryList(WaterfeeTemplateBo bo) {
        LambdaQueryWrapper<WaterfeeTemplate> lqw = buildQueryWrapper(bo);
        List<WaterfeeTemplateVo> list = baseMapper.selectVoList(lqw);

        // 设置模板信息
        list.forEach(this::setTemplateInfo);

        return list;
    }

    @Override
    public Boolean insertByBo(WaterfeeTemplateBo bo) {
        WaterfeeTemplate add = MapstructUtils.convert(bo, WaterfeeTemplate.class);
        validEntityBeforeSave(add);

        // 设置上传信息
        add.setUploadBy(LoginHelper.getUserId().toString());
        add.setUploadTime(new Date());
        add.setIsEnabled("0"); // 默认禁用

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTemplateId(add.getTemplateId());
        }
        return flag;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean uploadTemplate(MultipartFile file, WaterfeeTemplateBo templateBo) {
//        if (file == null || file.isEmpty()) {
//            throw new ServiceException("上传文件不能为空");
//        }
//
//        try {
//            // 验证文件类型
//            String fileName = file.getOriginalFilename();
//            if (StringUtils.isBlank(fileName)) {
//                throw new ServiceException("文件名不能为空");
//            }
//
//            String fileExtension = FileUtils.getExtension(fileName).toLowerCase();
//            if (!isValidFileType(fileExtension, templateBo.getTemplateType())) {
//                throw new ServiceException("文件类型不匹配，请上传正确的文件格式");
//            }
//
//            // 上传文件
//            String uploadPath = profile + "/templates/";
//            String filePath = FileUploadUtils.upload(uploadPath, file);
//
//            // 设置文件信息
//            templateBo.setFileName(fileName);
//            templateBo.setFilePath(filePath);
//            templateBo.setFileSize(file.getSize());
//            templateBo.setFileUrl(filePath);
//
//            // 保存模板信息
//            return insertByBo(templateBo);
//
//        } catch (Exception e) {
//            log.error("上传模板文件失败", e);
//            throw new ServiceException("上传模板文件失败：" + e.getMessage());
//        }
//    }

    @Override
    public Boolean updateByBo(WaterfeeTemplateBo bo) {
        WaterfeeTemplate update = MapstructUtils.convert(bo, WaterfeeTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableTemplate(Long templateId) {
        // 1. 查询要启用的模板
        WaterfeeTemplate template = baseMapper.selectById(templateId);
        if (template == null) {
            throw new ServiceException("模板不存在");
        }

        // 2. 禁用同类型同分类的其他模板
        LambdaUpdateWrapper<WaterfeeTemplate> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(WaterfeeTemplate::getTemplateType, template.getTemplateType());
        if (StringUtils.isNotBlank(template.getTemplateCategory())) {
            updateWrapper.eq(WaterfeeTemplate::getTemplateCategory, template.getTemplateCategory());
        }
        updateWrapper.ne(WaterfeeTemplate::getTemplateId, templateId);
        updateWrapper.set(WaterfeeTemplate::getIsEnabled, "0");
        updateWrapper.set(WaterfeeTemplate::getEnabledBy, null);
        updateWrapper.set(WaterfeeTemplate::getEnabledTime, null);
        updateWrapper.set(WaterfeeTemplate::getUpdateTime, new Date());
        updateWrapper.set(WaterfeeTemplate::getUpdateBy, LoginHelper.getUserId().toString());

        baseMapper.update(null, updateWrapper);

        // 3. 启用指定模板
        WaterfeeTemplate updateTemplate = new WaterfeeTemplate();
        updateTemplate.setTemplateId(templateId);
        updateTemplate.setIsEnabled("1");
        updateTemplate.setEnabledBy(LoginHelper.getUserId().toString());
        updateTemplate.setEnabledTime(new Date());
        updateTemplate.setUpdateTime(new Date());
        updateTemplate.setUpdateBy(LoginHelper.getUserId());

        return baseMapper.updateById(updateTemplate) > 0;
    }

    @Override
    public Boolean disableTemplate(Long templateId) {
        WaterfeeTemplate updateTemplate = new WaterfeeTemplate();
        updateTemplate.setTemplateId(templateId);
        updateTemplate.setIsEnabled("0");
        updateTemplate.setEnabledBy(null);
        updateTemplate.setEnabledTime(null);
        updateTemplate.setUpdateTime(new Date());
        updateTemplate.setUpdateBy(LoginHelper.getUserId());

        return baseMapper.updateById(updateTemplate) > 0;
    }

    @Override
    public WaterfeeTemplateVo getEnabledTemplate(String templateType, String templateCategory) {
        LambdaQueryWrapper<WaterfeeTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(WaterfeeTemplate::getTemplateType, templateType);
        if (StringUtils.isNotBlank(templateCategory)) {
            lqw.eq(WaterfeeTemplate::getTemplateCategory, templateCategory);
        }
        lqw.eq(WaterfeeTemplate::getIsEnabled, "1");
        lqw.orderByDesc(WaterfeeTemplate::getEnabledTime);
        lqw.last("LIMIT 1");

        WaterfeeTemplateVo vo = baseMapper.selectVoOne(lqw);
        if (vo != null) {
            setTemplateInfo(vo);
        }
        return vo;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有启用的模板
            LambdaQueryWrapper<WaterfeeTemplate> lqw = Wrappers.lambdaQuery();
            lqw.in(WaterfeeTemplate::getTemplateId, ids);
            lqw.eq(WaterfeeTemplate::getIsEnabled, "1");

            List<WaterfeeTemplate> enabledTemplates = baseMapper.selectList(lqw);
            if (!enabledTemplates.isEmpty()) {
                throw new ServiceException("不能删除已启用的模板，请先禁用后再删除");
            }
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }

//    @Override
//    public String downloadTemplate(Long templateId) {
//        WaterfeeTemplate template = baseMapper.selectById(templateId);
//        if (template == null) {
//            throw new ServiceException("模板不存在");
//        }
//
//        if (StringUtils.isBlank(template.getFilePath())) {
//            throw new ServiceException("模板文件路径为空");
//        }
//
//        File file = new File(template.getFilePath());
//        if (!file.exists()) {
//            throw new ServiceException("模板文件不存在");
//        }
//
//        return template.getFilePath();
//    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WaterfeeTemplate> buildQueryWrapper(WaterfeeTemplateBo bo) {
        LambdaQueryWrapper<WaterfeeTemplate> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTemplateName()), WaterfeeTemplate::getTemplateName, bo.getTemplateName());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateType()), WaterfeeTemplate::getTemplateType, bo.getTemplateType());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateCategory()), WaterfeeTemplate::getTemplateCategory, bo.getTemplateCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getIsEnabled()), WaterfeeTemplate::getIsEnabled, bo.getIsEnabled());
        lqw.eq(StringUtils.isNotBlank(bo.getUploadBy()), WaterfeeTemplate::getUploadBy, bo.getUploadBy());
        lqw.orderByDesc(WaterfeeTemplate::getIsEnabled);
        lqw.orderByDesc(WaterfeeTemplate::getCreateTime);
        return lqw;
    }

    /**
     * 设置模板信息
     */
    private void setTemplateInfo(WaterfeeTemplateVo vo) {
        if (vo != null) {
            // 设置类型名称
            vo.setTemplateTypeName(TemplateTypeEnum.getNameByCode(vo.getTemplateType()));

            // 设置分类名称
            vo.setTemplateCategoryName(TemplateCategoryEnum.getNameByCode(vo.getTemplateCategory()));

            // 设置启用状态名称
            vo.setEnabledStatusName("1".equals(vo.getIsEnabled()) ? "已启用" : "已禁用");

        }
    }

    /**
     * 校验实体
     */
    private void validEntityBeforeSave(WaterfeeTemplate entity) {
        // 可以添加保存前的校验逻辑
    }

    /**
     * 验证文件类型
     */
    private boolean isValidFileType(String fileExtension, String templateType) {
        switch (templateType) {
            case "WORD":
                return "doc".equals(fileExtension) || "docx".equals(fileExtension);
            case "EXCEL":
                return "xls".equals(fileExtension) || "xlsx".equals(fileExtension);
            case "PDF":
                return "pdf".equals(fileExtension);
            default:
                return false;
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long size) {
        if (size == null || size == 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();

        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }

        DecimalFormat df = new DecimalFormat("#.##");
        return df.format(fileSize) + " " + units[unitIndex];
    }
}
