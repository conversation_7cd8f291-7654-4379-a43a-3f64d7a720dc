package org.dromara.waterfee.bill.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.bill.domain.bo.WaterfeeBillBo;
import org.dromara.waterfee.bill.domain.vo.MeterBookBillSummaryVo;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 账单管理Service接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface IWaterfeeBillService {

    /**
     * 查询账单
     *
     * @param billId 账单主键
     * @return 账单
     */
    WaterfeeBillVo queryById(Long billId);

    /**
     * 查询账单
     *
     * @param billNumber
     * @return 账单
     */
    WaterfeeBillVo queryByBillNumber(String billNumber);

    /**
     * 查询账单列表
     *
     * @param billIds 账单主键集合
     * @return 账单集合
     */
    List<WaterfeeBillVo> queryByIds(Collection<Long> billIds);

    /**
     * 查询账单列表
     *
     * @param bo 账单
     * @return 账单集合
     */
    TableDataInfo<WaterfeeBillVo> queryPageList(WaterfeeBillBo bo, PageQuery pageQuery);

    /**
     * 查询账单列表
     *
     * @param bo 账单
     * @return 账单集合
     */
    TableDataInfo<WaterfeeBillVo> queryPageListDetail(WaterfeeBillBo bo, PageQuery pageQuery);

    /**
     * 查询账单列表
     *
     * @param bo 账单
     * @return 账单集合
     */
    List<WaterfeeBillVo> queryList(WaterfeeBillBo bo);

    /**
     * 新增账单
     *
     * @param bo 账单
     * @return 结果
     */
    Boolean insertByBo(WaterfeeBillBo bo);

    /**
     * 修改账单
     *
     * @param bo 账单
     * @return 结果
     */
    Boolean updateByBo(WaterfeeBillBo bo);

    /**
     * 校验并批量删除账单信息
     *
     * @param ids     需要删除的账单主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 调整账单用量
     *
     * @param bo 账单信息
     * @return 结果
     */
    Boolean adjustConsumption(WaterfeeBillBo bo);

    /**
     * 支付账单
     *
     * @param billId 账单ID
     * @param amount 支付金额
     * @return 结果
     */
    Boolean payBill(Long billId, BigDecimal amount);

    /**
     * 查询待发行账单列表
     *
     * @param bo        账单查询条件
     * @param pageQuery 分页参数
     * @return 待发行账单集合
     */
    TableDataInfo<WaterfeeBillVo> queryPendingIssueList(WaterfeeBillBo bo, PageQuery pageQuery);

    /**
     * 发行账单
     *
     * @param meterBookIds 需要发行的账单meterBookIds集合
     * @return 结果
     */
    Boolean issueBills(Collection<Long> meterBookIds);

    /**
     * 查询按表册分组的账单汇总信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 表册账单汇总信息分页列表
     */
    TableDataInfo<MeterBookBillSummaryVo> queryMeterBookBillSummaryPageList(WaterfeeBillBo bo, PageQuery pageQuery);
}
