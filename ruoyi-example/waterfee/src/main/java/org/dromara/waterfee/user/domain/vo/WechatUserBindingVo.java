package org.dromara.waterfee.user.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.waterfee.user.domain.WechatUserBinding;

import java.io.Serial;
import java.math.BigDecimal;



/**
 * 微信账号绑定户号关系视图对象 wechat_user_binding
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WechatUserBinding.class)
public class WechatUserBindingVo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 微信用户ID
     */
    @ExcelProperty(value = "微信用户ID")
    private Long wechatUserId;

    /**
     * 微信用户标识
     */
//    @ExcelProperty(value = "微信用户标识")
//    private String openid;

    /**
     * 用水户编号
     */
    @ExcelProperty(value = "用水户编号")
    private String userNo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 用水户名称
     */
    private String userName;

    /**
     * 用水地址
     */
    private String address;

    /**
     * 余额
     */
    private BigDecimal balance;


}
