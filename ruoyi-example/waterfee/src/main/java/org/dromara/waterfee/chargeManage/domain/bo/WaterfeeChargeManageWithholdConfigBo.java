package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 代扣配置信息业务对象 waterfee_charge_manage_withhold_config
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageWithholdConfig.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageWithholdConfigBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankAccount;

    /**
     * 
     */
    private String bankName;

    /**
     * 是否签约
     */
    private Long signed;

    /**
     * 签约时间
     */
    private Date signTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 
     */
    private String remark;


}
