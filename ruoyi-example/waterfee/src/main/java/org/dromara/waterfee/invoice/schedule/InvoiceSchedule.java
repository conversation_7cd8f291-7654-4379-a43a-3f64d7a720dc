package org.dromara.waterfee.invoice.schedule;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.invoice.domain.InvoiceRecord;
import org.dromara.waterfee.invoice.domain.QueryInvoiceParam;
import org.dromara.waterfee.invoice.domain.bo.InvoiceRecordBo;
import org.dromara.waterfee.invoice.enums.NuonuoInvoiceStatusEnum;
import org.dromara.waterfee.invoice.mapper.InvoiceRecordMapper;
import org.dromara.waterfee.invoice.service.IInvoiceRecordService;
import org.dromara.waterfee.invoice.service.NuonuoInvoiceService;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class InvoiceSchedule {

    private final InvoiceRecordMapper invoiceRecordMapper;
    private final IInvoiceRecordService invoiceRecordService;
    private final NuonuoInvoiceService nuonuoInvoiceService;

    /**
     * 每5分钟执行一次，查询并更新发票状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void syncInvoiceStatus() {
        log.info("开始执行发票状态同步定时任务");
        try {
            // 使用MyBatis-Plus查询状态为process的发票记录
            LambdaQueryWrapper<InvoiceRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InvoiceRecord::getInvoiceStatus, "process");
            List<InvoiceRecord> processRecords = invoiceRecordMapper.selectList(queryWrapper);
            if (processRecords.isEmpty()) {
                log.info("没有处于开票中状态的发票记录");
                return;
            }

            // 提取serialNo列表
            List<String> serialNos = processRecords.stream()
                    .map(InvoiceRecord::getSerialNo)
                    .collect(Collectors.toList());

            // 构建查询参数
            QueryInvoiceParam queryParam = new QueryInvoiceParam();
            queryParam.setSerialNos(serialNos);

            // 调用查询发票状态接口
            String result = nuonuoInvoiceService.queryInvoiceStatus(queryParam);
            log.info("发票状态查询结果: {}", result);

            // 解析结果并更新状态
            JSONObject resultJson = JSONObject.parseObject(result);
            if ("E0000".equals(resultJson.getString("code"))) {
                JSONArray invoices = resultJson.getJSONArray("result");
                for (Object invoiceObj : invoices) {
                    JSONObject invoice = (JSONObject) invoiceObj;
                    String serialNo = invoice.getString("serialNo");
                    String status = invoice.getString("status");
                    Long invoiceId = processRecords.stream().filter(record -> record.getSerialNo().equals(serialNo)).findFirst().get().getInvoiceId();
                    // 更新发票状态
                    InvoiceRecordBo updateBo = new InvoiceRecordBo();
                    updateBo.setInvoiceId(invoiceId);
                    for(InvoiceRecord record : processRecords) {
                        if(record.getSerialNo().equals(serialNo)) {
                            updateBo.setInvoiceId(record.getInvoiceId());
                            break;
                        }
                    }
                    if(status.equals(NuonuoInvoiceStatusEnum.INVOICED_COMPLETED.getCode())) {
                        updateBo.setInvoiceStatus("success");
                    }else if(status.equals(NuonuoInvoiceStatusEnum.INVOICE_FAILED.getCode()) || status.equals(NuonuoInvoiceStatusEnum.SIGN_FAILED_AFTER_SUCCESS.getCode())) {
                        updateBo.setInvoiceStatus("failure");
                    }else {
                        continue;
                    }
                    if(invoice.containsKey("pdfUrl")) {
                        updateBo.setPdfUrl(invoice.getString("pdfUrl"));
                    }
                    Long time = invoice.getLongValue("invoiceTime");
                    updateBo.setInvoiceTime(new Date(time));
                    updateBo.setInvoiceCode(invoice.getString("invoiceCode"));
                    updateBo.setInvoiceNo(invoice.getString("invoiceNo"));
                    updateBo.setInvoiceKind(invoice.getString("invoiceKind"));
                    updateBo.setOrderAmount(invoice.getBigDecimal("orderAmount"));
                    updateBo.setExTaxAmount(invoice.getBigDecimal("exTaxAmount"));
                    updateBo.setTaxAmount(invoice.getBigDecimal("taxAmount"));
                    if(updateBo.getInvoiceId() != null) {
                        invoiceRecordService.updateByBo(updateBo);
                    }else {
                        log.error("更新发票状态失败: serialNo={}, status={}", serialNo, status);
                    }
                    log.info("更新发票状态成功: serialNo={}, status={}", serialNo, status);
                }
            } else {
                log.error("查询发票状态失败: {}", resultJson.getString("describe"));
            }
        } catch (Exception e) {
            log.error("发票状态同步任务执行异常", e);
        }
    }
}
