package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 应收账分账记录对象 waterfee_charge_manage_ar_split
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_ar_split")
public class WaterfeeChargeManageArSplit extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 分账金额
     */
    private Long splitAmount;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
