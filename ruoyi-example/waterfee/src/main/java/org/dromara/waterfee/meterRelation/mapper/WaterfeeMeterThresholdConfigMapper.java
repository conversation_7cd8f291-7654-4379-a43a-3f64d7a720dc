package org.dromara.waterfee.meterRelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterThresholdConfig;
import org.dromara.waterfee.meterRelation.domain.vo.MeterThresholdConfigVO;

import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:26
 **/

public interface WaterfeeMeterThresholdConfigMapper extends BaseMapper<WaterfeeMeterThresholdConfig> {

    List<MeterThresholdConfigVO> selectConfigList();

    WaterfeeMeterThresholdConfig selectByParentAndChild(@Param("parentId") Long parentId, @Param("childId") Long childId);

    // 新增方法：根据总表ID查询阈值配置
    WaterfeeMeterThresholdConfig selectByParentId(@Param("parentId") Long parentId);
}
