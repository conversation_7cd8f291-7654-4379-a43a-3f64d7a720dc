package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManagePenaltyAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManagePenaltyAdjustmentBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 违约金调整/减免Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManagePenaltyAdjustmentMapper extends BaseMapperPlus<WaterfeeChargeManagePenaltyAdjustment, WaterfeeChargeManagePenaltyAdjustmentVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManagePenaltyAdjustmentVo>> P selectVoPage(IPage<WaterfeeChargeManagePenaltyAdjustment> page, Wrapper<WaterfeeChargeManagePenaltyAdjustment> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询违约金调整/减免列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManagePenaltyAdjustmentVo> queryList(@Param("page") Page<WaterfeeChargeManagePenaltyAdjustment> page, @Param("query") WaterfeeChargeManagePenaltyAdjustmentBo query);

}
