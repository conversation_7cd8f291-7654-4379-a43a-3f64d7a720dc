package org.dromara.waterfee.income.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收入类型枚举
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Getter
@AllArgsConstructor
public enum IncomeTypeEnum {

    /**
     * 账单缴费
     */
    BILL_PAYMENT("bill_payment", "账单缴费"),

    /**
     * 预存充值
     */
    PRESTORE("prestore", "预存充值"),

    /**
     * 账单退费
     */
    REFUND("refund", "账单退费");

    private final String code;
    private final String info;

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static IncomeTypeEnum getByCode(String code) {
        for (IncomeTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 