package org.dromara.waterfee.template.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.template.domain.bo.WaterfeeTemplateBo;
import org.dromara.waterfee.template.domain.vo.WaterfeeTemplateVo;
import org.dromara.waterfee.template.enums.TemplateCategoryEnum;
import org.dromara.waterfee.template.enums.TemplateTypeEnum;
import org.dromara.waterfee.template.service.IWaterfeeTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模板管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/template")
public class WaterfeeTemplateController extends BaseController {

    private final IWaterfeeTemplateService waterfeeTemplateService;

    /**
     * 查询模板管理列表
     */
    @SaCheckPermission("waterfee:template:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeTemplateVo> list(WaterfeeTemplateBo bo, PageQuery pageQuery) {
        return waterfeeTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出模板管理列表
     */
    @SaCheckPermission("waterfee:template:export")
    @Log(title = "模板管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeTemplateBo bo, HttpServletResponse response) {
        List<WaterfeeTemplateVo> list = waterfeeTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "模板管理", WaterfeeTemplateVo.class, response);
    }

    /**
     * 获取模板管理详细信息
     *
     * @param templateId 模板管理主键
     */
    @SaCheckPermission("waterfee:template:query")
    @GetMapping("/{templateId}")
    public R<WaterfeeTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long templateId) {
        return R.ok(waterfeeTemplateService.queryById(templateId));
    }

    /**
     * 新增模板管理
     */
    @SaCheckPermission("waterfee:template:add")
    @Log(title = "模板管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeTemplateBo bo) {
        return toAjax(waterfeeTemplateService.insertByBo(bo));
    }

    /**
     * 修改模板管理
     */
    @SaCheckPermission("waterfee:template:edit")
    @Log(title = "模板管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeTemplateBo bo) {
        return toAjax(waterfeeTemplateService.updateByBo(bo));
    }

    /**
     * 启用模板
     */
    @SaCheckPermission("waterfee:template:enable")
    @Log(title = "启用模板", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{templateId}")
    public R<Void> enableTemplate(@NotNull(message = "模板ID不能为空")
                                  @PathVariable Long templateId) {
        return toAjax(waterfeeTemplateService.enableTemplate(templateId));
    }

    /**
     * 禁用模板
     */
    @SaCheckPermission("waterfee:template:disable")
    @Log(title = "禁用模板", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{templateId}")
    public R<Void> disableTemplate(@NotNull(message = "模板ID不能为空")
                                   @PathVariable Long templateId) {
        return toAjax(waterfeeTemplateService.disableTemplate(templateId));
    }

    /**
     * 获取启用的模板
     */
    @SaCheckPermission("waterfee:template:query")
    @GetMapping("/enabled")
    public R<WaterfeeTemplateVo> getEnabledTemplate(@RequestParam("templateType") String templateType,
                                                    @RequestParam(value = "templateCategory", required = false) String templateCategory) {
        WaterfeeTemplateVo template = waterfeeTemplateService.getEnabledTemplate(templateType, templateCategory);
        return R.ok(template);
    }

    /**
     * 删除模板管理
     *
     * @param templateIds 模板管理主键串
     */
    @SaCheckPermission("waterfee:template:remove")
    @Log(title = "模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] templateIds) {
        return toAjax(waterfeeTemplateService.deleteWithValidByIds(List.of(templateIds), true));
    }

    /**
     * 获取模板类型列表
     */
    @GetMapping("/types")
    public R<List<Map<String, String>>> getTemplateTypes() {
        return R.ok(TemplateTypeEnum.getAllTypes());
    }

    /**
     * 获取模板分类列表
     */
    @GetMapping("/categories")
    public R<List<Map<String, String>>> getTemplateCategories() {
        return R.ok(TemplateCategoryEnum.getAllCategories());
    }
}
