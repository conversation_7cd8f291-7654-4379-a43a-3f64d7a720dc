package org.dromara.waterfee.meterReading.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 抄表任务配置类
 * 用于配置抄表任务的各种性能参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "waterfee.meter-reading")
public class MeterReadingConfig {

    /**
     * 批处理配置
     */
    private BatchConfig batch = new BatchConfig();

    /**
     * 并发配置
     */
    private ConcurrencyConfig concurrency = new ConcurrencyConfig();

    /**
     * 性能监控配置
     */
    private PerformanceConfig performance = new PerformanceConfig();

    /**
     * 智能表配置
     */
    private IntelligentMeterConfig intelligentMeter = new IntelligentMeterConfig();

    @Data
    public static class BatchConfig {
        /**
         * 批处理大小
         */
        private int size = 500;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 批处理超时时间（毫秒）
         */
        private long timeoutMs = 30000;

        /**
         * 是否启用批量插入优化
         */
        private boolean enableBatchInsert = true;
    }

    @Data
    public static class ConcurrencyConfig {
        /**
         * 最大并发任务数
         */
        private int maxConcurrentTasks = 10;

        /**
         * 线程池核心大小
         */
        private int corePoolSize = Runtime.getRuntime().availableProcessors();

        /**
         * 线程池最大大小
         */
        private int maxPoolSize = Runtime.getRuntime().availableProcessors() * 2;

        /**
         * 线程池队列大小
         */
        private int queueCapacity = 1000;

        /**
         * 线程空闲时间（秒）
         */
        private int keepAliveSeconds = 60;
    }

    @Data
    public static class PerformanceConfig {
        /**
         * 慢任务阈值（毫秒）
         */
        private long slowTaskThresholdMs = 30000;

        /**
         * 高失败率阈值（百分比）
         */
        private double highFailRateThreshold = 10.0;

        /**
         * 性能监控数据保留数量
         */
        private int maxMonitorRecords = 1000;

        /**
         * 是否启用性能告警
         */
        private boolean enablePerformanceAlert = true;

        /**
         * 性能报告生成间隔（分钟）
         */
        private int reportIntervalMinutes = 60;
    }

    @Data
    public static class IntelligentMeterConfig {
        /**
         * 智能表读取超时时间（毫秒）
         */
        private long readTimeoutMs = 5000;

        /**
         * 智能表读取重试次数
         */
        private int maxRetries = 3;

        /**
         * 智能表读取失败后的默认值
         */
        private Double defaultValueOnFailure = null;

        /**
         * 是否启用智能表数据缓存
         */
        private boolean enableCache = true;

        /**
         * 缓存过期时间（分钟）
         */
        private int cacheExpirationMinutes = 5;
    }

    /**
     * 获取有效的批处理大小
     */
    public int getEffectiveBatchSize() {
        return Math.max(1, Math.min(batch.size, 2000)); // 限制在1-2000之间
    }

    /**
     * 获取有效的并发任务数
     */
    public int getEffectiveConcurrentTasks() {
        return Math.max(1, Math.min(concurrency.maxConcurrentTasks, 50)); // 限制在1-50之间
    }

    /**
     * 验证配置参数
     */
    public void validateConfig() {
        if (batch.size <= 0) {
            throw new IllegalArgumentException("批处理大小必须大于0");
        }
        if (concurrency.maxConcurrentTasks <= 0) {
            throw new IllegalArgumentException("最大并发任务数必须大于0");
        }
        if (performance.slowTaskThresholdMs <= 0) {
            throw new IllegalArgumentException("慢任务阈值必须大于0");
        }
        if (performance.highFailRateThreshold < 0 || performance.highFailRateThreshold > 100) {
            throw new IllegalArgumentException("高失败率阈值必须在0-100之间");
        }
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "抄表任务配置 - 批处理大小: %d, 最大并发: %d, 慢任务阈值: %dms, 高失败率阈值: %.1f%%",
            getEffectiveBatchSize(),
            getEffectiveConcurrentTasks(),
            performance.slowTaskThresholdMs,
            performance.highFailRateThreshold
        );
    }
}
