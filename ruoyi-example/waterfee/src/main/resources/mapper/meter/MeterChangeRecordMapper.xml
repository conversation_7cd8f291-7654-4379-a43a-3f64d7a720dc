<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meter.mapper.MeterChangeRecordMapper">

    <resultMap type="org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo" id="MeterChangeRecordVoResult">
        <result property="changeId" column="change_id"/>
        <result property="userId" column="user_id"/>
        <result property="userNo" column="user_no"/>
        <result property="userName" column="user_name"/>
        <result property="oldMeterNo" column="old_meter_no"/>
        <result property="newMeterNo" column="new_meter_no"/>
        <result property="changeType" column="change_type"/>
        <result property="changeReason" column="change_reason"/>
        <result property="changeTime" column="change_time"/>
        <result property="operator" column="operator"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditor" column="auditor"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        change_id,
        user_id,
        user_no,
        user_name,
        old_meter_no,
        new_meter_no,
        change_type,
        change_reason,
        change_time,
        operator,
        audit_status,
        audit_time,
        auditor,
        remark
    </sql>

    <!-- 查询最近一次换表记录 -->
    <select id="selectLastChangeRecord" parameterType="String" resultMap="MeterChangeRecordVoResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        waterfee_meter_change_record
        WHERE
        del_flag = '0'
        AND (old_meter_no = #{meterNo} OR new_meter_no = #{meterNo})
        ORDER BY
        change_time DESC, change_id DESC
        LIMIT 1
    </select>

    <!-- 如果需要自定义复杂查询，可以添加如下映射 -->
    <select id="selectByMeterNo" parameterType="String" resultMap="MeterChangeRecordVoResult">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        waterfee_meter_change_record USE INDEX(idx_old_meter_no, idx_new_meter_no)
        WHERE
        (old_meter_no = #{meterNo} OR new_meter_no = #{meterNo})
        AND del_flag = '0'
        ORDER BY
        change_time DESC, change_id DESC
    </select>

    <select id="getUserChangeTableCount" resultType="org.dromara.waterfee.statisticalReport.domain.UserChangeTableCountVO">
        select
            cr.create_time,
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as use_water_nature,
            cr.old_meter_reading,
            cr.new_meter_reading
        from waterfee_meter_change_record cr
            left join waterfee_user u on u.user_id = cr.user_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = u.use_water_nature
        where
            cr.del_flag = 0
        order by cr.create_time desc
    </select>
</mapper>
