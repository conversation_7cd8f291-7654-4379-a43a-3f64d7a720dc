<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.invoice.mapper.InvoiceRecordMapper">
    <resultMap id="InvoiceRecordResult" autoMapping="true" type="org.dromara.waterfee.invoice.domain.InvoiceRecord">
        <id property="invoiceId" column="invoice_id"/>
    </resultMap>

    <resultMap id="InvoiceRecordResultVo" autoMapping="true" type="org.dromara.waterfee.invoice.domain.vo.InvoiceRecordVo">
        <id property="invoiceId" column="invoice_id"/>
    </resultMap>

    <sql id="selectInvoiceRecordVo">
        select ir.invoice_id, ir.payment_detail_id, ir.serial_no, ir.invoice_status, ir.fail_cause, ir.pdf_url, ir.invoice_time, ir.invoice_code, ir.invoice_no, ir.remark, ir.tenant_id, ir.create_dept, ir.create_by, ir.create_time, ir.update_by, ir.update_time, ir.del_flag from invoice_record ir
    </sql>
    <select id="queryList" resultMap="InvoiceRecordResultVo">
        <include refid="selectInvoiceRecordVo"/>
        <where>
            <if test="query.paymentDetailId != null and query.paymentDetailId != ''"> and ir.payment_detail_id = #{query.paymentDetailId}</if>
            <if test="query.serialNo != null and query.serialNo != ''"> and ir.serial_no = #{query.serialNo}</if>
            <if test="query.invoiceStatus != null and query.invoiceStatus != ''"> and ir.invoice_status = #{query.invoiceStatus}</if>
            <if test="query.failCause != null and query.failCause != ''"> and ir.fail_cause = #{query.failCause}</if>
            <if test="query.pdfUrl != null and query.pdfUrl != ''"> and ir.pdf_url = #{query.pdfUrl}</if>
            <if test="query.invoiceTime != null"> and ir.invoice_time = #{query.invoiceTime}</if>
            <if test="query.invoiceCode != null and query.invoiceCode != ''"> and ir.invoice_code = #{query.invoiceCode}</if>
            <if test="query.invoiceNo != null and query.invoiceNo != ''"> and ir.invoice_no = #{query.invoiceNo}</if>
            and ir.del_flag = '0'
        </where>
    </select>
</mapper>
