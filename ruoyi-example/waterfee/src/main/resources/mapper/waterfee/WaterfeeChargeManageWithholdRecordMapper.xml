<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageWithholdRecordMapper">
    <resultMap id="WaterfeeChargeManageWithholdRecordResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageWithholdRecordResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdRecordVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageWithholdRecordVo">
        select wcmwr.id, wcmwr.user_id, wcmwr.amount, wcmwr.withhold_time, wcmwr.status, wcmwr.remark, wcmwr.tenant_id, wcmwr.create_by, wcmwr.create_time, wcmwr.update_by, wcmwr.update_time, wcmwr.del_flag from waterfee_charge_manage_withhold_record wcmwr
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageWithholdRecordResultVo">
        <include refid="selectWaterfeeChargeManageWithholdRecordVo"/>
        <where>
            <if test="query.userId != null"> and wcmwr.user_id = #{query.userId}</if>
            <if test="query.amount != null"> and wcmwr.amount = #{query.amount}</if>
            <if test="query.withholdTime != null"> and wcmwr.withhold_time = #{query.withholdTime}</if>
            <if test="query.status != null and query.status != ''"> and wcmwr.status = #{query.status}</if>
            and wcmwr.del_flag = '0'
        </where>
    </select>
</mapper>
