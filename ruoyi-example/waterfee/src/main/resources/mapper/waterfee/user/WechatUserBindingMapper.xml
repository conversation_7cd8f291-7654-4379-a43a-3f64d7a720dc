<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WechatUserBindingMapper">
    <resultMap id="WechatUserBindingResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WechatUserBinding">
        <id property="wechatUserId" column="wechat_user_id"/>
    </resultMap>

    <resultMap id="WechatUserBindingResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WechatUserBindingVo">
        <id property="wechatUserId" column="wechat_user_id"/>
    </resultMap>

    <sql id="selectWechatUserBindingVo">
        select wub.wechat_user_id, wub.openid, wub.user_no, wub.remark, wub.tenant_id, wub.create_dept, wub.create_by, wub.create_time, wub.update_by, wub.update_time, wub.del_flag from wechat_user_binding wub
    </sql>
    <select id="queryList" resultMap="WechatUserBindingResultVo">
        <include refid="selectWechatUserBindingVo"/>
        <where>
            <if test="query.openid != null and query.openid != ''"> and wub.openId = #{query.openid}</if>
            <if test="query.userNo != null and query.userNo != ''"> and wub.user_no = #{query.userNo}</if>
            and wub.del_flag = '0'
        </where>
    </select>

    <select id="queryBindingUserListByOpenid" resultMap="WechatUserBindingResultVo">
        select wub.wechat_user_id, wub.openid, wub.user_no, wub.remark, wub.tenant_id, wub.create_dept, wub.create_by, wub.create_time, wub.update_by, wub.update_time, wub.del_flag, wu.user_name, wu.address, wu.balance
        from wechat_user_binding wub
        left join waterfee_user wu on wub.user_no = wu.user_no
        where wub.del_flag = '0' and wub.openId = #{openid}
    </select>
</mapper>
