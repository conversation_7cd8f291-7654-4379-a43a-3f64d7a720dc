<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserDeactivateRecordMapper">
    <resultMap id="WaterfeeUserDeactivateRecordResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUserDeactivateRecord">
        <id property="deactivateId" column="deactivate_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserDeactivateRecordResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WaterfeeUserDeactivateRecordVo">
        <id property="deactivateId" column="deactivate_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserDeactivateRecordVo">
        select wudr.deactivate_id, wudr.user_id, wudr.deactivate_reason, wudr.cancellation_time, wudr.tenant_id, wudr.create_dept, wudr.create_time, wudr.create_by, wudr.update_time, wudr.update_by, wudr.del_flag from waterfee_user_deactivate_record wudr
    </sql>

    <select id="queryList" resultMap="WaterfeeUserDeactivateRecordResultVo">
        select wudr.deactivate_id, wudr.user_id, wudr.deactivate_reason, wudr.cancellation_time, wudr.tenant_id, wudr.create_dept, wudr.create_time, wudr.create_by, wudr.update_time, wudr.update_by, wudr.del_flag, wu.user_no, wu.meter_no, wu.user_name, wu.customer_nature,
        wu.use_water_nature, su.nick_name as create_by_user_name, wc.community_name
        from waterfee_user_deactivate_record wudr
        left join waterfee_user wu on wudr.user_id = wu.user_id
        left join sys_user su on wupcr.create_by = su.user_id
        left join waterfee_community wc on wu.community_id = wc.id
        <where>
            wudr.del_flag = '0'
            <if test="query.userNoOrUserName != null and query.userNoOrUserName != ''">
                and (wu.user_no like concat('%', #{query.userNoOrUserName}, '%')
                or wu.user_name like concat('%', #{query.userNoOrUserName}, '%'))
            </if>
            <if test="query.startTime != null">
                and DATE(wudr.create_time) &gt;= DATE(#{query.startTime})
            </if>
            <if test="query.endTime != null">
                and DATE(wudr.create_time) &lt;= DATE(#{query.endTime})
            </if>
        </where>
        order by wudr.create_time desc
    </select>
</mapper>
