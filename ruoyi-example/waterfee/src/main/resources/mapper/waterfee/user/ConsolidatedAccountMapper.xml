<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.ConsolidatedAccountMapper">
    <resultMap id="ConsolidatedAccountResult" autoMapping="true" type="org.dromara.waterfee.user.domain.ConsolidatedAccount">
        <id property="consolidatedAccountId" column="consolidated_account_id"/>
    </resultMap>

    <resultMap id="ConsolidatedAccountResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.ConsolidatedAccountVo">
        <id property="consolidatedAccountId" column="consolidated_account_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUser">
        <id property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectConsolidatedAccountVo">
        select ca.consolidated_account_id, ca.consolidated_account_no, ca.consolidated_account_name, ca.owner_name, ca.owner_phone, ca.tenant_id, ca.create_dept, ca.create_time, ca.create_by, ca.update_time, ca.update_by, ca.del_flag, ca.remark from consolidated_account ca
    </sql>
    <select id="queryList" resultMap="ConsolidatedAccountResultVo">
        <include refid="selectConsolidatedAccountVo"/>
        <where>
            <if test="query.consolidatedAccountNo != null and query.consolidatedAccountNo != ''"> and ca.consolidated_account_no = #{query.consolidatedAccountNo}</if>
            <if test="query.consolidatedAccountName != null and query.consolidatedAccountName != ''"> and ca.consolidated_account_name like concat(concat('%', #{query.consolidatedAccountName}), '%')</if>
            <if test="query.ownerName != null and query.ownerName != ''"> and ca.owner_name like concat(concat('%', #{query.ownerName}), '%')</if>
            <if test="query.ownerPhone != null and query.ownerPhone != ''"> and ca.owner_phone = #{query.ownerPhone}</if>
            and ca.del_flag = '0'
        </where>
    </select>

    <select id="selectUserListByConsolidatedAccountId" resultMap="WaterfeeUserResult">
        select wu.user_id, wu.user_no, wu.area_id, wu.community_id, wu.unit_room_number, wu.customer_nature,
        wu.use_water_nature, wu.use_water_number, wu.user_name, wu.phone_number, wu.certificate_type,
        wu.certificate_number, wu.user_status, wu.address, wu.email, wu.taxpayer_identification_number,
        wu.supply_date, wu.invoice_name, wu.invoice_type, wu.price_use_water_nature, wu.billing_method,
        wu.if_penalty, wu.penalty_type, wu.if_extra_charge, wu.extra_charge_type, wu.tenant_id,
        wu.create_dept, wu.create_time, wu.create_by, wu.update_time, wu.update_by, wu.del_flag, wu.audit_status,
        wu.meter_no
        from consolidated_account ca
        left join consolidated_account_item cai on ca.consolidated_account_id = cai.consolidated_account_id
        left join waterfee_user wu on cai.user_id = wu.user_id
        where ca.consolidated_account_id = #{consolidatedAccountId}
    </select>
</mapper>
