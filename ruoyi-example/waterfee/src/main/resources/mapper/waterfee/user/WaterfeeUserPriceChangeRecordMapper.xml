<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserPriceChangeRecordMapper">
    <resultMap id="WaterfeeUserPriceChangeRecordResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUserPriceChangeRecord">
        <id property="priceChangeId" column="price_change_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserPriceChangeRecordResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WaterfeeUserPriceChangeRecordVo">
        <id property="priceChangeId" column="price_change_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserPriceChangeRecordVo">
        select wupcr.price_change_id, wupcr.user_id, wupcr.before_price_use_water_nature, wupcr.before_billing_method, wupcr.before_if_penalty, wupcr.before_penalty_type, wupcr.before_if_extra_charge, wupcr.before_extra_charge_type, wupcr.after_price_use_water_nature, wupcr.after_billing_method, wupcr.after_if_penalty, wupcr.after_penalty_type, wupcr.after_if_extra_charge, wupcr.after_extra_charge_type, wupcr.tenant_id, wupcr.create_dept, wupcr.create_time, wupcr.create_by, wupcr.update_time, wupcr.update_by, wupcr.del_flag from waterfee_user_price_change_record wupcr
    </sql>

    <select id="queryList" resultMap="WaterfeeUserPriceChangeRecordResultVo">
        select wupcr.price_change_id, wupcr.user_id, wupcr.before_price_use_water_nature, wupcr.before_billing_method, wupcr.before_if_penalty, wupcr.before_penalty_type, wupcr.before_if_extra_charge, wupcr.before_extra_charge_type, wupcr.after_price_use_water_nature, wupcr.after_billing_method, wupcr.after_if_penalty, wupcr.after_penalty_type, wupcr.after_if_extra_charge, wupcr.after_extra_charge_type, wupcr.tenant_id, wupcr.create_dept, wupcr.create_time, wupcr.create_by, wupcr.update_time, wupcr.update_by, wupcr.del_flag, wu.user_no, wu.user_name, su.nick_name as create_by_user_name,
        wpc1.name as before_billing_method_name, wpc2.name as after_billing_method_name
        from waterfee_user_price_change_record wupcr
        left join waterfee_user wu on wupcr.user_id = wu.user_id
        left join sys_user su on wupcr.create_by = su.user_id
        left join waterfee_price_config wpc1 on wpc1.id = wupcr.before_billing_method
        left join waterfee_price_config wpc2 on wpc2.id = wupcr.after_billing_method
        <where>
            wupcr.del_flag = '0'
            <if test="query.userNoOrUserName != null and query.userNoOrUserName != ''">
                and (wu.user_no like concat('%', #{query.userNoOrUserName}, '%')
                or wu.user_name like concat('%', #{query.userNoOrUserName}, '%'))
            </if>
            <if test="query.startTime != null">
                and DATE(wupcr.create_time) &gt;= DATE(#{query.startTime})
            </if>
            <if test="query.endTime != null">
                and DATE(wupcr.create_time) &lt;= DATE(#{query.endTime})
            </if>
        </where>
        order by wupcr.create_time desc
    </select>
</mapper>
