<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserMapper">
    <resultMap id="WaterfeeUserResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUser">
        <id property="userId" column="user_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WaterfeeUserVo">
        <id property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserVo">
        select wu.user_id, wu.user_no, wu.area_id, wu.community_id, wu.unit_room_number, wu.customer_nature,
        wu.use_water_nature, wu.use_water_number, wu.user_name, wu.phone_number, wu.certificate_type,
        wu.certificate_number, wu.user_status, wu.address, wu.email, wu.taxpayer_identification_number,
        wu.supply_date, wu.invoice_name, wu.invoice_type, wu.price_use_water_nature, wu.billing_method,
        wu.if_penalty, wu.penalty_type, wu.if_extra_charge, wu.extra_charge_type, wu.tenant_id,
        wu.create_dept, wu.create_time, wu.create_by, wu.update_time, wu.update_by, wu.del_flag, wu.audit_status,
        wm.meter_no, wm.meter_book_id, mb.book_name, wc.community_name
        from waterfee_user wu
        left join waterfee_meter wm on wu.user_id = wm.user_id
        left join waterfee_meter_book mb on wm.meter_book_id = mb.id
        left join waterfee_community wc on wu.community_id = wc.id
    </sql>

    <select id="queryList" resultMap="WaterfeeUserResultVo">
        <include refid="selectWaterfeeUserVo"/>
        <where>
            <if test="query.userId != null">and wu.user_id = #{query.userId}</if>
            <if test="query.userNo != null and query.userNo != ''">and wu.user_no = #{query.userNo}</if>
            <if test="query.customerNature != null and query.customerNature != ''">and wu.customer_nature =
                #{query.customerNature}
            </if>
            <if test="query.useWaterNature != null and query.useWaterNature != ''">and wu.use_water_nature =
                #{query.useWaterNature}
            </if>
            <if test="query.userName != null and query.userName != ''">and wu.user_name like concat(concat('%',
                #{query.userName}), '%')
            </if>
            <if test="query.userStatus != null and query.userStatus != ''">and wu.user_status = #{query.userStatus}</if>
            <if test="query.auditStatus != null and query.auditStatus != ''">and wu.audit_status =
                #{query.auditStatus}
            </if>
            <if test="query.searchValue != null and query.searchValue != ''">
                and (
                wu.user_name like concat('%', #{query.searchValue}, '%')
                or wu.user_no like concat('%', #{query.searchValue}, '%')
                or wm.meter_no like concat('%', #{query.searchValue}, '%')
                )
            </if>
            <if test="query.ifSpecific != null and query.ifSpecific != ''">and wu.if_specific = #{query.ifSpecific}</if>

            and wu.del_flag = '0'
        </where>
        order by wu.create_time desc
    </select>


    <select id="selectUserByUserNameNu" resultMap="WaterfeeUserResult">
        select wu.user_id, wu.user_no, wu.user_name, wu.phone_number from waterfee_user wu
        <where>
            wu.del_flag = '0'
            <if test="keyword != null and keyword != ''">
                and (wu.user_no = #{keyword} or wu.user_name like concat(concat('%',#{keyword}), '%'))
            </if>
        </where>
        order by wu.create_time desc
    </select>

    <!-- 批量逻辑删除用户 -->
<!--    <update id="logicDeleteByIds">-->
<!--        <foreach collection="ids" item="id">-->
<!--            update waterfee_user set del_flag = #{id}-->
<!--            where user_id = #{id}-->
<!--        </foreach>-->
<!--    </update>-->

    <select id="getNatureStatisticsUser" resultType="org.dromara.waterfee.statisticalReport.domain.NatureStatisticsUserVO">
        select
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as use_water_nature,
            m.manufacturer
        from waterfee_user u
            left join waterfee_meter m on u.user_id = m.user_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = u.use_water_nature
        where
            u.user_status = 'normal' and u.del_flag = 0
            <if test="customerNature != null and customerNature != ''">
                and u.customer_nature = #{customerNature}
            </if>
        group by u.user_id
        order by u.create_time desc
    </select>

    <select id="getNewAccountUserDetails" resultType="org.dromara.waterfee.statisticalReport.domain.NewAccountUserDetailsVO">
        select
            u.create_time,
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as use_water_nature
        from waterfee_user u
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = u.use_water_nature
        where
            u.del_flag = 0
            <if test="createTime != null and createTime != ''">
                and EXTRACT(YEAR FROM u.create_time) = #{createTime}
            </if>
        order by u.create_time desc
    </select>
</mapper>
