<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.income.mapper.WaterfeeIncomeRecordMapper">

    <resultMap type="org.dromara.waterfee.income.domain.WaterfeeIncomeRecord" id="WaterfeeIncomeRecordResult">
        <id property="incomeId" column="income_id"/>
        <result property="incomeType" column="income_type"/>
        <result property="userNo" column="user_no"/>
        <result property="collectorName" column="collector_name"/>
        <result property="incomeTime" column="income_time"/>
        <result property="amount" column="amount"/>
        <result property="payMethod" column="pay_method"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 自定义查询，避免查询不存在的字段 -->
    <sql id="selectWaterfeeIncomeRecordVo">
        select income_id, income_type, user_no, collector_name, income_time, amount, pay_method, remark,
        del_flag, tenant_id, create_by, create_time, update_by, update_time
        from waterfee_income_record
    </sql>

    <select id="selectVoList" resultType="org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo">
        <include refid="selectWaterfeeIncomeRecordVo"/>
        ${ew.customSqlSegment}
    </select>

    <select id="selectVoPage" resultType="org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo">
        <include refid="selectWaterfeeIncomeRecordVo"/>
        ${ew.customSqlSegment}
    </select>

    <select id="selectVoById" parameterType="Long"
            resultType="org.dromara.waterfee.income.domain.vo.WaterfeeIncomeRecordVo">
        <include refid="selectWaterfeeIncomeRecordVo"/>
        where income_id = #{incomeId}
    </select>

    <select id="selectIncomeSummary" resultType="org.dromara.waterfee.income.domain.vo.WaterfeeIncomeSummaryVo">
        SELECT
        SUM(CASE WHEN income_type = 'bill_payment' THEN amount ELSE 0 END) AS billPaymentAmount,
        COUNT(CASE WHEN income_type = 'bill_payment' THEN 1 ELSE NULL END) AS billPaymentCount,

        SUM(CASE WHEN income_type = 'prestore' THEN amount ELSE 0 END) AS prestoreAmount,
        COUNT(CASE WHEN income_type = 'prestore' THEN 1 ELSE NULL END) AS prestoreCount,

        SUM(CASE WHEN income_type = 'refund' THEN amount ELSE 0 END) AS refundAmount,
        COUNT(CASE WHEN income_type = 'refund' THEN 1 ELSE NULL END) AS refundCount,

        (
        SUM(CASE WHEN income_type = 'bill_payment' THEN amount ELSE 0 END) +
        SUM(CASE WHEN income_type = 'prestore' THEN amount ELSE 0 END) -
        SUM(CASE WHEN income_type = 'refund' THEN amount ELSE 0 END)
        ) AS totalIncomeAmount,

        COUNT(*) AS totalTransactionCount
        FROM waterfee_income_record
        WHERE del_flag = '0'
    </select>

</mapper>
