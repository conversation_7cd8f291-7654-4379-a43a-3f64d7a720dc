<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.bill.mapper.PaymentDetailMapper">

    <select id="selectVoPage" resultType="org.dromara.waterfee.bill.domain.vo.PaymentDetailVo">
        SELECT
        pd.payment_detail_id AS paymentDetailId,
        pd.bill_id AS billId,
        pd.payment_amount AS paymentAmount,
        pd.payment_time AS paymentTime,
        pd.payment_method AS paymentMethod,
        pd.transaction_id AS transactionId,
        pd.payment_status AS paymentStatus,
        pd.remark,
        pd.create_time AS createTime,
        pd.income_time AS incomeTime,
        wu.user_no AS userNo,
        wu.user_name AS userName
        FROM
        waterfee_payment_detail pd
        LEFT JOIN waterfee_bills wb ON pd.bill_id = wb.bill_id
        LEFT JOIN waterfee_user wu ON wb.customer_id = wu.user_id
        <where>
            <if test="bo != null">
                <if test="bo.paymentStatus != null and bo.paymentStatus != ''">
                    AND pd.payment_status = #{bo.paymentStatus}
                </if>
                <if test="bo.paymentMethod != null and bo.paymentMethod != ''">
                    AND pd.payment_method = #{bo.paymentMethod}
                </if>
                <if test="bo.paymentTime != null">
                    AND pd.payment_time = #{bo.paymentTime}
                </if>
                <if test="bo.paymentAmount != null">
                    AND pd.payment_amount = #{bo.paymentAmount}
                </if>
                <!-- 你还可以根据 PaymentDetailBo 里其他字段添加查询条件 -->
            </if>
        </where>
    </select>

    <select id="getPaymentDetails" resultType="org.dromara.waterfee.statisticalReport.domain.PaymentDetailsVO">
        select
            p.payment_time,
            u.user_no,
            u.user_name,
            u.address,
            b.consumption_volume,
            b.water_bill_only,
            b.water_resource_tax,
            b.sewage_treatment_fee,
            p.payment_amount,
            p.toll_collector,
            p.payment_method
        from waterfee_payment_detail p
            left join waterfee_user u on u.user_id = p.user_id
            left join waterfee_bills b on b.bill_id = p.bill_id
        where
            p.del_flag = 0
            <if test="tollCollector != null and tollCollector != ''">
                and p.toll_collector = #{tollCollector}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and p.payment_method = #{paymentMethod}
            </if>
            <if test="startTime != null and startTime != ''">
                and p.payment_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and p.payment_time &lt;= #{endTime}
            </if>
        order by p.payment_time desc
    </select>

    <select id="getOnlinePaymentDetailSummary" resultType="org.dromara.waterfee.statisticalReport.domain.OnlinePaymentDetailSummaryVO">
        select
            u.user_no,
            u.user_name,
            u.address,
            b.consumption_volume,
            b.water_bill_only,
            b.water_resource_tax,
            b.sewage_treatment_fee,
            p.payment_amount,
            p.payment_time,
            p.transaction_id
        from waterfee_payment_detail p
            left join waterfee_user u on u.user_id = p.user_id
            left join waterfee_bills b on b.bill_id = p.bill_id
        where
            p.del_flag = 0
            <if test="paymentMethod != null and paymentMethod != ''">
                and p.payment_method = #{paymentMethod}
            </if>
        order by p.payment_time desc
    </select>
</mapper>
