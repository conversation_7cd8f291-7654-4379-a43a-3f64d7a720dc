<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManagePenaltyAdjustmentMapper">
    <resultMap id="WaterfeeChargeManagePenaltyAdjustmentResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManagePenaltyAdjustmentResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManagePenaltyAdjustmentVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManagePenaltyAdjustmentVo">
        select wcmpa.id, wcmpa.user_id, wcmpa.bill_id, wcmpa.original_penalty, wcmpa.adjusted_penalty, wcmpa.reason, wcmpa.remark, wcmpa.tenant_id, wcmpa.create_by, wcmpa.create_time, wcmpa.update_by, wcmpa.update_time, wcmpa.del_flag from waterfee_charge_manage_penalty_adjustment wcmpa
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManagePenaltyAdjustmentResultVo">
        <include refid="selectWaterfeeChargeManagePenaltyAdjustmentVo"/>
        <where>
            <if test="query.userId != null"> and wcmpa.user_id = #{query.userId}</if>
            <if test="query.billId != null"> and wcmpa.bill_id = #{query.billId}</if>
            <if test="query.originalPenalty != null"> and wcmpa.original_penalty = #{query.originalPenalty}</if>
            <if test="query.adjustedPenalty != null"> and wcmpa.adjusted_penalty = #{query.adjustedPenalty}</if>
            <if test="query.reason != null and query.reason != ''"> and wcmpa.reason = #{query.reason}</if>
            and wcmpa.del_flag = '0'
        </where>
    </select>
</mapper>
