<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageWithholdConfigMapper">
    <resultMap id="WaterfeeChargeManageWithholdConfigResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageWithholdConfigResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdConfigVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageWithholdConfigVo">
        select wcmwc.id, wcmwc.user_id, wcmwc.bank_account, wcmwc.bank_name, wcmwc.signed, wcmwc.sign_time, wcmwc.cancel_time, wcmwc.remark, wcmwc.tenant_id, wcmwc.create_by, wcmwc.create_time, wcmwc.update_by, wcmwc.update_time, wcmwc.del_flag from waterfee_charge_manage_withhold_config wcmwc
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageWithholdConfigResultVo">
        <include refid="selectWaterfeeChargeManageWithholdConfigVo"/>
        <where>
            <if test="query.userId != null"> and wcmwc.user_id = #{query.userId}</if>
            <if test="query.bankAccount != null and query.bankAccount != ''"> and wcmwc.bank_account = #{query.bankAccount}</if>
            <if test="query.bankName != null and query.bankName != ''"> and wcmwc.bank_name like concat(concat('%', #{query.bankName}), '%')</if>
            <if test="query.signed != null"> and wcmwc.signed = #{query.signed}</if>
            <if test="query.signTime != null"> and wcmwc.sign_time = #{query.signTime}</if>
            <if test="query.cancelTime != null"> and wcmwc.cancel_time = #{query.cancelTime}</if>
            and wcmwc.del_flag = '0'
        </where>
    </select>
</mapper>
