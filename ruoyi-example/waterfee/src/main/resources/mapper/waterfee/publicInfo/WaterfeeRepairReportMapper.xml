<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.repair.mapper.WaterfeeRepairReportMapper">
    <resultMap id="WaterfeeRepairReportResult" autoMapping="true" type="org.dromara.waterfee.repair.domain.WaterfeeRepairReport">
        <id property="repairId" column="repair_id"/>
    </resultMap>

    <resultMap id="WaterfeeRepairReportResultVo" autoMapping="true" type="org.dromara.waterfee.repair.domain.vo.WaterfeeRepairReportVo">
        <id property="repairId" column="repair_id"/>
    </resultMap>

    <sql id="selectWaterfeeRepairReportVo">
        select wrr.repair_id, wrr.report_content, wrr.reporter_name, wrr.contact_phone, wrr.report_time, wrr.create_dept, wrr.create_by, wrr.create_time, wrr.update_by, wrr.update_time, wrr.remark, wrr.tenant_id from waterfee_repair_report wrr
    </sql>
    <select id="queryList" resultMap="WaterfeeRepairReportResultVo">
        <include refid="selectWaterfeeRepairReportVo"/>
        <where>
            <if test="query.reportContent != null and query.reportContent != ''"> and wrr.report_content like concat(concat('%', #{query.reportContent}), '%')</if>
            <if test="query.reporterName != null and query.reporterName != ''"> and wrr.reporter_name like concat(concat('%', #{query.reporterName}), '%')</if>
            <if test="query.contactPhone != null and query.contactPhone != ''"> and wrr.contact_phone = #{query.contactPhone}</if>
            <if test="query.reportTime != null"> and wrr.report_time = #{query.reportTime}</if>
        </where>
    </select>
</mapper>
