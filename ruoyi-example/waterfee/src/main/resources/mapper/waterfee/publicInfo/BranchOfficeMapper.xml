<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.branch.mapper.BranchOfficeMapper">
    <resultMap id="BranchOfficeResult" autoMapping="true" type="org.dromara.waterfee.branch.domain.BranchOffice">
        <id property="branchId" column="branch_id"/>
    </resultMap>

    <resultMap id="BranchOfficeResultVo" autoMapping="true" type="org.dromara.waterfee.branch.domain.vo.BranchOfficeVo">
        <id property="branchId" column="branch_id"/>
    </resultMap>

    <sql id="selectBranchOfficeVo">
        select bo.branch_id, bo.branch_name, bo.address, bo.contact_phone, bo.lon, bo.lat, bo.create_dept, bo.create_by, bo.create_time, bo.update_by, bo.update_time, bo.remark, bo.tenant_id from branch_office bo
    </sql>
    <select id="queryList" resultMap="BranchOfficeResultVo">
        <include refid="selectBranchOfficeVo"/>
        <where>
            <if test="query.branchName != null and query.branchName != ''"> and bo.branch_name like concat(concat('%', #{query.branchName}), '%')</if>
            <if test="query.address != null and query.address != ''"> and bo.address like concat(concat('%', #{query.address}), '%')</if>
            <if test="query.contactPhone != null and query.contactPhone != ''"> and bo.contact_phone = #{query.contactPhone}</if>
        </where>
    </select>
</mapper>
