<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.complaint.mapper.ComplaintSuggestionMapper">
    <resultMap id="ComplaintSuggestionResult" autoMapping="true" type="org.dromara.waterfee.complaint.domain.ComplaintSuggestion">
        <id property="complaintSuggestionId" column="complaint_suggestion_id"/>
    </resultMap>

    <resultMap id="ComplaintSuggestionResultVo" autoMapping="true" type="org.dromara.waterfee.complaint.domain.vo.ComplaintSuggestionVo">
        <id property="complaintSuggestionId" column="complaint_suggestion_id"/>
    </resultMap>

    <sql id="selectComplaintSuggestionVo">
        select cs.complaint_suggestion_id, cs.submitter_name, cs.contact_phone, cs.content, cs.submit_time, cs.create_dept, cs.create_by, cs.create_time, cs.update_by, cs.update_time, cs.remark, cs.tenant_id from complaint_suggestion cs
    </sql>
    <select id="queryList" resultMap="ComplaintSuggestionResultVo">
        <include refid="selectComplaintSuggestionVo"/>
        <where>
            <if test="query.submitterName != null and query.submitterName != ''"> and cs.submitter_name like concat(concat('%', #{query.submitterName}), '%')</if>
            <if test="query.contactPhone != null and query.contactPhone != ''"> and cs.contact_phone = #{query.contactPhone}</if>
            <if test="query.content != null and query.content != ''"> and cs.content like concat(concat('%', #{query.content}), '%')</if>
            <if test="query.submitTime != null"> and cs.submit_time = #{query.submitTime}</if>
        </where>
    </select>
</mapper>
