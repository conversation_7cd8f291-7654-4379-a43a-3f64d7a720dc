<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.leak.mapper.WaterfeeLeakReportMapper">
    <resultMap id="WaterfeeLeakReportResult" autoMapping="true" type="org.dromara.waterfee.leak.domain.WaterfeeLeakReport">
        <id property="reportId" column="report_id"/>
    </resultMap>

    <resultMap id="WaterfeeLeakReportResultVo" autoMapping="true" type="org.dromara.waterfee.leak.domain.vo.WaterfeeLeakReportVo">
        <id property="reportId" column="report_id"/>
    </resultMap>

    <sql id="selectWaterfeeLeakReportVo">
        select wlr.report_id, wlr.reporter_name, wlr.reporter_phone, wlr.report_time, wlr.description, wlr.file, wlr.lon, wlr.lat, wlr.create_dept, wlr.create_by, wlr.create_time, wlr.update_by, wlr.update_time, wlr.remark, wlr.tenant_id from waterfee_leak_report wlr
    </sql>
    <select id="queryList" resultMap="WaterfeeLeakReportResultVo">
        <include refid="selectWaterfeeLeakReportVo"/>
        <where>
            <if test="query.reporterName != null and query.reporterName != ''"> and wlr.reporter_name like concat(concat('%', #{query.reporterName}), '%')</if>
            <if test="query.reporterPhone != null and query.reporterPhone != ''"> and wlr.reporter_phone = #{query.reporterPhone}</if>
            <if test="query.reportTime != null"> and wlr.report_time = #{query.reportTime}</if>
            <if test="query.description != null and query.description != ''"> and wlr.description like concat(concat('%', #{query.description}), '%')</if>
        </where>
    </select>
</mapper>
