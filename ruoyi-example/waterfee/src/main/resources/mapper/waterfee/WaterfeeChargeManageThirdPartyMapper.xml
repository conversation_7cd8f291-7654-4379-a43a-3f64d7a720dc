<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageThirdPartyMapper">
    <resultMap id="WaterfeeChargeManageThirdPartyResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageThirdPartyResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageThirdPartyVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageThirdPartyVo">
        select wcmtp.id, wcmtp.channel_code, wcmtp.local_order_no, wcmtp.third_order_no, wcmtp.amount, wcmtp.status, wcmtp.reconciliation_date, wcmtp.remark, wcmtp.tenant_id, wcmtp.create_by, wcmtp.create_time, wcmtp.update_by, wcmtp.update_time, wcmtp.del_flag from waterfee_charge_manage_third_party wcmtp
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageThirdPartyResultVo">
        <include refid="selectWaterfeeChargeManageThirdPartyVo"/>
        <where>
            <if test="query.channelCode != null and query.channelCode != ''"> and wcmtp.channel_code = #{query.channelCode}</if>
            <if test="query.localOrderNo != null and query.localOrderNo != ''"> and wcmtp.local_order_no = #{query.localOrderNo}</if>
            <if test="query.thirdOrderNo != null and query.thirdOrderNo != ''"> and wcmtp.third_order_no = #{query.thirdOrderNo}</if>
            <if test="query.amount != null"> and wcmtp.amount = #{query.amount}</if>
            <if test="query.status != null and query.status != ''"> and wcmtp.status = #{query.status}</if>
            <if test="query.reconciliationDate != null"> and wcmtp.reconciliation_date = #{query.reconciliationDate}</if>
            and wcmtp.del_flag = '0'
        </where>
    </select>
</mapper>
