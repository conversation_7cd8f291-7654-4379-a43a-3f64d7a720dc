<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.wechat.mapper.WechatDraftMapper">

    <resultMap type="org.dromara.waterfee.wechat.domain.vo.WechatDraftVo" id="WechatDraftResult">
        <result property="draftId"              column="draft_id"               />
        <result property="mediaId"              column="media_id"               />
        <result property="title"                column="title"                  />
        <result property="draftType"            column="draft_type"             />
        <result property="content"              column="content"                />
        <result property="thumbMediaId"         column="thumb_media_id"         />
        <result property="thumbUrl"             column="thumb_url"              />
        <result property="author"               column="author"                 />
        <result property="digest"               column="digest"                 />
<!--        <result property="showCoverPic"         column="show_cover_pic"         />-->
        <result property="contentSourceUrl"     column="content_source_url"     />
        <result property="needOpenComment"      column="need_open_comment"      />
        <result property="onlyFansCanComment"   column="only_fans_can_comment"  />
        <result property="draftStatus"          column="draft_status"           />
        <result property="articleStatus"        column="article_status"         />
        <result property="publishTime"          column="publish_time"           />
        <result property="articlePublishTime"   column="article_publish_time"   />
        <result property="createBy"             column="create_by"              />
        <result property="createTime"           column="create_time"            />
        <result property="updateBy"             column="update_by"              />
        <result property="updateTime"           column="update_time"            />
        <result property="remark"               column="remark"                 />
    </resultMap>

    <sql id="selectWechatDraftVo">
        select draft_id, tenant_id, media_id, title, draft_type, content, thumb_media_id, thumb_url, author, digest,
            content_source_url, need_open_comment, only_fans_can_comment,
               draft_status, article_status, publish_time, article_publish_time, create_by, create_time, update_by, update_time, remark
        from wechat_draft
    </sql>

<!--    <select id="selectList" parameterType="org.dromara.waterfee.wechat.domain.bo.WechatDraftBo" resultMap="WechatDraftResult">-->
<!--        <include refid="selectWechatDraftVo"/>-->
<!--        <where>-->
<!--            del_flag = '0'-->
<!--            <if test="title != null and title != ''">-->
<!--                AND title like concat('%', #{title}, '%')-->
<!--            </if>-->
<!--            <if test="author != null and author != ''">-->
<!--                AND author like concat('%', #{author}, '%')-->
<!--            </if>-->
<!--            <if test="draftStatus != null and draftStatus != ''">-->
<!--                AND draft_status = #{draftStatus}-->
<!--            </if>-->
<!--            <if test="articleStatus != null and articleStatus != ''">-->
<!--                AND article_status = #{articleStatus}-->
<!--            </if>-->
<!--            <if test="draftType != null and draftType != ''">-->
<!--                AND draft_type = #{draftType}-->
<!--            </if>-->
<!--            <if test="params.beginTime != null and params.beginTime != ''">-->
<!--                AND create_time &gt;= #{params.beginTime}-->
<!--            </if>-->
<!--            <if test="params.endTime != null and params.endTime != ''">-->
<!--                AND create_time &lt;= #{params.endTime}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by create_time desc-->
<!--    </select>-->

</mapper>
