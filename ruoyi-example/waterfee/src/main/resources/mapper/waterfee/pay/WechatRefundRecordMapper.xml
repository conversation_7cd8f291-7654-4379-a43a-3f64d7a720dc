<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.pay.mapper.WechatRefundRecordMapper">
    <resultMap id="WechatRefundRecordResult" autoMapping="true" type="org.dromara.waterfee.pay.domain.WechatRefundRecord">
        <id property="refundId" column="refund_id"/>
    </resultMap>

    <resultMap id="WechatRefundRecordResultVo" autoMapping="true" type="org.dromara.waterfee.pay.domain.vo.WechatRefundRecordVo">
        <id property="refundId" column="refund_id"/>
    </resultMap>

    <sql id="selectWechatRefundRecordVo">
        select wrr.refund_id, wrr.payment_detail_id, wrr.wechat_refund_id, wrr.refund_status, wrr.success_time, wrr.user_received_account, wrr.transaction_id, wrr.payment_amount, wrr.payment_refund, wrr.payer_amount, wrr.payer_refund, wrr.remark, wrr.tenant_id, wrr.create_dept, wrr.create_by, wrr.create_time, wrr.update_by, wrr.update_time, wrr.del_flag from wechat_refund_record wrr
    </sql>
    <select id="queryList" resultMap="WechatRefundRecordResultVo">
        <include refid="selectWechatRefundRecordVo"/>
        <where>
            <if test="query.paymentDetailId != null and query.paymentDetailId != ''"> and wrr.payment_detail_id = #{query.paymentDetailId}</if>
            <if test="query.wechatRefundId != null and query.wechatRefundId != ''"> and wrr.wechat_refund_id = #{query.wechatRefundId}</if>
            <if test="query.refundStatus != null and query.refundStatus != ''"> and wrr.refund_status = #{query.refundStatus}</if>
            <if test="query.successTime != null"> and wrr.success_time = #{query.successTime}</if>
            <if test="query.userReceivedAccount != null and query.userReceivedAccount != ''"> and wrr.user_received_account = #{query.userReceivedAccount}</if>
            <if test="query.transactionId != null and query.transactionId != ''"> and wrr.transaction_id = #{query.transactionId}</if>
            <if test="query.paymentAmount != null"> and wrr.payment_amount = #{query.paymentAmount}</if>
            <if test="query.paymentRefund != null"> and wrr.payment_refund = #{query.paymentRefund}</if>
            <if test="query.payerAmount != null"> and wrr.payer_amount = #{query.payerAmount}</if>
            <if test="query.payerRefund != null"> and wrr.payer_refund = #{query.payerRefund}</if>
            and wrr.del_flag = '0'
        </where>
    </select>
</mapper>
