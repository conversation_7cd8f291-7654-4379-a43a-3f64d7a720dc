<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.MeterReadingBatchMapper">

    <!-- 批量插入抄表记录 -->
    <insert id="batchInsertReadingRecords" parameterType="java.util.List">
        INSERT INTO waterfee_meter_reading_record (
        meter_no, meter_book_id, meter_type, last_reading, last_reading_time,
        current_reading, old_meter_stop_reading, water_usage, reading_time,
        task_id, source_type, is_audited, manual_id, tenant_id, create_by,
        create_time, update_by, update_time, del_flag, is_pending, pending_reason, remark
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.meterNo},
            #{record.meterBookId},
            #{record.meterType},
            #{record.lastReading},
            #{record.lastReadingTime},
            #{record.currentReading},
            #{record.oldMeterStopReading},
            #{record.waterUsage},
            #{record.readingTime},
            #{record.taskId},
            #{record.sourceType},
            #{record.isAudited},
            #{record.manualId},
            #{record.tenantId},
            #{record.createBy},
            #{record.createTime},
            #{record.updateBy},
            #{record.updateTime},
            #{record.delFlag},
            #{record.isPending},
            #{record.pendingReason},
            #{record.remark}
            )
        </foreach>
    </insert>

    <!-- 批量更新抄表记录状态 -->
    <update id="batchUpdateRecordStatus">
        UPDATE waterfee_meter_reading_record
        SET is_audited = #{status},
        update_time = NOW()
        WHERE record_id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量逻辑删除抄表记录 -->
    <update id="batchDeleteRecords">
        UPDATE waterfee_meter_reading_record
        SET del_flag = '1',
        update_time = NOW()
        WHERE record_id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量获取水表信息的结果映射 -->
    <resultMap id="WaterfeeMeterVoResult" type="org.dromara.waterfee.meter.domain.WaterfeeMeter">
        <id property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterType" column="meter_type"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="meterCategory" column="meter_category"/>
        <result property="meterClassification" column="meter_classification"/>
        <result property="measurementPurpose" column="measurement_purpose"/>
        <result property="caliber" column="caliber"/>
        <result property="accuracy" column="accuracy"/>
        <result property="initialReading" column="initial_reading"/>
        <result property="installDate" column="install_date"/>
        <result property="businessAreaId" column="business_area_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="sortNo" column="sort_no"/>
        <result property="installAddress" column="install_address"/>
        <result property="meterRatio" column="meter_ratio"/>
        <result property="communicationMode" column="communication_mode"/>
        <result property="valveControl" column="valve_control"/>
        <result property="imei" column="imei"/>
        <result property="imsi" column="imsi"/>
        <result property="iotPlatform" column="iot_platform"/>
        <result property="prepaid" column="prepaid"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <!-- 批量获取抄表记录的结果映射 -->
    <resultMap id="MeterReadingRecordResult" type="org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord">
        <id property="recordId" column="record_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="meterType" column="meter_type"/>
        <result property="lastReading" column="last_reading"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="currentReading" column="current_reading"/>
        <result property="oldMeterStopReading" column="old_meter_stop_reading"/>
        <result property="waterUsage" column="water_usage"/>
        <result property="readingTime" column="reading_time"/>
        <result property="taskId" column="task_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="isAudited" column="is_audited"/>
        <result property="manualId" column="manual_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="isPending" column="is_pending"/>
        <result property="pendingReason" column="pending_reason"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 优化的批量获取水表信息查询 -->
    <select id="batchSelectMetersByNos" resultMap="WaterfeeMeterVoResult">
        SELECT
        m.meter_id, m.meter_no, m.meter_type, m.manufacturer, m.meter_category,
        m.meter_classification, m.measurement_purpose, m.caliber, m.accuracy,
        m.initial_reading, m.install_date, m.business_area_id, m.meter_book_id,
        m.sort_no, m.install_address, m.meter_ratio, m.communication_mode,
        m.valve_control, m.imei, m.imsi, m.iot_platform, m.prepaid, m.user_id,
        m.create_by, m.create_time, m.update_by, m.update_time, m.remark,
        m.del_flag, m.tenant_id
        FROM waterfee_meter m
        WHERE m.meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND m.del_flag = '0'
        ORDER BY m.sort_no ASC, m.meter_id ASC
    </select>

    <!-- 优化的批量获取最新抄表记录查询 -->
    <select id="batchSelectLatestRecords" resultMap="MeterReadingRecordResult">
        SELECT r1.*
        FROM waterfee_meter_reading_record r1
        INNER JOIN (
        SELECT meter_no, MAX(reading_time) as max_time, MAX(record_id) as max_id
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND del_flag = '0'
        GROUP BY meter_no
        ) r2 ON r1.meter_no = r2.meter_no
        AND r1.reading_time = r2.max_time
        AND r1.record_id = r2.max_id
        WHERE r1.del_flag = '0'
        ORDER BY r1.meter_no
    </select>

    <!-- 批量获取最近N次抄表记录 -->
    <select id="batchGetRecentRecords" resultMap="MeterReadingRecordResult">
        SELECT * FROM (
        SELECT r.*,
        ROW_NUMBER() OVER (PARTITION BY r.meter_no ORDER BY r.reading_time DESC, r.record_id DESC) as rn
        FROM waterfee_meter_reading_record r
        WHERE r.meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND r.del_flag = '0'
        ) ranked
        WHERE ranked.rn &lt;= #{limit}
        ORDER BY ranked.meter_no, ranked.reading_time DESC
    </select>

    <!-- 批量检查季度重复记录 -->
    <select id="batchCheckQuarterDuplicates" resultType="java.lang.String">
        SELECT DISTINCT meter_no
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND task_id = #{taskId}
        AND YEAR(reading_time) = #{year}
        AND QUARTER(reading_time) = #{quarter}
        AND del_flag = '0'
    </select>

    <!-- 批量获取水表统计信息 -->
    <select id="batchGetMeterStatistics" resultType="java.util.Map">
        SELECT
            meter_book_id,
            COUNT(*) as total_count,
            SUM(CASE WHEN meter_type = 1 THEN 1 ELSE 0 END) as mechanical_count,
            SUM(CASE WHEN meter_type = 2 THEN 1 ELSE 0 END) as intelligent_count
        FROM waterfee_meter
        WHERE meter_book_id IN
        <foreach collection="meterBookIds" item="bookId" open="(" separator="," close=")">
            #{bookId}
        </foreach>
        AND del_flag = '0'
        GROUP BY meter_book_id
    </select>

    <!-- 获取指定时间范围内的抄表记录统计 -->
    <select id="getReadingStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN is_audited = '1' THEN 1 ELSE 0 END) as audited_count,
            SUM(CASE WHEN is_pending = '1' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN current_reading IS NOT NULL THEN 1 ELSE 0 END) as completed_count,
            AVG(CASE WHEN water_usage IS NOT NULL THEN water_usage ELSE 0 END) as avg_water_usage
        FROM waterfee_meter_reading_record
        WHERE reading_time BETWEEN #{startTime} AND #{endTime}
        AND del_flag = '0'
    </select>

    <!-- 获取任务执行统计 -->
    <select id="batchGetTaskStatistics" resultType="java.util.Map">
        SELECT
            task_id,
            COUNT(*) as total_records,
            SUM(CASE WHEN current_reading IS NOT NULL THEN 1 ELSE 0 END) as completed_records,
            SUM(CASE WHEN is_audited = '1' THEN 1 ELSE 0 END) as audited_records,
            MIN(reading_time) as first_reading_time,
            MAX(reading_time) as last_reading_time
        FROM waterfee_meter_reading_record
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND del_flag = '0'
        GROUP BY task_id
    </select>

    <!-- 批量检查水表是否存在未完成的抄表记录 -->
    <select id="batchCheckIncompleteRecords" resultType="java.lang.String">
        SELECT DISTINCT meter_no
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND current_reading IS NULL
        AND is_audited != '1'
        AND del_flag = '0'
    </select>

    <!-- 性能优化的索引建议 -->
    <!--
    建议创建以下索引来优化查询性能：

    1. 水表表索引：
    CREATE INDEX idx_meter_no ON waterfee_meter(meter_no);
    CREATE INDEX idx_meter_book_id ON waterfee_meter(meter_book_id);
    CREATE INDEX idx_meter_type ON waterfee_meter(meter_type);

    2. 抄表记录表索引：
    CREATE INDEX idx_meter_no_time ON waterfee_meter_reading_record(meter_no, reading_time DESC);
    CREATE INDEX idx_task_id ON waterfee_meter_reading_record(task_id);
    CREATE INDEX idx_reading_time ON waterfee_meter_reading_record(reading_time);
    CREATE INDEX idx_meter_no_task_quarter ON waterfee_meter_reading_record(meter_no, task_id, reading_time);

    3. 复合索引：
    CREATE INDEX idx_meter_del_flag ON waterfee_meter(del_flag, meter_book_id);
    CREATE INDEX idx_record_del_flag ON waterfee_meter_reading_record(del_flag, meter_no, reading_time);
    -->

</mapper>
