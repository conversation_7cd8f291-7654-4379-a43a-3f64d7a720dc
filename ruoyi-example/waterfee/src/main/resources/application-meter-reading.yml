# 抄表任务性能优化配置
waterfee:
  meter-reading:
    # 批处理配置
    batch:
      # 批处理大小，建议根据内存和数据库性能调整
      # 小批次：更好的内存控制，但可能增加处理次数
      # 大批次：更高的处理效率，但占用更多内存
      size: 500

      # 最大重试次数
      max-retries: 3

      # 批处理超时时间（毫秒）
      timeout-ms: 30000

      # 是否启用批量插入优化
      # 启用后会使用批量SQL插入，显著提升数据库写入性能
      enable-batch-insert: true

    # 并发配置
    concurrency:
      # 最大并发任务数
      # 建议设置为CPU核心数的1-2倍，避免过度并发导致资源竞争
      max-concurrent-tasks: 10

      # 线程池核心大小
      # 建议设置为CPU核心数
      core-pool-size: 8

      # 线程池最大大小
      # 建议设置为CPU核心数的2倍
      max-pool-size: 16

      # 线程池队列大小
      # 用于缓冲待处理的任务
      queue-capacity: 1000

      # 线程空闲时间（秒）
      # 超过此时间的空闲线程将被回收
      keep-alive-seconds: 60

    # 性能监控配置
    performance:
      # 慢任务阈值（毫秒）
      # 超过此时间的任务将被标记为慢任务并告警
      slow-task-threshold-ms: 30000

      # 高失败率阈值（百分比）
      # 超过此失败率的任务将触发告警
      high-fail-rate-threshold: 10.0

      # 性能监控数据保留数量
      # 用于控制内存使用，避免监控数据过多
      max-monitor-records: 1000

      # 是否启用性能告警
      # 启用后会在检测到性能问题时输出告警日志
      enable-performance-alert: true

      # 性能报告生成间隔（分钟）
      # 定期生成性能报告的时间间隔
      report-interval-minutes: 60

      # 性能报告文件配置
      reports:
        # 是否启用文件输出
        enable-file-output: true

        # 报告文件根目录
        base-directory: "logs/performance-reports"

        # 文件保留天数
        retention-days: 30

        # 是否启用文件压缩（超过指定天数的文件）
        enable-compression: true
        compression-after-days: 7

        # 单个报告文件最大大小（MB）
        max-file-size-mb: 10

  # 账单处理优化配置
  bill-processing:
    # 批处理配置
    batch:
      # 账单批处理大小
      bill-batch-size: 200

      # 支付批处理大小
      payment-batch-size: 100

      # 最大重试次数
      max-retries: 3

      # 批处理超时时间（毫秒）
      timeout-ms: 60000

      # 是否启用批量优化
      enable-batch-optimization: true

    # 并发配置
    concurrency:
      # 最大并发批次数
      max-concurrent-batches: 5

      # 账单处理线程池大小
      bill-processing-thread-pool-size: 8

      # 支付处理线程池大小
      payment-processing-thread-pool-size: 4

      # 线程池队列大小
      queue-capacity: 1000

      # 线程空闲时间（秒）
      keep-alive-seconds: 60

    # 性能配置
    performance:
      # 是否启用性能监控
      enable-performance-monitoring: true

      # 慢处理阈值（毫秒）
      slow-processing-threshold-ms: 10000

      # 性能统计窗口大小
      performance-window-size: 100

      # 是否启用缓存
      enable-caching: true

      # 缓存过期时间（分钟）
      cache-expiration-minutes: 10

    # 支付配置
    payment:
      # 是否启用自动支付
      enable-auto-payment: true

      # 支付超时时间（毫秒）
      payment-timeout-ms: 30000

      # 最大支付重试次数
      max-payment-retries: 2

      # 支付失败后的等待时间（毫秒）
      payment-retry-delay-ms: 1000

      # 单次最大支付金额
      max-payment-amount: 10000.0

      # 是否检查客户余额
      check-customer-balance: true

    # 智能表配置
    intelligent-meter:
      # 智能表读取超时时间（毫秒）
      read-timeout-ms: 5000

      # 智能表读取重试次数
      max-retries: 3

      # 智能表读取失败后的默认值
      # 设置为null表示读取失败时不设置默认值
      default-value-on-failure: null

      # 是否启用智能表数据缓存
      # 启用后会缓存智能表读取结果，减少重复读取
      enable-cache: true

      # 缓存过期时间（分钟）
      cache-expiration-minutes: 5

# Spring异步配置
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 8
        # 最大线程数
        max-size: 16
        # 队列容量
        queue-capacity: 1000
        # 线程名前缀
        thread-name-prefix: "meter-reading-"
        # 线程空闲时间
        keep-alive: 60s
      # 关闭时等待任务完成
      shutdown:
        await-termination: true
        await-termination-period: 60s
  datasource:
    hikari:
      # 最小空闲连接数
      minimum-idle: 10
      # 最大连接池大小
      maximum-pool-size: 50
      # 连接超时时间
      connection-timeout: 30000
      # 空闲连接最大存活时间
      idle-timeout: 600000
      # 连接最大存活时间
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1

# 数据库连接池配置（针对高并发优化）

# 日志配置
logging:
  level:
    # 抄表相关日志级别
    org.dromara.waterfee.meterReading: INFO
    # 性能监控日志级别
    org.dromara.waterfee.meterReading.service.MeterReadingPerformanceMonitor: INFO
    # 批量处理日志级别
    org.dromara.waterfee.meterReading.service.MeterReadingBatchService: INFO
    # SQL日志级别（生产环境建议设置为WARN）
    org.dromara.waterfee.meterReading.mapper: DEBUG

# MyBatis配置优化
mybatis-plus:
  configuration:
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: true
    # 积极延迟加载
    aggressive-lazy-loading: false
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 监控端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

waterfee:
  meter-reading:
    batch:
      size: 100  # 开发环境使用较小批次便于调试
    concurrency:
      max-concurrent-tasks: 2  # 开发环境降低并发数
    performance:
      enable-performance-alert: true
      slow-task-threshold-ms: 10000  # 开发环境降低慢任务阈值

logging:
  level:
    org.dromara.waterfee.meterReading: DEBUG
    org.dromara.waterfee.meterReading.mapper: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

waterfee:
  meter-reading:
    batch:
      size: 1000  # 生产环境使用较大批次提高效率
    concurrency:
      max-concurrent-tasks: 20  # 生产环境提高并发数
    performance:
      enable-performance-alert: true
      slow-task-threshold-ms: 60000  # 生产环境提高慢任务阈值

logging:
  level:
    org.dromara.waterfee.meterReading: INFO
    org.dromara.waterfee.meterReading.mapper: WARN

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test

waterfee:
  meter-reading:
    batch:
      size: 50   # 测试环境使用小批次
    concurrency:
      max-concurrent-tasks: 1  # 测试环境使用单线程便于测试
    performance:
      enable-performance-alert: false  # 测试环境关闭告警

logging:
  level:
    org.dromara.waterfee.meterReading: DEBUG
