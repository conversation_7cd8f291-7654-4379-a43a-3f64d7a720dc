package org.dromara.waterfee.api;

import org.dromara.waterfee.api.domain.WaterfeeMeterDTO;
import org.dromara.waterfee.api.domain.WaterfeeUserDTO;

/**
 * 用水用户远程服务接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface RemoteWaterfeeUserService {

    /**
     * 根据用户ID查询用水用户信息
     *
     * @param userId 用户ID
     * @param source 调用来源
     * @return 用水用户信息
     */
    WaterfeeUserDTO getWaterfeeUserById(Long userId, String source);

    /**
     * 更新用水用户信息
     *
     * @param waterfeeUser 用水用户信息
     * @param source 调用来源
     * @return 是否更新成功
     */
    Boolean updateWaterfeeUser(WaterfeeUserDTO waterfeeUser, String source);

    /**
     * 删除用水用户
     *
     * @param userId 用户ID
     * @param source 调用来源
     * @return 是否删除成功
     */
    Boolean deleteWaterfeeUser(Long userId, String source);

    /**
     * 工作流 - 新增用水用户基本信息
     *
     * @param userDTO 用水用户基本信息
     * @param source 调用来源
     * @return 新增后的用水用户信息
     */
    WaterfeeUserDTO addUserBasicInfo(WaterfeeUserDTO userDTO, String source);

    /**
     * 工作流 - 用水用户关联水表
     *
     * @param userId 用户ID
     * @param meterNo 水表编号
     * @param source 调用来源
     * @return 是否关联成功
     */
    Boolean bindUserMeter(Long userId, String meterNo, String source);

    /**
     * 工作流 - 设定用水用户价格信息
     *
     * @param userDTO 用水用户价格信息
     * @param source 调用来源
     * @return 是否设置成功
     */
    Boolean updateUserPriceInfo(WaterfeeUserDTO userDTO, String source);
}
